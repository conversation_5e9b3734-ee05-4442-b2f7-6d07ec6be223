# IoTeX Logo Loading Animation

基于IoTeX项目logo制作的精美Lottie加载动画，简洁生动，适用于各种页面加载场景。

## 🎨 动画特色

### 基础版本 (`lottie_logo_loading.json`)
- **主圆形脉冲效果**: 85%-100%缩放动画，营造呼吸感
- **渐变色旋转**: 青色到紫色的渐变色围绕圆形旋转
- **中心圆点闪烁**: 透明度40%-100%变化，增加节奏感
- **装饰元素轨道运动**: 小圆形围绕主体360度旋转

### 增强版本 (`lottie_logo_loading_enhanced.json`)
- **外环虚线旋转**: 添加外围虚线环，反向旋转增加层次感
- **更丰富的颜色变化**: 4个关键帧的渐变色变化
- **中心圆点缩放**: 结合透明度和缩放动画
- **更流畅的动画曲线**: 使用贝塞尔曲线优化动画效果

## 📁 文件结构

```
app/src/main/res/raw/
├── lottie_logo_loading.json          # 基础版本动画
└── lottie_logo_loading_enhanced.json # 增强版本动画

app/src/main/res/layout/
└── dialog_logo_loading.xml           # 加载对话框布局

app/src/main/java/io/iotex/iopay/
├── wallet/dialog/LogoLoadingDialog.kt # 加载对话框类
├── util/LoadingUtils.kt               # 加载工具类
└── demo/LoadingDemoActivity.kt        # 演示Activity
```

## 🚀 使用方法

### 1. 基础使用

```kotlin
// 显示默认加载动画
LoadingUtils.showLoading(context)

// 显示自定义文本
LoadingUtils.showLoading(context, "正在加载数据...")

// 隐藏加载动画
LoadingUtils.hideLoading()
```

### 2. 对话框使用

```kotlin
val dialog = LogoLoadingDialog.Builder(context)
    .setLoadingText("Loading...")
    .setCancelable(true)
    .setCancelOutside(true)
    .create()

dialog.show()
```

### 3. 在布局中直接使用

```xml
<com.airbnb.lottie.LottieAnimationView
    android:layout_width="80dp"
    android:layout_height="80dp"
    app:lottie_autoPlay="true"
    app:lottie_loop="true"
    app:lottie_rawRes="@raw/lottie_logo_loading" />
```

### 4. 特定场景使用

```kotlin
// 网络请求加载
LoadingUtils.showNetworkLoading(context)

// 钱包操作加载
LoadingUtils.showWalletLoading(context)

// 交易处理加载
LoadingUtils.showTransactionLoading(context)

// 数据同步加载
LoadingUtils.showSyncLoading(context)
```

## 🎯 动画参数

| 参数 | 基础版本 | 增强版本 | 说明 |
|------|----------|----------|------|
| 帧率 | 30fps | 30fps | 流畅的动画效果 |
| 时长 | 4秒 | 4秒 | 完整循环时间 |
| 尺寸 | 200x200 | 200x200 | 适配各种屏幕 |
| 循环 | 无缝循环 | 无缝循环 | 持续播放 |

## 🎨 设计元素

### 颜色方案
- **主渐变**: `#44FFB2` → `#617AFF` → `#855EFF`
- **描边色**: `#575757` (深灰)
- **中心点**: `#343439` (深色)
- **装饰元素**: `#FFFFFF` (白色)

### 动画效果
1. **缩放动画**: 使用贝塞尔曲线实现自然的呼吸效果
2. **旋转动画**: 360度连续旋转，营造动感
3. **透明度动画**: 闪烁效果增加视觉吸引力
4. **渐变动画**: 颜色流动效果更加生动

## 📱 适用场景

- ✅ 应用启动页面
- ✅ 网络请求等待
- ✅ 数据加载过程
- ✅ 文件上传下载
- ✅ 钱包操作确认
- ✅ 交易处理等待
- ✅ 页面切换过渡

## 🔧 自定义配置

### 修改动画速度
```kotlin
lottieAnimationView.speed = 1.5f // 1.5倍速播放
```

### 修改循环次数
```kotlin
lottieAnimationView.repeatCount = 3 // 播放3次后停止
```

### 动态控制播放
```kotlin
lottieAnimationView.playAnimation() // 开始播放
lottieAnimationView.pauseAnimation() // 暂停播放
lottieAnimationView.resumeAnimation() // 恢复播放
```

## 📋 注意事项

1. **性能优化**: Lottie动画在低端设备上可能影响性能，建议在必要时使用
2. **内存管理**: 及时释放不需要的动画资源
3. **网络环境**: 确保在网络较差时也能正常显示加载状态
4. **用户体验**: 避免加载时间过长，适时提供取消选项

## 🎉 演示

运行 `LoadingDemoActivity` 查看完整的动画效果演示。

---

**制作说明**: 这个动画基于IoTeX项目的logo设计，保持了品牌一致性的同时，添加了现代化的动画效果，让用户在等待过程中也能感受到品牌的专业性和创新性。
