package io.iotex.iopay

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.webkit.URLUtil
import android.widget.FrameLayout
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.facebook.react.modules.core.DefaultHardwareBackBtnHandler
import com.google.android.gms.common.GoogleApiAvailabilityLight
import com.google.android.material.bottomnavigation.BottomNavigationItemView
import com.google.android.material.bottomnavigation.BottomNavigationMenuView
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.machinefi.lockscreen.LockAuthHelper
import com.machinefi.lockscreen.PFSecurityUtils
import com.machinefi.walletconnect2.WC2Helper
import com.machinefi.walletconnect2.WalletConnectStatusEvent
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.dapp.DiscoverFragment
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivityMainMenuBinding
import io.iotex.iopay.home.BinoAiFragment
import io.iotex.iopay.home.DepinFragment
import io.iotex.iopay.home.HomeFragment
import io.iotex.iopay.home.SwapHomeFragment
import io.iotex.iopay.home.item.HomeFragmentPager
import io.iotex.iopay.meta.ui.GeoHomeActivity
import io.iotex.iopay.reactnative.ReactNativeFragment
import io.iotex.iopay.reactnative.ReactScene
import io.iotex.iopay.record.ActionRecordFragment
import io.iotex.iopay.record.ActionRecordViewModel
import io.iotex.iopay.support.eventbus.BinoAiPageEvent
import io.iotex.iopay.support.eventbus.GiftPageEvent
import io.iotex.iopay.support.eventbus.GiftUploadEvent
import io.iotex.iopay.support.eventbus.MainPageEvent
import io.iotex.iopay.support.eventbus.NewsRedDotEvent
import io.iotex.iopay.util.ANDROID_KEY_STORE
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.Constant.IOPAY_OPEN_W3BSTREAM_PAGE
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseParam
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IOTX_KEYSTORE_ALIAS
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.IoPayConstant.Companion.PUSH_HASH
import io.iotex.iopay.util.IoPayConstant.Companion.PUSH_TYPE
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.DeepLinkActivity
import io.iotex.iopay.wallet.home.WalletVisitorFragment
import io.iotex.iopay.xapp.XAppsActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity
import java.security.KeyPairGenerator
import java.security.KeyStore

class MainActivity :
    BaseBindActivity<BaseViewModel, ActivityMainMenuBinding>(R.layout.activity_main_menu),
    DefaultHardwareBackBtnHandler {

    companion object {
        const val KEY_SHOW_LOCK = "key_show_lock"
        const val KEY_IS_VISITOR = "key_is_visitor"

        fun startActivity(context: Context, showLock: Boolean = false, isVisitor: Boolean = false) {
            val intent = Intent(context, MainActivity::class.java)
            intent.putExtra(KEY_SHOW_LOCK, showLock)
            intent.putExtra(KEY_IS_VISITOR, isVisitor)
            context.startActivity(intent)
        }

        fun getIsVisitor(callback:(isVisitor:Boolean)->Unit){
            MainScope().launch {
                val count = withContext(Dispatchers.IO) {
                    AppDatabase.getInstance(Utils.getApp()).walletDao().count()
                }
                callback.invoke(count == 0)
            }
        }
    }

    private var showLock: Boolean = true
    private var isVisitor: Boolean = false
    private var wc2: Boolean = false
    private var pushHash: String = ""
    private var pushType: String = ""

    private var viewPagerAdapter: FragmentStateAdapter? = null
    private var exitTime: Long = 0

    // from other app to login with our wallet
    private var intentUri: Uri? = null
    private var loginPluginNext: String? = null

    private val mActionRecordViewModel by lazy {
        ViewModelProvider(this)[ActionRecordViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val fragments = mutableListOf<Fragment>()
    private val walletFragment by lazy {
        WalletVisitorFragment.newInstance()
    }

    private val walletDetailFragment by lazy {
        HomeFragment()
    }

    private val binoAiFragment by lazy {
        BinoAiFragment.newInstance()
    }

    private val depinFragment by lazy {
        DepinFragment.newInstance()
    }

    private val stakeFragment by lazy {
        ReactNativeFragment.newInstance(ReactScene.StakeRoot.name,showStatusBar = true)
    }

    private val discoverFragment by lazy {
        DiscoverFragment()
    }

    private val swapFragment by lazy {
        SwapHomeFragment()
    }

    override fun initView() {
        EventBus.getDefault().register(this)
        BarUtils.setStatusBarColor(
            this@MainActivity,
            ColorUtils.getColor(R.color.transparent)
        )
        val passed = checkFirstTimeLaunch()
        if (!passed) {
            return
        }

        if (!BuildConfig.AUTO_UPDATE_APK) {
            GoogleApiAvailabilityLight.getInstance().isGooglePlayServicesAvailable(this)
        }

        isVisitor = intent?.getBooleanExtra(KEY_IS_VISITOR, false) ?: false

        addFragment()

        initViewPager()

        backgroundInit(intent)
    }

    private fun initViewPager() {
        viewPagerAdapter = HomeFragmentPager(this, fragments)
        mBinding.homeViewPager.adapter = viewPagerAdapter
        mBinding.homeViewPager.isUserInputEnabled = false
        mBinding.homeViewPager.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {

            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                var tabName = ""
                when (position) {
                    0 -> tabName = FireBaseParam.HOME_TAB_WALLET
                    1 -> tabName = FireBaseParam.HOME_TAB_DEPINSCAN
                    2 -> tabName = FireBaseParam.HOME_TAB_BROWSER
                    3 -> tabName = FireBaseParam.HOME_TAB_EXPLORE
                    4 -> tabName = FireBaseParam.HOME_TAB_SETTING
                }
                val bundle = Bundle()
                bundle.putString(FireBaseParam.HOME_TAB, tabName)
                if (viewPagerAdapter?.itemCount == 3) {
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_VISITOR_MENU, bundle)
                } else {
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_MENU, bundle)
                }
                if (position == 4) {
                    toggleRedDot(false, 4)
                }
                if (position == 1) {
                    mActionRecordViewModel.getRecordByApi()
                }
                mBinding.bottomNavigationBar.checkItem(position)
            }

        })

        mBinding.bottomNavigationBar.setOnItemSelectedListener {
            mBinding.homeViewPager.setCurrentItem(it, false)
        }

        AppDatabase.getInstance(this).notificationDao().unreadCount().observe(this) { count ->
            mBinding.bottomNavigationBar.badgeItem(4)
        }
    }

    fun isSwapPage(): Boolean {
        return swapFragment.isResumed
    }

    @SuppressLint("RestrictedApi")
    private fun toggleRedDot(display: Boolean, tabIndex: Int) {
        val menuView = mBinding.bottomNavigationBar.getChildAt(0)
        if (menuView !is BottomNavigationMenuView) return
        val itemView = menuView.getChildAt(tabIndex) as? BottomNavigationItemView ?: return
        val dotView = itemView.getChildAt(itemView.childCount - 1)
        if (display) {
            if (dotView.tag == "dot") {
                dotView.setVisible()
            } else {
                val dot = View(this).apply {
                    setBackgroundResource(R.drawable.shape_circle_e53737)
                    val dotParams = FrameLayout.LayoutParams(6.dp2px(), 6.dp2px())
                    dotParams.gravity = Gravity.CENTER
                    dotParams.marginStart = 12.dp2px()
                    dotParams.topMargin = -(17.dp2px())
                    this.layoutParams = dotParams
                    tag = "dot"
                }
                itemView.addView(dot)
            }
        } else {
            if (dotView.tag == "dot") {
                itemView.removeView(dotView)
            }
        }
    }

    override fun onBackPressed() {
        if (System.currentTimeMillis() - exitTime > Constant.TIME_EXIT_INTERVAL) {
            ToastUtils.showShort(resources.getString(R.string.exit_app_tip))
            exitTime = System.currentTimeMillis()
        } else {
            ActivityUtils.finishAllActivities()
            android.os.Process.killProcess(android.os.Process.myPid())
            super.onBackPressed()
        }
    }

    private fun addFragment() {
        mBinding.bottomNavigationBar.setVisitor(isVisitor)
        if (isVisitor) {
            fragments.clear()
            fragments.add(walletFragment)
            if (UserStore.getSwitchBinoAi()) {
                fragments.add(binoAiFragment)
            } else {
                fragments.add(depinFragment)
            }
            fragments.add(discoverFragment)
            mBinding.homeViewPager.offscreenPageLimit = 3
        } else {
            mBinding.bottomNavigationBar.setVisitor(false)
            fragments.clear()
            fragments.add(walletDetailFragment)
            fragments.add(depinFragment)
            fragments.add(swapFragment)
            if (UserStore.getSwitchBinoAi()) {
                fragments.add(binoAiFragment)
            } else {
                fragments.add(stakeFragment)
            }
            fragments.add(discoverFragment)
            mBinding.homeViewPager.offscreenPageLimit = 5
        }
        viewPagerAdapter?.notifyDataSetChanged()
    }

    private fun backgroundInit(intent: Intent?) {
        if (isVisitor) {
            showLock = intent?.getBooleanExtra(KEY_SHOW_LOCK, false) ?: false
            if (!PFSecurityUtils.isCreatePinCode() || showLock) {
                LockAuthHelper.showAuthLock(this@MainActivity, false) {
                    dispatchAction(intent)
                    onAuthSuccess()
                }
            } else {
                dispatchAction(intent)
            }
        }
    }

    private fun onAuthSuccess() {
        if (!loginPluginNext.isNullOrBlank()) {
            val intent = Intent(this@MainActivity, DeepLinkActivity::class.java)
            intent.putExtra("nonce", intentUri?.getQueryParameter("nonce"))
            intent.putExtra("next", intentUri?.getQueryParameter("next"))
            intent.putExtra("source", intentUri?.getQueryParameter("source"))
            intent.putExtra("appName", intentUri?.getQueryParameter("appName"))
            startActivity(intent)
        }
        if (!TextUtils.isEmpty(pushHash)) {
            WalletHelper.gotoExplorer(pushHash)
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        backgroundInit(intent)
    }

    private fun dispatchAction(i: Intent?) {
        intentUri = i?.data
        if (intentUri?.isHierarchical == true) {
            loginPluginNext = intentUri?.getQueryParameter(Config.PARAM_BACK_URL)
        }
        pushHash = i?.getStringExtra(PUSH_HASH) ?: ""
        pushType = i?.getStringExtra(PUSH_TYPE) ?: ""
        var action: String? = null
        var url: String? = null
        if (WC2Helper.isValidWc2Bridge(i?.dataString)) {
            action = Constant.DEEPLINK_ACTION_WALLET_CONNECT
            url = i?.dataString
        } else if (i?.data?.isHierarchical == true) {
            action = i.data?.getQueryParameter(IoPayConstant.ACTION_TYPE)
            url = i.data?.getQueryParameter(IoPayConstant.WEB_URL)
        }
        when (i?.data.toString()) {
            IOPAY_OPEN_W3BSTREAM_PAGE -> {
                startActivity<GeoHomeActivity>()
            }
        }
        if (action.isNullOrBlank() || url.isNullOrBlank()) return
        when (action) {
            Constant.DEEPLINK_ACTION_WALLET_CONNECT -> {
                wc2 = true
                WC2Helper.pair(url, "", "")
            }

            Constant.DEEPLINK_ACTION_WEB -> {
                parseUrlToWebView(url)
            }

            Constant.DEEPLINK_ACTION_STAKE -> {
                SchemeUtil.goto(this, SchemeUtil.SCHEME_STAKE_PAGE)
            }
        }
    }

    private fun parseUrlToWebView(url: String) {
        val intent = Intent(this, XAppsActivity::class.java)
        intent.putExtra(IoPayConstant.BROWSER_URL, url)
        intent.putExtra(IoPayConstant.X_APP_TITLE, url)
        if (URLUtil.isValidUrl(url)) {
            runCatching {
                val uri = Uri.parse(url)
                intent.putExtra(IoPayConstant.X_APP_TITLE, uri.host)
            }
        }
        startActivity(intent)
    }

    override fun invokeDefaultOnBackPressed() {
        onBackPressed()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNewsRedDotEvent(event: NewsRedDotEvent) {
        if (mBinding.homeViewPager.currentItem == 4) return
        toggleRedDot(true, 4)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGiftUploadEvent(event: GiftUploadEvent) {
        if (mBinding.homeViewPager.currentItem == 4) return
        toggleRedDot(true, 4)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMainPageEvent(event: MainPageEvent) {
        mBinding.bottomNavigationBar.checkItem(event.page)
        mBinding.homeViewPager.setCurrentItem(event.page, false)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGiftPageEvent(event: GiftPageEvent) {
        kotlin.runCatching {
            val index = if (event.isVisitor) 2 else 4
            mBinding.bottomNavigationBar.checkItem(index)
            mBinding.homeViewPager.setCurrentItem(index, false)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBinoAiPageEvent(event: BinoAiPageEvent) {
        kotlin.runCatching {
            val index = if (isVisitor) 1 else 3
            mBinding.bottomNavigationBar.checkItem(index)
            mBinding.homeViewPager.setCurrentItem(index, false)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletConnectStatusEvent(event: WalletConnectStatusEvent) {
        if (event.connected && wc2) {
            ToastUtils.showShort(getString(R.string.go_back_to_the_dapp_and_proceed))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    private fun checkFirstTimeLaunch(): Boolean {
        val previouslyStarted =
            SPUtils.getInstance().getBoolean(getString(R.string.pref_previously_started), false)
        if (!previouslyStarted) {
            try {
                val keyStore = KeyStore.getInstance(ANDROID_KEY_STORE)
                keyStore.load(null)
                if (!keyStore.containsAlias(IOTX_KEYSTORE_ALIAS)) {
                    val keyGenerator = KeyPairGenerator.getInstance(
                        KeyProperties.KEY_ALGORITHM_RSA,
                        ANDROID_KEY_STORE
                    )
                    val builder = KeyGenParameterSpec.Builder(
                        IOTX_KEYSTORE_ALIAS,
                        KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
                    )
                        .setDigests(KeyProperties.DIGEST_SHA256, KeyProperties.DIGEST_SHA512)
                        .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_RSA_OAEP)
                    keyGenerator.initialize(builder.build())
                    keyGenerator.generateKeyPair()
                }
                SPUtils.getInstance().put(getString(R.string.pref_previously_started), true)
            } catch (e: Exception) {
                FirebaseCrashlytics.getInstance().recordException(e)
                AlertDialog.Builder(this)
                    .setTitle("Error first launching")
                    .setMessage(getString(R.string.action_error, e.message))
                    .setPositiveButton(R.string.cancel) { dialog, _ ->
                        dialog.dismiss()
                    }
                    .setOnDismissListener { finish() }
                    .show()
            }
        }

        return true
    }
}