package io.iotex.iopay

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.walletconnect2.WC2Config
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.home.SwapActivity
import io.iotex.iopay.reactnative.ReactNativeActivity
import io.iotex.iopay.reactnative.ReactScene
import io.iotex.iopay.repo.GiftRepo
import io.iotex.iopay.setting.InviteFriendActivity
import io.iotex.iopay.support.eventbus.BinoAiPageEvent
import io.iotex.iopay.support.eventbus.DAppPageEvent
import io.iotex.iopay.support.eventbus.GiftPageEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.TransferActivity
import io.iotex.iopay.xapp.XAppsActivity
import io.iotex.iopay.wallet.add.WalletAddActivity
import io.iotex.iopay.wallet.dialog.NoteDialog
import io.iotex.iopay.wallet.dialog.UpdaterDialog
import io.iotex.iopay.xapp.trust.AddChainsUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus

object SchemeUtil {
    private const val SCHEME_SWAP_PAGE = "iopay://swap"
    private const val SCHEME_BUY_PAGE = "iopay://buy"
    private const val SCHEME_SEND_PAGE = "iopay://send"
    private const val SCHEME_DAPP_PAGE = "iopay://dapp"
    private const val SCHEME_INVITE_PAGE = "iopay://invite_friend"
    const val SCHEME_STAKE_PAGE = "iopay://stake"
    private const val SCHEME_UPDATE_APP = "iopay://update_app"
    private const val SCHEME_ADD_WALLET = "iopay://add_wallet"
    private const val SCHEME_SIGN_IN = "iopay://sign_in"
    private const val SCHEME_HOME_DEPIN = "iopay://home_depin"
    private const val SCHEME_GIFT_CENTER = "iopay://gift_center"
    private const val SCHEME_OPEN_WEB = "iopay://io.iotex.iopay/open"
    private const val SCHEME_TAB_BINOAI = "iopay://tab/binoai"
    private var updaterDialog: UpdaterDialog? = null
    fun goto(context: Context, page: String) {
        if (page.startsWith(SCHEME_OPEN_WEB)) {
            val url = Uri.parse(page).getQueryParameter("url")
            val intent = Intent(context, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, url)
            context.startActivity(intent)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_GIFT_CENTER_BANNER)
            return
        }

        if (page.startsWith(SCHEME_SWAP_PAGE)) {
            val chainId = Uri.parse(page).getQueryParameter("chainId")
            val from = Uri.parse(page).getQueryParameter("from")
            val to = Uri.parse(page).getQueryParameter("to")
            SwapActivity.startActivity(
                context, chainId?.toIntOrNull() ?: UserStore.getChainId(),
                from, to
            )
            return
        }
        if (page.startsWith(SCHEME_BUY_PAGE)) {
            val chainId = Uri.parse(page).getQueryParameter("chainId")
            val intent = Intent(context, ReactNativeActivity::class.java)
            intent.putExtra(
                ReactNativeActivity.REACT_COMPONENT_NAME,
                ReactScene.BuyPage.name
            )
            if(!chainId.isNullOrEmpty())intent.putExtra("_chain_id", chainId)
            context.startActivity(intent)
            return
        }
        if (page.startsWith(SCHEME_SEND_PAGE)) {
            val chainId = Uri.parse(page).getQueryParameter("chainId")?.toIntOrNull()
            val intent = Intent(context, TransferActivity::class.java)
            checkChainId(chainId){
                context.startActivity(intent)
            }
            return
        }

        when (page) {
            SCHEME_DAPP_PAGE -> {
                EventBus.getDefault().post(DAppPageEvent())
            }

            SCHEME_TAB_BINOAI -> {
                EventBus.getDefault().post(BinoAiPageEvent())
            }

            SCHEME_HOME_DEPIN -> {
                val url = if (UserStore.isDarkTheme()) {
                    "${Config.DEPIN_SCAN_WEB_URL}?theme=dark"
                } else {
                    "${Config.DEPIN_SCAN_WEB_URL}?theme=light"
                }
                val intent = Intent(context, XAppsActivity::class.java)
                intent.putExtra(IoPayConstant.BROWSER_URL, url)
                context.startActivity(intent)
            }

            SCHEME_GIFT_CENTER -> {
                isVisitor {
                    EventBus.getDefault().post(GiftPageEvent(it))
                }
            }

            SCHEME_SIGN_IN -> {
                giftSignInTask()
            }

            SCHEME_INVITE_PAGE -> {
                val intent = Intent(context, InviteFriendActivity::class.java)
                context.startActivity(intent)
            }

            SCHEME_ADD_WALLET -> {
                val intent = Intent(context, WalletAddActivity::class.java)
                context.startActivity(intent)
            }

            SCHEME_STAKE_PAGE -> {
                if (isValueChain() && isValueBucket()) {
                    val intent = Intent(context, ReactNativeActivity::class.java)
                    intent.putExtra(
                        ReactNativeActivity.REACT_COMPONENT_NAME,
                        ReactScene.StakeRoot.name
                    )
                    context.startActivity(intent)
                }
            }

            SCHEME_UPDATE_APP -> {
                GiftRepo().fetchAppVersion {
                    val update = AppUtils.getAppVersionCode() < it.target_version_code()
                    if (!update) {
                        ToastUtils.showShort(R.string.iopay_is_already_the_latest_version)
                        return@fetchAppVersion
                    }
                    if (updaterDialog == null) {
                        updaterDialog = UpdaterDialog(it)
                            .apply {
                                onDismiss = {
                                    updaterDialog = null
                                }
                            }
                        (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let { activity ->
                            updaterDialog?.show(
                                activity.supportFragmentManager,
                                System.currentTimeMillis().toString()
                            )
                        }
                    }
                }
            }
            else ->{
                val intent = Intent(context, XAppsActivity::class.java)
                intent.putExtra(IoPayConstant.BROWSER_URL, page)
                context.startActivity(intent)
            }
        }
    }

    private fun giftSignInTask() {
        CoroutineScope(Dispatchers.IO).launch {
            val pointTaskEntry = GiftRepo().hasSignInTask()
            if (pointTaskEntry?.released == true) {
                val address = WalletHelper.getCurWallet()?.address ?: return@launch
                val result = GiftRepo().updateEvent(pointTaskEntry.id, address)
                if (result?.ok == true) {
                    val bundle = Bundle()
                    val addressEvm = Constant.currentWallet?.address ?: ""
                    bundle.putString("address", addressEvm)
                    bundle.putString("device_id", DeviceUtils.getUniqueDeviceId())
                    bundle.putString("time", DateTimeUtils.formatYMD(System.currentTimeMillis()))
                    FireBaseUtil.logFireBase(FireBaseEvent.POINT_TASK_SIGN_IN, bundle)
                }
            }
        }
    }

    private fun isVisitor(callback: (isVisitor: Boolean) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            val count = AppDatabase.getInstance(Utils.getApp()).walletDao().count()
            MainScope().launch {
                callback.invoke(count == 0)
            }
        }
    }

    private fun isValueChain(): Boolean {
        if (!WalletHelper.isIoTexNetWork()) {
            ToastUtils.showShort(R.string.please_switch_our_network_to_iotex)
            return false
        }
        if (Constant.currentWallet?.isWatch == true || Constant.currentWallet?.isSolanaPrivateWallet() == true) {
            ToastUtils.showShort(R.string.the_current_wallet_is_not_support)
            return false
        }
        return true
    }

    private fun isValueBucket(): Boolean {
        val show = UserStore.getBucketDialog()
        if (show) {
            val activity = ActivityUtils.getTopActivity() as? AppCompatActivity
            activity?.let {
                NoteDialog().apply {
                    message = Utils.getApp().getString(R.string.please_update_the_iopay)
                }.show(activity.supportFragmentManager, NoteDialog::class.java.name)
            }
            return false
        } else {
            return true
        }
    }

    fun checkChainId(chainId:Int?,back:()->Unit){
        if (chainId != null && chainId != WalletHelper.getCurChainId()) {
            MainScope().launch {
                val network = withContext(Dispatchers.IO) {
                    AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                        .queryRPCNetworkByChainId(chainId)
                } ?: return@launch
                AddChainsUtil.showSwitchNetworkDialog(
                    WC2Config.IOPAY_LOGO,
                    WC2Config.IOPAY_URL,
                    network
                ) {
                    if (!it) return@showSwitchNetworkDialog
                    back.invoke()
                }
            }
        } else {
            back.invoke()
        }
    }
}