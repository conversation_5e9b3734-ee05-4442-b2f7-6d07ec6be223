package io.iotex.iopay.api

import io.iotex.iopay.data.bean.DAppListResult
import io.iotex.iopay.data.bean.NetworkListResult
import io.iotex.iopay.data.bean.TokenListResult
import retrofit2.http.GET
import retrofit2.http.QueryMap

interface IoPayRestApi {

    @GET("dapp_list_latest_v2")
    suspend fun dAppList(): DAppListResult

    @GET("network_list")
    suspend fun networkList(): NetworkListResult

    @GET("token_list_v4")
    suspend fun tokenList(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): TokenListResult
}