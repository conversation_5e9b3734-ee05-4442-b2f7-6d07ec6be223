package io.iotex.iopay.api

import io.iotex.iopay.data.bean.TokenListResult.SourceToken
import io.iotex.iopay.data.bean.TokenPrice
import okhttp3.ResponseBody
import retrofit2.http.*

interface StockApi {

    @GET("iopay/queryKlineData")
    suspend fun requestStock(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): ResponseBody

    @GET("iopay/queryQuestions")
    suspend fun requestQuestion(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): List<String>

    @GET("iopay/queryTokenPrice")
    suspend fun queryTokenPrice(@Query("id") id: String): TokenPrice

    @GET("iopay/queryTokenList")
    suspend fun queryTokenList(@Query("platform") platform: String): List<SourceToken>?

}