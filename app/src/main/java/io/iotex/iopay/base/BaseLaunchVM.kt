package io.iotex.iopay.base

import android.app.Application
import androidx.lifecycle.LifecycleOwner
import com.google.firebase.crashlytics.FirebaseCrashlytics
import io.iotex.base.BaseViewModel
import io.iotex.base.ILifecycle
import kotlinx.coroutines.*

open class BaseLaunchVM(application: Application) : BaseViewModel(application), ILifecycle,
    CoroutineScope by CoroutineScope(Dispatchers.IO) {

    protected var singJob: Job? = null
    private var launchJobs = ArrayList<Job>()

    override fun onDestroy(owner: LifecycleOwner) {
        launchJobs.forEach {
            kotlin.runCatching {
                if (!it.isCancelled) it.cancel()
            }
        }
        super.onDestroy(owner)
    }

    fun cancelAllJob(){
        launchJobs.iterator().forEach {
            kotlin.runCatching {
                if (!it.isCancelled) it.cancel()
            }
        }
        kotlin.runCatching {
            if (singJob?.isCancelled == false) singJob?.cancel()
        }
    }

    fun addLaunchNoCancel(
        showLoading: Boolean = false,
        onError: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ) {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            exception.printStackTrace()
            if (showLoading) iView?.hideLoading()
            onError?.invoke(exception)
            this.cancel()
            FirebaseCrashlytics.getInstance().recordException(exception)
        }
        launch(SupervisorJob() + exceptionHandler) {
            if (showLoading) iView?.showLoading()
            block.invoke(this)
            if (showLoading) iView?.hideLoading()
        }
    }

    fun addLaunch(
        showLoading: Boolean = false,
        onError: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ) {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            exception.printStackTrace()
            if (showLoading) iView?.hideLoading()
            onError?.invoke(exception)
            this.cancel()
            FirebaseCrashlytics.getInstance().recordException(exception)
        }
        val job = launch(SupervisorJob() + exceptionHandler) {
            if (showLoading) iView?.showLoading()
            block.invoke(this)
            if (showLoading) iView?.hideLoading()
        }
        launchJobs.add(job)
    }

    fun addLaunchSingle(
        showLoading: Boolean = false,
        onError: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ) {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            exception.printStackTrace()
            if (showLoading) iView?.hideLoading()
            onError?.invoke(exception)
            this.cancel()
            FirebaseCrashlytics.getInstance().recordException(exception)
        }
        if (singJob?.isCancelled == false) singJob?.cancel()
        singJob = launch(SupervisorJob() + exceptionHandler) {
            if (showLoading) iView?.showLoading()
            block.invoke(this)
            if (showLoading) iView?.hideLoading()
        }
    }
}