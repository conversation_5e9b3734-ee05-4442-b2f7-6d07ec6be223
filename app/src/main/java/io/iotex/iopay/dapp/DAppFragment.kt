package io.iotex.iopay.dapp

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import io.iotex.api.DAppCategoriesQuery
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.dapp.dialog.DAppTabsDialog
import io.iotex.iopay.dapp.viewmodel.DiscoverViewModel
import io.iotex.iopay.databinding.FragmentDappBinding
import io.iotex.iopay.databinding.ViewTabDappBinding
import io.iotex.iopay.dapp.item.VpFragmentPager
import io.iotex.iopay.support.eventbus.RefreshDAppEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.wallet.qrcode.IoScanQRCodeActivity
import io.iotex.iopay.xapp.SearchXAppsActivity
import org.greenrobot.eventbus.EventBus

class DAppFragment :
    BaseBindFragment<DiscoverViewModel, FragmentDappBinding>(R.layout.fragment_dapp) {

    var currentPosition = 0

    override fun initView() {
        mBinding.rlSearch.setOnClickListener {
            val intent = Intent(activity, SearchXAppsActivity::class.java)
            startActivity(intent)
            FireBaseUtil.logFireBase("action_browser_search_page_click_input")
        }

        mBinding.ivScan.setOnClickListener {
            IoScanQRCodeActivity.startActivity(requireContext())
        }

        mBinding.ivTabsAll.setOnClickListener {
            mViewModel.discoverCategoryDApps.value?.let { list ->
                DAppTabsDialog(list[mBinding.tabs.selectedTabPosition], list)
                    .apply {
                        onItemClick = {
                            mBinding.viewPager.currentItem = it
                        }
                    }
                    .show(childFragmentManager, System.currentTimeMillis().toString())
            }
            FireBaseUtil.logFireBase("action_browser_menu_all")
        }

        mBinding.smartRefresh.setOnRefreshListener {
            mViewModel.fetchDApps(true)
        }
    }

    override fun initData() {
        mViewModel.loadDAppsCategory()
        mViewModel.discoverCategoryDApps.observe(this) {
            initCategoryTab(it)
        }

        mViewModel.loadDApps()
        mViewModel.discoverDApps.observe(this) {
            mBinding.smartRefresh.finishRefresh()
            EventBus.getDefault().post(RefreshDAppEvent())
        }
    }

    private fun initCategoryTab(tabs: List<DAppCategoriesQuery.Dapp_category>) {
        val mFragments = mutableListOf<Fragment>()
        tabs.forEach { item ->
            if (item.name() == "Home") {
                mFragments.add(DAppHotFragment.newInstance())
            } else {
                mFragments.add(DAppCategoryFragment.newInstance(item.name(),item.category_banner()))
            }
        }
        mBinding.viewPager.adapter = VpFragmentPager(this, mFragments)
        mBinding.viewPager.offscreenPageLimit = tabs.size
        mBinding.viewPager.setCurrentItem(currentPosition, false)
        val mediator = TabLayoutMediator(mBinding.tabs, mBinding.viewPager) { tab, position ->
            val bind = ViewTabDappBinding.inflate(LayoutInflater.from(requireContext()))
            bind.ivHot.visibility = if (tabs[position].hot() == true) View.VISIBLE else View.GONE
            if (position == 0) bind.tvName.setTextColor(ContextCompat.getColor(requireContext(), R.color.color_title))
            bind.tvName.text = tabs[position].name()
            tab.customView = bind.root
        }
        mediator.attach()
        mBinding.tabs.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.customView?.findViewById<TextView>(R.id.tvName)
                    ?.setTextColor(ContextCompat.getColor(requireContext(), R.color.color_title))
                currentPosition = tab?.position ?: 0
                val categoryName = tabs[currentPosition].name()
                val bundle = Bundle()
                bundle.putString("category", categoryName.lowercase())
                FireBaseUtil.logFireBase(
                    "action_browser_menu",
                    bundle
                )
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.customView?.findViewById<TextView>(R.id.tvName)
                    ?.setTextColor(ContextCompat.getColor(requireContext(), R.color.color_title_sub))
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {

            }

        })
    }
}