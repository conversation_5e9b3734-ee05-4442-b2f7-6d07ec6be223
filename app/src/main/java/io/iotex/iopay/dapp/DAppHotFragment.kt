package io.iotex.iopay.dapp

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.blankj.utilcode.util.ScreenUtils
import com.drakeet.multitype.MultiTypeAdapter
import com.youth.banner.indicator.CircleIndicator
import io.iotex.api.DiscoveryBannerQuery
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.dapp.item.DAppBannerAdapter
import io.iotex.iopay.dapp.item.DAppHotItemBinder
import io.iotex.iopay.dapp.viewmodel.DAppHotViewModel
import io.iotex.iopay.databinding.FragmentDappHotBinding
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.RefreshDAppEvent
import io.iotex.iopay.util.*
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.xapp.XAppsActivity
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class DAppHotFragment :
    BaseBindFragment<DAppHotViewModel, FragmentDappHotBinding>(R.layout.fragment_dapp_hot) {

    companion object {
        fun newInstance(): Fragment {
            return DAppHotFragment()
        }
    }

    private val hotAppsAdapter = MultiTypeAdapter()
    private val myAppsAdapter = MultiTypeAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun initView() {
        myAppsAdapter.register(DAppHotItemBinder(childFragmentManager))
        mBinding.recyclerViewMy.layoutManager = GridLayoutManager(context, 4)
        mBinding.recyclerViewMy.adapter = myAppsAdapter
        hotAppsAdapter.register(DAppHotItemBinder(childFragmentManager))
        mBinding.recyclerViewHot.layoutManager = GridLayoutManager(context, 4)
        mBinding.recyclerViewHot.adapter = hotAppsAdapter

        mBinding.llMore.setOnClickListener {
            val intent = Intent(activity, MyDAppActivity::class.java)
            startActivity(intent)
        }

    }

    override fun initData() {
        mViewModel.loadDiscoverBanner()
        mViewModel.bannerLiveData.observe(this) {
            handleBanners(it)
        }

        mViewModel.getCurrentNetWork()
        mViewModel.curNetworkLiveData.observe(this) { network ->
            if (network?.tokenApprovalChecker.isNullOrEmpty()) {
                mBinding.llApprovalChecker.setGone()
            } else {
                mBinding.llApprovalChecker.setVisible()
                mBinding.llApprovalChecker.setOnClickListener {
                    val intent = Intent(activity, XAppsActivity::class.java)
                    intent.putExtra(IoPayConstant.BROWSER_URL, network?.tokenApprovalChecker)
                    startActivity(intent)
                }
            }
        }

        mViewModel.loadHotDApps()
        mViewModel.hotDAppLiveData.observe(this) {
            if (it.isNullOrEmpty()) {
                mBinding.llHot.visibility = View.GONE
            } else {
                mBinding.llHot.visibility = View.VISIBLE
                hotAppsAdapter.items = it
                hotAppsAdapter.notifyDataSetChanged()
            }
        }

        mViewModel.loadMyDApps()
        mViewModel.myDAppLiveData.observe(this) {
            if (it.isNullOrEmpty()) {
                mBinding.llMyDApp.visibility = View.GONE
            } else {
                mBinding.llMyDApp.visibility = View.VISIBLE
                myAppsAdapter.items = it
                myAppsAdapter.notifyDataSetChanged()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mViewModel.loadMyDApps()
    }

    private fun handleBanners(banners: List<DiscoveryBannerQuery.Discovery_banner>) {
        val ivParams = mBinding.banner.layoutParams
        ivParams.width = ScreenUtils.getScreenWidth() - 30.dp2px()
        ivParams.height = (ivParams.width * 114 / 345)

        mBinding.banner.layoutParams = ivParams
        if (banners.isEmpty()) {
            mBinding.banner.visibility = View.GONE
        } else {
            mBinding.banner.visibility = View.VISIBLE
        }
        mBinding.banner.setTouchSlop(0)
        mBinding.banner.setLoopTime(4500)
        mBinding.banner.addBannerLifecycleObserver(viewLifecycleOwner)
            .setIndicator(CircleIndicator(requireActivity()))
            .setAdapter(DAppBannerAdapter(banners).apply {
                setOnBannerListener { data, _ ->
                    SchemeUtil.goto(requireContext(),data.url())
                    val bundleEvent = Bundle()
                    bundleEvent.putString(FireBaseUtil.DAPP_URL, data.url())
                    FireBaseUtil.logFireBase(FireBaseEvent.CLICK_DISCOVERY_BANNER, bundleEvent)
                }
            })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshDAppEvent(event: RefreshDAppEvent) {
        mViewModel.loadDiscoverBanner()
        mViewModel.loadMyDApps()
        mViewModel.loadHotDApps()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNetworkSwitchEvent(event: NetworkSwitchEvent) {
        mViewModel.getCurrentNetWork()
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

}