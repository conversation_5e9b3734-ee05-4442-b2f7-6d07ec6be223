package io.iotex.iopay.data

import com.apollographql.apollo.ApolloClient
import io.iotex.base.okHttpClient
import io.iotex.iopay.util.Config

class ApolloDataSourceHelper private constructor() {
    companion object {
        val instance: ApolloDataSourceHelper by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            ApolloDataSourceHelper()
        }
    }

    private val ioPayApolloClient: ApolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    fun getIoPayDataSourceV1(): IoPayDataV1Source {
        return IoPayV1CallbackService(ioPayApolloClient)
    }
}
