package io.iotex.iopay.data.db

import android.content.Context
import android.database.sqlite.SQLiteException
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.blankj.utilcode.util.LogUtils
import io.iotex.iopay.meta.bean.DeviceDao
import io.iotex.iopay.meta.bean.DeviceEntry
import io.iotex.iopay.meta.bean.RecordDao
import io.iotex.iopay.meta.bean.RecordEntry

@Database(
    entities = [Wallet::class,
        DAppRecord::class,
        Notification::class,
        TrustDapp::class,
        SignApp::class,
        TokenAddressInfo::class,
        DApps::class,
        RPCNetwork::class,
        ERC20Entry::class,
        ActionRecordEntry::class,
        SignatureEntry::class,
        AddressBookEntry::class,
        NFTTokens::class,
        DiscoverDApps::class,
        RPCNetworkNode::class,
        DappPromote::class,
        NetworkChain::class,
        RecordEntry::class,
        DeviceEntry::class,
        CertifiedContractEntry::class,
        NftTokenEntry::class,
        ContractErrorMsgEntry::class,
        Mnemonic::class,
        WalletCache::class,
        NetworkAAConfig::class,
        BitcoinWallet::class,
        PointTaskEntry::class,
        WalletConnectSessionEntry::class,
        TokenEntry::class,
        TokenCacheEntry::class,
        SolanaWallet::class],
    version = 57,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun walletDao(): WalletDao
    abstract fun dAppRecordDao(): DAppRecordDao
    abstract fun notificationDao(): NotificationDao
    abstract fun signAppDao(): SignAppDao
    abstract fun tokenAddressInfoDao(): TokenAddressInfoDao
    abstract fun trustDapp(): TrustDappDao
    abstract fun dAppSort(): DAppsDao
    abstract fun rpcNetworkDao(): RPCNetworkDao
    abstract fun actionRecordDao(): ActionRecordDao
    abstract fun signatureDao(): SignatureDao
    abstract fun addressBookDao(): AddressBookDao
    abstract fun discoverDapps(): DiscoverDAppsDao
    abstract fun rpcNetworkNode(): RPCNetworkNodeDao
    abstract fun dappPromoteDao(): DappPromoteDao
    abstract fun networkChainDao(): NetworkChainDao
    abstract fun recordDao(): RecordDao
    abstract fun deviceDao(): DeviceDao
    abstract fun nftTokenDao(): NftTokenDao
    abstract fun certifiedContractDao(): CertifiedContractDao
    abstract fun mnemonicDao(): MnemonicDao
    abstract fun contractErrorMsgDao(): ContractErrorMsgDao
    abstract fun networkAAConfigDao(): NetworkAAConfigDao
    abstract fun walletCacheDao(): WalletCacheDao
    abstract fun bitcoinWalletDao(): BitcoinWalletDao
    abstract fun pointTaskDao(): PointTaskDao
    abstract fun solanaWalletDao(): SolanaWalletDao
    abstract fun walletConnectSessionDao(): WalletConnectSessionDao
    abstract fun tokenDao(): TokenDao
    abstract fun tokenCacheDao(): TokenCacheDao

    companion object {

        internal val Tag = this::class.java.simpleName

        // For Singleton instantiation
        @Volatile
        private var instance: AppDatabase? = null

        fun getInstance(context: Context): AppDatabase {

            return instance ?: synchronized(AppDatabase::class.java) {
                instance
                    ?: Room.databaseBuilder(context, AppDatabase::class.java, "iopay_db")
                        .addMigrations(
                            MIGRATION_1_2,
                            MIGRATION_2_3,
                            MIGRATION_3_4,
                            MIGRATION_4_5,
                            MIGRATION_5_6,
                            MIGRATION_6_7,
                            MIGRATION_7_8,
                            MIGRATION_8_9,
                            MIGRATION_9_10,
                            MIGRATION_10_11,
                            MIGRATION_11_12,
                            MIGRATION_12_13,
                            MIGRATION_13_14,
                            MIGRATION_14_15,
                            MIGRATION_15_16,
                            MIGRATION_16_17,
                            MIGRATION_17_18,
                            MIGRATION_18_19,
                            MIGRATION_19_20,
                            MIGRATION_20_21,
                            MIGRATION_21_22,
                            MIGRATION_22_23,
                            MIGRATION_23_24,
                            MIGRATION_24_25,
                            MIGRATION_25_26,
                            MIGRATION_26_27,
                            MIGRATION_27_28,
                            MIGRATION_28_29,
                            MIGRATION_29_30,
                            MIGRATION_30_31,
                            MIGRATION_31_32,
                            MIGRATION_32_33,
                            MIGRATION_33_34,
                            MIGRATION_34_35,
                            MIGRATION_35_36,
                            MIGRATION_36_37,
                            MIGRATION_37_38,
                            MIGRATION_38_39,
                            MIGRATION_39_40,
                            MIGRATION_40_41,
                            MIGRATION_41_42,
                            MIGRATION_42_43,
                            MIGRATION_43_44,
                            MIGRATION_44_45,
                            MIGRATION_45_46,
                            MIGRATION_46_47,
                            MIGRATION_47_48,
                            MIGRATION_48_49,
                            MIGRATION_49_50,
                            MIGRATION_50_51,
                            MIGRATION_51_52,
                            MIGRATION_52_53,
                            MIGRATION_53_54,
                            MIGRATION_54_55,
                            MIGRATION_55_56,
                            MIGRATION_56_57,
                        )
                        .build().also { instance = it }
            }
        }

        private val MIGRATION_1_2: Migration = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE wallet ADD COLUMN thumbnail INTEGER")
                } catch (e: SQLiteException) {
                    e.printStackTrace()
                    LogUtils.e(Tag, "MIGRATION_1_2 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_2_3: Migration = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS DAppRecord (url TEXT  NOT NULL, time INTEGER NOT NULL,PRIMARY KEY(url))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_2_3 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_3_4: Migration = object : Migration(3, 4) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE notification (id INTEGER primary key autoincrement NOT NULL,msg_id INTEGER NOT NULL,address TEXT NOT NULL,title TEXT NOT NULL,activity TEXT,customContent TEXT,notification_action_type INTEGER NOT NULL,read INTEGER NOT NULL,content TEXT NOT NULL,update_time INTEGER NOT NULL)")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_3_4 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_4_5: Migration = object : Migration(4, 5) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS SignApp (id INTEGER primary key autoincrement NOT NULL, walletId INTEGER NOT NULL, token TEXT  NOT NULL, source TEXT  NOT NULL, nonce TEXT  NOT NULL, createAt INTEGER  NOT NULL, expireAt INTEGER NOT NULL)")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_4_5 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_5_6: Migration = object : Migration(5, 6) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS xrc20 (wallet_address TEXT  NOT NULL, network TEXT  NOT NULL, name TEXT  NOT NULL, address TEXT  NOT NULL, PRIMARY KEY (wallet_address, address, network))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_5_6 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_6_7: Migration = object : Migration(6, 7) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS TokenAddressMetadata (id INTEGER primary key autoincrement NOT NULL, address TEXT  NOT NULL, name TEXT  NOT NULL, logo TEXT  NOT NULL, type TEXT  NOT NULL, image_urls TEXT)")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_6_7 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_7_8: Migration = object : Migration(7, 8) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE wallet ADD COLUMN balance TEXT")
                } catch (e: SQLiteException) {
                    e.printStackTrace()
                    LogUtils.e(Tag, "MIGRATION_7_8 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_8_9: Migration = object : Migration(8, 9) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS CustomToken (address TEXT  NOT NULL, name TEXT NOT NULL,PRIMARY KEY(address))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_8_9 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_9_10: Migration = object : Migration(9, 10) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE CustomToken ADD COLUMN network TEXT NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_9_10 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_10_11: Migration = object : Migration(10, 11) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE xrc20 ADD COLUMN logo TEXT NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_10_11 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_11_12: Migration = object : Migration(11, 12) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS TrustDapp (wallet_address TEXT  NOT NULL, dapp TEXT  NOT NULL, truest_time INTEGER  NOT NULL, PRIMARY KEY (wallet_address, dapp))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_11_12 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_12_13: Migration = object : Migration(12, 13) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE TrustDapp ADD COLUMN  dappName TEXT NOT NULL DEFAULT 0")
                    database.execSQL("ALTER TABLE TrustDapp ADD COLUMN  logo TEXT NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_12_13 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_13_14: Migration = object : Migration(13, 14) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("DROP TABLE IF EXISTS TokenAddressMetadata")
                    database.execSQL("CREATE TABLE IF NOT EXISTS TokenAddressInfo (address TEXT NOT NULL, name TEXT  NOT NULL, logo TEXT NOT NULL, type TEXT NOT NULL, image_urls TEXT, PRIMARY KEY(address))")
                    database.execSQL("CREATE TABLE IF NOT EXISTS DApps (url TEXT  NOT NULL, title TEXT  NOT NULL,logo TEXT  NOT NULL,lastTime INTEGER  NOT NULL,intervalDay INTEGER  NOT NULL,count INTEGER  NOT NULL,weightValue REAL  NOT NULL,PRIMARY KEY (url))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_13_14 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_14_15: Migration = object : Migration(14, 15) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS CustomNode (address TEXT  NOT NULL, name TEXT  NOT NULL,type INTEGER  NOT NULL,symbol TEXT,PRIMARY KEY (address))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_14_15 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_15_16: Migration = object : Migration(15, 16) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE DApps ADD COLUMN  named INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("ALTER TABLE DApps ADD COLUMN  category TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE DApps ADD COLUMN  content TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE DApps ADD COLUMN  contentCN TEXT NOT NULL DEFAULT ''")
                    database.execSQL("DELETE FROM DApps")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_15_16 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_16_17: Migration = object : Migration(16, 17) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE TokenAddressInfo ADD COLUMN  remark TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_16_17 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_17_18: Migration = object : Migration(17, 18) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE xrc20 ADD COLUMN  balance TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE xrc20 ADD COLUMN  sortValue TEXT NOT NULL DEFAULT '0'")
                    database.execSQL("ALTER TABLE xrc20 ADD COLUMN  isTag INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_17_18 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_18_19: Migration = object : Migration(18, 19) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE wallet ADD COLUMN isWatch INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_18_19 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_19_20: Migration = object : Migration(19, 20) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE CustomToken ADD COLUMN symbol TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE CustomToken ADD COLUMN decimals INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_18_19 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_20_21: Migration = object : Migration(20, 21) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE DApps ADD COLUMN weight INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_20_21 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_21_22: Migration = object : Migration(21, 22) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE DAppRecord ADD COLUMN loadUrl TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE DAppRecord ADD COLUMN logo TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_21_22 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_22_23: Migration = object : Migration(22, 23) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS erc20 (wallet_address TEXT  NOT NULL, chain_id INTEGER  NOT NULL, address TEXT  NOT NULL,  id TEXT  NOT NULL, name TEXT  NOT NULL,symbol TEXT  NOT NULL,decimals INTEGER NOT NULL,logo TEXT  NOT NULL,balance TEXT  NOT NULL,price TEXT  NOT NULL,is_tag INTEGER  NOT NULL,status INTEGER  NOT NULL,timestamp TEXT  NOT NULL,PRIMARY KEY (wallet_address, chain_id,address))")
                    database.execSQL("CREATE TABLE IF NOT EXISTS ActionRecord (hash TEXT NOT NULL, wallet_address TEXT NOT NULL, chain_id INTEGER NOT NULL, from_address TEXT NOT NULL, to_address TEXT NOT NULL, dapp TEXT NOT NULL, method TEXT NOT NULL, amount TEXT NOT NULL, fee TEXT NOT NULL, contract_address TEXT NOT NULL, nonce TEXT NOT NULL, status INTEGER NOT NULL, type INTEGER NOT NULL, timestamp TEXT NOT NULL, PRIMARY KEY (hash))")
                    database.execSQL("CREATE TABLE IF NOT EXISTS signature (id TEXT NOT NULL, signature TEXT NOT NULL, PRIMARY KEY (id))")
                    database.execSQL("CREATE TABLE IF NOT EXISTS AddressBook (chain_id INTEGER NOT NULL, address TEXT NOT NULL, name TEXT NOT NULL, describe TEXT NOT NULL, PRIMARY KEY (chain_id, address))")
                    database.execSQL("CREATE TABLE IF NOT EXISTS RPCNetwork (id TEXT NOT NULL, chain_id INTEGER  NOT NULL, name TEXT  NOT NULL,short_name TEXT  NOT NULL,platform TEXT NOT NULL,rpc TEXT  NOT NULL,explorer TEXT  NOT NULL,logo TEXT  NOT NULL,currency_symbol TEXT  NOT NULL,currency_name TEXT  NOT NULL,currency_logo TEXT  NOT NULL,currency_decimals INTEGER  NOT NULL,currency_price TEXT  NOT NULL,token_alias TEXT NOT NULL,multicall_address TEXT NOT NULL,swap_url TEXT NOT NULL,dev_mode INTEGER NOT NULL, theme_bg TEXT NOT NULL, theme_bg2 TEXT NOT NULL, networkName TEXT NOT NULL, networkOrder INTEGER NOT NULL, PRIMARY KEY (id))")
                    database.execSQL("ALTER TABLE DApps ADD COLUMN  chains TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE wallet ADD COLUMN chain_id INTEGER NOT NULL DEFAULT 4689")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_22_23 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_23_24: Migration = object : Migration(23, 24) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS erc20_source (chain_id INTEGER  NOT NULL, address TEXT  NOT NULL,  id TEXT  NOT NULL, name TEXT  NOT NULL,symbol TEXT  NOT NULL,decimals INTEGER NOT NULL,logo TEXT  NOT NULL,price TEXT  NOT NULL, timestamp TEXT  NOT NULL, PRIMARY KEY (chain_id,address))")
                    database.execSQL("ALTER TABLE wallet ADD COLUMN timestamp TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_23_24 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_24_25: Migration = object : Migration(24, 25) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS DiscoverDApps (url TEXT NOT NULL, img_url TEXT NOT NULL, content_cn TEXT NOT NULL, content TEXT NOT NULL, chains TEXT NOT NULL, id TEXT NOT NULL, tags TEXT NOT NULL, title TEXT NOT NULL, weight INTEGER NOT NULL, PRIMARY KEY(url))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_24_25 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_25_26: Migration = object : Migration(25, 26) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS NFTTokens (address TEXT NOT NULL, chainId INTEGER NOT NULL, decimals INTEGER NOT NULL, id TEXT NOT NULL, logo TEXT NOT NULL, name TEXT NOT NULL, symbol TEXT NOT NULL, PRIMARY KEY(address))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_25_26 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_26_27: Migration = object : Migration(26, 27) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS NFTTokensDisPlay (walletAddressChain TEXT NOT NULL,address TEXT NOT NULL, chainId INTEGER NOT NULL, decimals INTEGER NOT NULL, id TEXT NOT NULL, logo TEXT NOT NULL, name TEXT NOT NULL, symbol TEXT NOT NULL,balance INTEGER NOT NULL,walletAddress TEXT NOT NULL, PRIMARY KEY(walletAddressChain))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_26_27 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_27_28: Migration = object : Migration(27, 28) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN bg_image TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN bg_color_start TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN bg_color_end TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_27_28 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_28_29: Migration = object : Migration(28, 29) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE wallet ADD COLUMN avatar TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_28_29 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_29_30: Migration = object : Migration(29, 30) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN immutable INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("CREATE TABLE IF NOT EXISTS RPCNetworkNode (chain_id INTEGER NOT NULL, rpc_url TEXT NOT NULL, rpc_status INTEGER NOT NULL DEFAULT 2, immutable INTEGER NOT NULL DEFAULT 0, active INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(chain_id, rpc_url))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_29_30 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_30_31: Migration = object : Migration(30, 31) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS DappPromote (id TEXT NOT NULL, title TEXT NOT NULL, logo TEXT NOT NULL, chain TEXT NOT NULL, url TEXT NOT NULL, promote_content TEXT NOT NULL, promote_content_cn TEXT NOT NULL, promote INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(id))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_30_31 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_31_32: Migration = object : Migration(31, 32) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS NetworkChain (name TEXT NOT NULL, icon TEXT NOT NULL, chain TEXT NOT NULL, chain_id TEXT NOT NULL, rpc TEXT NOT NULL, info_url TEXT NOT NULL, symbol TEXT NOT NULL, PRIMARY KEY(name))")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN is_top INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("CREATE TABLE IF NOT EXISTS RecordEntry (id TEXT NOT NULL, imei TEXT NOT NULL, lng TEXT NOT NULL, lat TEXT NOT NULL, timestamp TEXT NOT NULL, success TEXT NOT NULL, PRIMARY KEY(id))")
                    database.execSQL("CREATE TABLE IF NOT EXISTS DeviceEntry (imei TEXT NOT NULL, sn TEXT NOT NULL, pub_key TEXT NOT NULL, owner TEXT NOT NULL, power INTEGER NOT NULL DEFAULT 0, registered INTEGER NOT NULL,  activated INTEGER NOT NULL, PRIMARY KEY(imei))")
                    database.execSQL("ALTER TABLE NFTTokensDisPlay ADD COLUMN type INTEGER NOT NULL DEFAULT 721")
                    database.execSQL("ALTER TABLE NFTTokensDisPlay ADD COLUMN tokenId TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE NFTTokensDisPlay ADD COLUMN amount TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE ActionRecord ADD COLUMN description TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE DappPromote ADD COLUMN token TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_31_32 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_32_33: Migration = object : Migration(32, 33) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS NftToken (contract TEXT NOT NULL, token_id INTEGER NOT NULL, name TEXT NOT NULL, symbol TEXT NOT NULL, wallet_address TEXT NOT NULL, chain_id INTEGER NOT NULL, token_url TEXT NOT NULL, type TEXT NOT NULL, amount INTEGER NOT NULL, PRIMARY KEY(contract, token_id))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_32_33 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_33_34: Migration = object : Migration(33, 34) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("DROP TABLE NftToken")
                    database.execSQL("CREATE TABLE IF NOT EXISTS NftToken (contract TEXT NOT NULL, token_id TEXT NOT NULL, name TEXT NOT NULL, symbol TEXT NOT NULL, wallet_address TEXT NOT NULL, chain_id INTEGER NOT NULL, token_url TEXT NOT NULL, type TEXT NOT NULL, amount TEXT NOT NULL, PRIMARY KEY(contract, token_id))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_33_34 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_34_35: Migration = object : Migration(34, 35) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN risk_status TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN token_approval_checker TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_34_35 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_35_36: Migration = object : Migration(35, 36) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE DiscoverDApps ADD COLUMN popularity TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE NftToken ADD COLUMN sbt INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN weight INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN weight INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_32_33 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_36_37: Migration = object : Migration(36, 37) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("DROP TABLE NftToken")
                    database.execSQL("CREATE TABLE IF NOT EXISTS NftToken (contract TEXT NOT NULL, token_id TEXT NOT NULL, name TEXT NOT NULL, symbol TEXT NOT NULL, wallet_address TEXT NOT NULL, chain_id INTEGER NOT NULL, token_url TEXT NOT NULL, type TEXT NOT NULL, amount TEXT NOT NULL, sbt INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(contract, token_id, wallet_address))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_36_37 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_37_38: Migration = object : Migration(37, 38) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN website TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN website TEXT NOT NULL DEFAULT ''")
                    database.execSQL("CREATE TABLE IF NOT EXISTS CertifiedContractEntry (contract TEXT NOT NULL DEFAULT '',name TEXT NOT NULL DEFAULT '',nike_name TEXT NOT NULL DEFAULT '',option TEXT NOT NULL DEFAULT '', PRIMARY KEY(contract))")
                    database.execSQL("ALTER TABLE wallet ADD COLUMN address_index INTEGER")
                    database.execSQL("ALTER TABLE wallet ADD COLUMN mnemonic_id TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE wallet ADD COLUMN `order` INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("CREATE TABLE IF NOT EXISTS mnemonic (id TEXT NOT NULL, encrypted_mnemonic TEXT NOT NULL, coin_type INTEGER NOT NULL, name TEXT NOT NULL, avatar TEXT NOT NULL, PRIMARY KEY(id))")
                    database.execSQL("ALTER TABLE DAppRecord ADD COLUMN chains TEXT NOT NULL DEFAULT ''")
                    database.execSQL("CREATE TABLE IF NOT EXISTS ContractErrorMsgEntry (id INTEGER NOT NULL DEFAULT 0,error TEXT NOT NULL DEFAULT '',msg TEXT DEFAULT '',msg_cn TEXT DEFAULT '', PRIMARY KEY(id))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_37_38 migrate ${e.message}")
                }
            }
        }
        //change PrimaryKey
        private val MIGRATION_38_39: Migration = object : Migration(38, 39) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS RPCNetworkTemp (chain_id INTEGER NOT NULL DEFAULT 0, id TEXT NOT NULL DEFAULT '', name TEXT NOT NULL DEFAULT '', short_name TEXT NOT NULL DEFAULT '', platform TEXT NOT NULL DEFAULT '', rpc TEXT NOT NULL DEFAULT '', explorer TEXT NOT NULL DEFAULT '', logo TEXT NOT NULL DEFAULT '', currency_symbol TEXT NOT NULL DEFAULT '', currency_name TEXT NOT NULL DEFAULT '', currency_logo TEXT NOT NULL DEFAULT '', currency_decimals INTEGER NOT NULL DEFAULT 0, currency_price TEXT NOT NULL DEFAULT '',token_alias TEXT NOT NULL DEFAULT '',multicall_address TEXT NOT NULL DEFAULT '',swap_url TEXT NOT NULL DEFAULT '', dev_mode INTEGER NOT NULL DEFAULT 0,theme_bg TEXT NOT NULL DEFAULT '',theme_bg2 TEXT NOT NULL DEFAULT '',networkName TEXT NOT NULL DEFAULT '',networkOrder INTEGER NOT NULL DEFAULT 0,bg_image TEXT NOT NULL DEFAULT '',bg_color_start TEXT NOT NULL DEFAULT '',bg_color_end TEXT NOT NULL DEFAULT '',immutable INTEGER NOT NULL DEFAULT 0,token_approval_checker TEXT NOT NULL DEFAULT '',PRIMARY KEY(chain_id))")
                    database.execSQL("INSERT OR IGNORE INTO RPCNetworkTemp (id,chain_id,name,short_name,platform,rpc,explorer,logo,currency_symbol,currency_name,currency_logo,currency_decimals,currency_price,token_alias,multicall_address,swap_url,dev_mode,theme_bg,theme_bg2,networkName,networkOrder,bg_image,bg_color_start,bg_color_end,immutable,token_approval_checker) select  id,chain_id,name,short_name,platform,rpc,explorer,logo,currency_symbol,currency_name,currency_logo,currency_decimals,currency_price,token_alias,multicall_address,swap_url,dev_mode,theme_bg,theme_bg2,networkName,networkOrder,bg_image,bg_color_start,bg_color_end,immutable,token_approval_checker  from RPCNetwork")
                    database.execSQL("DROP TABLE RPCNetwork")
                    database.execSQL("ALTER TABLE RPCNetworkTemp RENAME TO RPCNetwork")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_38_39 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_39_40: Migration = object : Migration(39, 40) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN chain_icon TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN chain_icon_selected TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN gas_station TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_39_40 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_40_41: Migration = object : Migration(40, 41) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE wallet ADD COLUMN aa_salt TEXT")
                    database.execSQL("ALTER TABLE wallet ADD COLUMN email TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE wallet ADD COLUMN effective INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("CREATE TABLE IF NOT EXISTS network_aa_config (chain_id INTEGER NOT NULL DEFAULT 4690, entry_point TEXT NOT NULL DEFAULT '', factory TEXT NOT NULL DEFAULT '', bound_email TEXT NOT NULL DEFAULT '', email_service TEXT NOT NULL DEFAULT '', bundler_service TEXT NOT NULL DEFAULT '', paymaster_service TEXT NOT NULL DEFAULT '', subgraph TEXT NOT NULL DEFAULT '',PRIMARY KEY(chain_id))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_40_41 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_41_42: Migration = object : Migration(41, 42) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN is_depin_token INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN tags TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN is_depin_token INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN update_time TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN update_time TEXT NOT NULL DEFAULT ''")
                    database.execSQL("CREATE TABLE IF NOT EXISTS WalletCache (chain_id INTEGER NOT NULL DEFAULT 0, address TEXT NOT NULL DEFAULT '',fee_gas TEXT NOT NULL DEFAULT '',balance TEXT NOT NULL DEFAULT '',stake TEXT NOT NULL DEFAULT '', PRIMARY KEY(address,chain_id))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_41_42 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_42_43: Migration = object : Migration(42, 43) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN is_official INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN is_official INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_42_43 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_43_44: Migration = object : Migration(43, 44) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE network_aa_config ADD COLUMN force_use_paymaster INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_43_44 migrate ${e.message}")
                }
            }
        }

        //change PrimaryKey
        private val MIGRATION_44_45: Migration = object : Migration(44, 45) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS WalletEntry (address TEXT NOT NULL DEFAULT '', alias TEXT NOT NULL DEFAULT '', password TEXT NOT NULL DEFAULT '', file TEXT NOT NULL DEFAULT '', is_watch INTEGER NOT NULL DEFAULT 0, timestamp TEXT NOT NULL DEFAULT '', `order` INTEGER NOT NULL DEFAULT 0, mnemonic_id TEXT NOT NULL DEFAULT '', address_index INTEGER NOT NULL DEFAULT 0, aa_salt TEXT NOT NULL DEFAULT '', email TEXT NOT NULL DEFAULT '', effective INTEGER NOT NULL DEFAULT 0,PRIMARY KEY(address))")
                    database.execSQL("INSERT OR REPLACE INTO WalletEntry (address,alias,password,file,is_watch,timestamp,`order`,mnemonic_id,address_index,aa_salt,email,effective) select address,alias,password,file,isWatch,timestamp,`order`,mnemonic_id,address_index,aa_salt,email,effective from wallet")
//                    database.execSQL("DROP TABLE IF EXISTS wallet")//next version
//                    database.execSQL("DROP TABLE IF EXISTS ActionRecord")//next version
                    database.execSQL("DROP TABLE IF EXISTS CustomToken")
                    database.execSQL("DROP TABLE IF EXISTS CustomNode")
                    database.execSQL("CREATE TABLE IF NOT EXISTS ActionRecordEntry (timestamp TEXT NOT NULL DEFAULT '', chain_id INTEGER NOT NULL DEFAULT 0, hash TEXT NOT NULL DEFAULT '', `from` TEXT NOT NULL DEFAULT '', `to` TEXT NOT NULL DEFAULT '', value TEXT NOT NULL DEFAULT '', data TEXT NOT NULL DEFAULT '', gas_price TEXT NOT NULL DEFAULT '', gas_limit TEXT NOT NULL DEFAULT '', max_priority_fee TEXT NOT NULL DEFAULT '', max_fee_per_gas TEXT NOT NULL DEFAULT '', contract TEXT NOT NULL DEFAULT '', status INTEGER NOT NULL DEFAULT -1, type INTEGER NOT NULL DEFAULT 0,name TEXT NOT NULL DEFAULT '',symbol TEXT NOT NULL DEFAULT '', origin TEXT NOT NULL DEFAULT '', nonce TEXT NOT NULL DEFAULT '', method TEXT NOT NULL DEFAULT '', nft_token_id TEXT NOT NULL DEFAULT '', decimals TEXT NOT NULL DEFAULT '',nftTokenType TEXT NOT NULL DEFAULT '',cancel INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(timestamp))")
                    database.execSQL("INSERT OR IGNORE INTO ActionRecordEntry (timestamp, chain_id, hash, `from`, `to`, value, contract, status, type) select timestamp,chain_id,hash,wallet_address,to_address,amount,contract_address,status,type from ActionRecord")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_44_45 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_45_46: Migration = object : Migration(45, 46) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN price_change_24h TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN sparkline_in_7d TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN rank_point TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN price_change_24h TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN sparkline_in_7d TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN rank_point TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN theme_color TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN price_change_24h TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN sparkline_in_7d TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_42_43 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_46_47: Migration = object : Migration(46, 47) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE WalletEntry ADD COLUMN address_type INTEGER NOT NULL DEFAULT 1")
                    database.execSQL("ALTER TABLE WalletEntry ADD COLUMN path TEXT NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE WalletEntry ADD COLUMN path_type INTEGER NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE WalletCache ADD COLUMN available_balance TEXT NOT NULL DEFAULT '0'")
                    database.execSQL("CREATE TABLE IF NOT EXISTS BitcoinWallet (bitcoin_address TEXT NOT NULL DEFAULT '', chain_id INTEGER NOT NULL, evm_address TEXT NOT NULL DEFAULT '', address_type INTEGER NOT NULL DEFAULT 1, type_name TEXT NOT NULL DEFAULT '', path TEXT NOT NULL DEFAULT '', PRIMARY KEY(bitcoin_address, evm_address))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_46_47 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_47_48: Migration = object : Migration(47, 48) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS SolanaWallet (address_evm TEXT NOT NULL DEFAULT '', public_key_base58 TEXT NOT NULL DEFAULT '', PRIMARY KEY(address_evm))")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN pubkey TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_46_47 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_48_49: Migration = object : Migration(48, 49) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN chain_icon_light TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_48_49 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_49_50: Migration = object : Migration(49, 50) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS PointTaskEntry (id INTEGER NOT NULL DEFAULT 0, action_type TEXT NOT NULL DEFAULT '', transaction_type TEXT NOT NULL DEFAULT '', point TEXT NOT NULL DEFAULT '', released INTEGER NOT NULL DEFAULT 1, PRIMARY KEY(id))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_49_50 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_50_51: Migration = object : Migration(50, 51) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN is_meme INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN is_meme INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_49_50 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_51_52: Migration = object : Migration(51, 52) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS WalletConnectSessionEntry (topic TEXT NOT NULL DEFAULT '', logo TEXT NOT NULL DEFAULT '',name TEXT NOT NULL DEFAULT '',url TEXT NOT NULL DEFAULT '',address TEXT NOT NULL DEFAULT '',chains TEXT NOT NULL DEFAULT '', PRIMARY KEY(url,topic))")
                    database.execSQL("ALTER TABLE erc20 ADD COLUMN robot_pump INTEGER NOT NULL DEFAULT 0")
                    database.execSQL("ALTER TABLE erc20_source ADD COLUMN robot_pump INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_49_50 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_52_53: Migration = object : Migration(52, 53) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("CREATE TABLE IF NOT EXISTS TokenEntry (id TEXT NOT NULL DEFAULT '', chain_id INTEGER NOT NULL DEFAULT 0, address TEXT NOT NULL DEFAULT '', name TEXT NOT NULL DEFAULT '',symbol TEXT NOT NULL DEFAULT '', decimals INTEGER NOT NULL DEFAULT 18, logo TEXT NOT NULL DEFAULT '',price TEXT NOT NULL DEFAULT '',is_top INTEGER NOT NULL DEFAULT 0,risk_status TEXT NOT NULL DEFAULT '',is_custom_token INTEGER NOT NULL DEFAULT 0,weight INTEGER NOT NULL DEFAULT 0,website TEXT NOT NULL DEFAULT '',is_depin_token INTEGER NOT NULL DEFAULT 0,is_official INTEGER NOT NULL DEFAULT 0,update_time TEXT NOT NULL DEFAULT '',price_change_24h TEXT NOT NULL DEFAULT '',sparkline_in_7d TEXT NOT NULL DEFAULT '',rank_point TEXT NOT NULL DEFAULT '',tags TEXT NOT NULL DEFAULT '',is_meme INTEGER NOT NULL DEFAULT 0,robot_pump INTEGER NOT NULL DEFAULT 0,PRIMARY KEY(chain_id,address))")
                    database.execSQL("CREATE TABLE IF NOT EXISTS TokenCacheEntry (wallet_address TEXT NOT NULL DEFAULT '', chain_id INTEGER NOT NULL DEFAULT 0, address TEXT NOT NULL DEFAULT '', balance TEXT NOT NULL DEFAULT '',likeStatus INTEGER NOT NULL DEFAULT 0, pubkey TEXT NOT NULL DEFAULT '',PRIMARY KEY(wallet_address,chain_id,address))")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_52_53 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_53_54: Migration = object : Migration(53, 54) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN currency_id TEXT NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_53_54 migrate ${e.message}")
                }
            }
        }

        private val MIGRATION_54_55: Migration = object : Migration(54, 55) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN dapp_chain INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_54_55 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_55_56: Migration = object : Migration(55, 56) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE TokenEntry ADD COLUMN mimo_disabled INTEGER NOT NULL DEFAULT 0")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_54_55 migrate ${e.message}")
                }
            }
        }
        private val MIGRATION_56_57: Migration = object : Migration(56, 57) {
            override fun migrate(database: SupportSQLiteDatabase) {
                try {
                    database.execSQL("ALTER TABLE RPCNetwork ADD COLUMN token_category Text NOT NULL DEFAULT ''")
                    database.execSQL("ALTER TABLE TokenEntry ADD COLUMN category Text NOT NULL DEFAULT ''")
                } catch (e: SQLiteException) {
                    LogUtils.e(Tag, "MIGRATION_56_57 migrate ${e.message}")
                }
            }
        }
    }
}