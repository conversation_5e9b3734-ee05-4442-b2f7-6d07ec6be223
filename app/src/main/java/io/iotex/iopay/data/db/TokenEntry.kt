package io.iotex.iopay.data.db

import android.text.SpannableStringBuilder
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import com.blankj.utilcode.util.ColorUtils
import com.machinefi.w3bstream.utils.extension.setForeTextColor
import io.iotex.iopay.R
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.asNumericStr
import java.math.BigDecimal

@Entity(tableName = "TokenEntry", primaryKeys = ["chain_id", "address"])
data class TokenEntry(

    val id :String,

    @ColumnInfo(name = "chain_id")
    val chainId: Int,

    val address: String,

    val name: String,

    var symbol: String,

    var decimals: Int,

    val logo: String,

    var price: String,

    @ColumnInfo(name = "is_top")
    var isTop: Boolean = false,

    @ColumnInfo(name = "risk_status")
    var riskStatus: String = "",

    @ColumnInfo(name = "is_custom_token")
    val isCustomToken: Boolean = false,

    val weight: Int = 0,

    val website: String = "",

    @ColumnInfo(name = "is_depin_token")
    val isDepinToken: Boolean = false,

    @ColumnInfo(name = "is_official")
    val isOfficial: Boolean = false,

    @ColumnInfo(name = "update_time")
    val updateTime: String = "",

    val price_change_24h: String = "",

    val sparkline_in_7d: String = "",

    val rank_point: String = "",

    val tags :String = "",

    @ColumnInfo(name = "is_meme")
    val isMeme: Boolean = false,

    @ColumnInfo(name = "robot_pump")
    val isRobotPump: Boolean = false,

    @ColumnInfo(name = "mimo_disabled")
    val mimoDisabled: Boolean = false,

    val category :String = "",
) {
    @Ignore
    var balance: String = ""

    @Ignore
    var isStake: Boolean = false

    @Ignore
    var status: Int = 0

    val displayPrice: String
        get() {
            return TokenUtil.displayPrice(price)
        }

    val displayAmount: String
        get() {
            val exactBalance =
                TokenUtil.weiToTokenBN(balance, decimals.toLong())
            return TokenUtil.displayBalance(exactBalance)
        }

    val displayValue: String
        get() {
            val exactBalance =
                TokenUtil.weiToTokenBN(balance, decimals.toLong()).asBigDecimal()
            val value = exactBalance.multiply(price.asBigDecimal()).toPlainString()
            return TokenUtil.displayPrice(value)
        }

    val displayChange: SpannableStringBuilder
        get() {
            return kotlin.runCatching {
                val priceChangeBig = price_change_24h.asBigDecimal()
                val priceBig = price.asBigDecimal() - priceChangeBig
                if (priceBig != BigDecimal.ZERO) {
                    val percent = priceChangeBig * BigDecimal(100) / priceBig
                    var displayPercent =
                        TokenUtil.displayPrice(percent.toString().asNumericStr(), 2) + "%"
                    if (displayPercent.contains("-")) {
                        displayPercent.setForeTextColor(
                            displayPercent,
                            ColorUtils.getColor(R.color.color_f16381)
                        )
                    } else {
                        displayPercent = if(displayPercent.contains("+")) displayPercent else "+$displayPercent"
                        displayPercent.setForeTextColor(
                            displayPercent,
                            ColorUtils.getColor(R.color.color_00dc9c)
                        )
                    }
                } else {
                    SpannableStringBuilder()
                }
            }.getOrNull()?:SpannableStringBuilder()
        }

    override fun equals(other: Any?): Boolean {
        if(other is TokenEntry && balance != other.balance){
            return false
        }
        return super.equals(other)
    }
}

