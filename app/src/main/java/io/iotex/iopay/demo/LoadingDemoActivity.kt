package io.iotex.iopay.demo

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import com.airbnb.lottie.LottieAnimationView
import io.iotex.iopay.R
import io.iotex.iopay.util.LoadingUtils
import io.iotex.iopay.wallet.dialog.LogoLoadingDialog

/**
 * Logo Loading Animation Demo Activity
 * 展示IoTeX Logo加载动画的示例Activity
 */
class LoadingDemoActivity : AppCompatActivity() {

    private lateinit var lottieBasic: LottieAnimationView
    private lateinit var lottieEnhanced: LottieAnimationView
    private lateinit var btnShowBasic: Button
    private lateinit var btnShowEnhanced: Button
    private lateinit var btnHideLoading: Button
    private lateinit var btnTestUtils: Button

    private val handler = Handler(Looper.getMainLooper())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_loading_demo)
        
        initViews()
        setupClickListeners()
    }

    private fun initViews() {
        lottieBasic = findViewById(R.id.lottieBasic)
        lottieEnhanced = findViewById(R.id.lottieEnhanced)
        btnShowBasic = findViewById(R.id.btnShowBasic)
        btnShowEnhanced = findViewById(R.id.btnShowEnhanced)
        btnHideLoading = findViewById(R.id.btnHideLoading)
        btnTestUtils = findViewById(R.id.btnTestUtils)
    }

    private fun setupClickListeners() {
        // 显示基础版本对话框
        btnShowBasic.setOnClickListener {
            val dialog = LogoLoadingDialog.Builder(this)
                .setLoadingText("Loading with Basic Animation...")
                .setCancelable(true)
                .setCancelOutside(true)
                .create()
            
            dialog.show()
            
            // 3秒后自动关闭
            handler.postDelayed({
                if (dialog.isShowing) {
                    dialog.dismiss()
                }
            }, 3000)
        }

        // 显示增强版本对话框
        btnShowEnhanced.setOnClickListener {
            // 创建增强版本的对话框（需要修改LogoLoadingDialog来支持不同的动画）
            showEnhancedLoadingDialog()
        }

        // 隐藏加载动画
        btnHideLoading.setOnClickListener {
            LoadingUtils.hideLoading()
        }

        // 测试工具类
        btnTestUtils.setOnClickListener {
            testLoadingUtils()
        }
    }

    private fun showEnhancedLoadingDialog() {
        // 使用增强版本的动画
        val dialog = LogoLoadingDialog.Builder(this)
            .setLoadingText("Enhanced Animation Loading...")
            .setCancelable(true)
            .setCancelOutside(true)
            .create()
        
        // 这里可以通过反射或者扩展LogoLoadingDialog来使用增强版本的动画
        // 为了简化，我们先使用基础版本
        dialog.show()
        
        // 5秒后自动关闭
        handler.postDelayed({
            if (dialog.isShowing) {
                dialog.dismiss()
            }
        }, 5000)
    }

    private fun testLoadingUtils() {
        // 测试不同类型的加载动画
        val loadingTypes = listOf(
            "Default Loading" to { LoadingUtils.showLoading(this) },
            "Network Loading" to { LoadingUtils.showNetworkLoading(this) },
            "Wallet Loading" to { LoadingUtils.showWalletLoading(this) },
            "Transaction Loading" to { LoadingUtils.showTransactionLoading(this) },
            "Sync Loading" to { LoadingUtils.showSyncLoading(this) }
        )

        var currentIndex = 0
        
        fun showNextLoading() {
            if (currentIndex < loadingTypes.size) {
                val (name, action) = loadingTypes[currentIndex]
                action.invoke()
                
                handler.postDelayed({
                    LoadingUtils.hideLoading()
                    currentIndex++
                    
                    handler.postDelayed({
                        showNextLoading()
                    }, 500) // 间隔500ms
                }, 2000) // 每个显示2秒
            }
        }
        
        showNextLoading()
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacksAndMessages(null)
        LoadingUtils.hideLoading()
    }
}
