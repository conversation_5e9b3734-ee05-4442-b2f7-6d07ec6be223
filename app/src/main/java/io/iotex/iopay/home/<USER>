package io.iotex.iopay.home

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.databinding.FragmentMemeBinding
import io.iotex.iopay.home.item.HomeTokenBinder
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.support.eventbus.MainCardEyeRefresh
import io.iotex.iopay.support.eventbus.MainPullRefresh
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.support.eventbus.TokenLoadFinishEvent
import io.iotex.iopay.token.TokenDetailActivity
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.home.bean.fromToken
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class MemeFragment :
    BaseBindFragment<MemeViewModel, FragmentMemeBinding>(R.layout.fragment_meme) {

        companion object{
            private const val KEY_CATEGORY = "key_category"
            fun newInstance(category:String): MemeFragment{
                val args = Bundle()
                args.putString(KEY_CATEGORY,category)
                val fragment = MemeFragment()
                fragment.arguments = args
                return fragment
            }
        }

    override fun useEventBus(): Boolean {
        return true
    }

    private val mAdapter = MultiTypeAdapter()

    private val category by lazy {
        arguments?.getString(KEY_CATEGORY)
    }

    override fun initView() {
        val binder = HomeTokenBinder(false).apply {
            onItemClickCallback = {
                TokenDetailActivity.startActivity(
                    requireActivity(),
                    fromToken(it)
                )
            }
        }
        mAdapter.register(TokenEntry::class, binder)
        mBinding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        mBinding.recyclerView.adapter = mAdapter
    }

    override fun initEvent() {
        mBinding.llMeme.isVisible = category == "Meme"
        if (category == "xStocks") {
            mBinding.ivEmpty.setImageResource(R.drawable.icon_xstocks_empty)
            mBinding.tvEmpty.text =
                getString(R.string.to_trade_stocks_please_switch_to_the_solana_network)
        } else {
            mBinding.ivEmpty.setImageResource(R.drawable.icon_token_empty)
            mBinding.tvEmpty.text = getString(R.string.no_tokens)
        }
        mViewModel.category = category?:""
        mViewModel.getMemeList()
        mViewModel.tokenListLiveData.observe(this) {
            if (it.isNullOrEmpty()) {
                mBinding.llList.setGone()
                mBinding.llEmpty.setVisible()
            } else {
                mBinding.llEmpty.setGone()
                mBinding.llList.setVisible()
                mAdapter.items = it
                mAdapter.notifyDataSetChanged()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: TokenLoadFinishEvent) {
        mViewModel.getMemeList()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNetworkSwitchEvent(event: NetworkSwitchEvent) {
        mViewModel.getMemeList()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMainCardEyeRefresh(event: MainCardEyeRefresh) {
        mAdapter.notifyDataSetChanged()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletSwitchEvent(event: SwitchWalletEvent) {
        mViewModel.cancelAllJob()
        mViewModel.getMemeList()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMainPullRefresh(event: MainPullRefresh) {
        mViewModel.getMemeList()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshActionListEvent(event: ActionRefreshEvent) {
        mViewModel.getMemeList()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: FavoriteOrDislikeERC20Event) {
        mViewModel.getMemeList()
    }
}