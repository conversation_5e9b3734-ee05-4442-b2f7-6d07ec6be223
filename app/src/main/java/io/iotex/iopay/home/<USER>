package io.iotex.iopay.home

import android.annotation.SuppressLint
import android.view.MotionEvent
import androidx.core.view.isVisible
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.LogUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.FragmentDepinBinding
import io.iotex.iopay.support.eventbus.WebViewDePinErrorEvent
import io.iotex.iopay.support.eventbus.WebViewDePinRefreshErrorEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.loadResources
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.xapp.XAppFragment
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe

class DepinFragment:BaseBindFragment<BaseViewModel,FragmentDepinBinding>(R.layout.fragment_depin) {
    companion object{
        fun newInstance(): DepinFragment{
            return DepinFragment()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initView() {
        EventBus.getDefault().register(this)
        mBinding.ivDepinError.loadResources(R.drawable.icon_depin_web_error)
        val url = if (UserStore.isDarkTheme()) {
            "${Config.DEPIN_SCAN_WEB_URL}?theme=dark"
        } else {
            "${Config.DEPIN_SCAN_WEB_URL}?theme=light"
        }
        val webViewFrag = XAppFragment.newInstance(url, "", "", false)
        childFragmentManager.beginTransaction().add(R.id.flLayout, webViewFrag).commit()
        mBinding.refreshLayout.setOnRefreshListener {
            mBinding.refreshLayout.finishRefresh()
            mBinding.ivDepinError.setGone()
            mBinding.ivBack.setVisible()
            mBinding.refreshLayout.setEnableRefresh(false)
            webViewFrag.firstPageFinished = false
            webViewFrag.webView.reload()
        }

        mBinding.ivBack.setOnClickListener {
            webViewFrag.goBack()
        }

        webViewFrag.backAndForeCallback = { back, _ ->
            mBinding.ivBack.isVisible = back
        }

        mBinding.ivBack.setOnTouchListener { _, event ->
            when (event.action and MotionEvent.ACTION_MASK) {
                MotionEvent.ACTION_DOWN -> {
                    mBinding.ivBack.alpha = 0.5f
                }
                MotionEvent.ACTION_UP -> {
                    mBinding.ivBack.alpha = 1f
                }
            }
            return@setOnTouchListener false
        }

        PageEventUtil.logEvent(PageEventUtil.MENUDEPINSCAN)
        activity?.let {
            KeyboardUtils.registerSoftInputChangedListener(it){ height->
                if (height > 100) {
                    val layoutParams = mBinding.keyboardView.layoutParams
                    layoutParams.height = height - 50.dp2px()
                    mBinding.keyboardView.layoutParams = layoutParams
                } else {
                    val layoutParams = mBinding.keyboardView.layoutParams
                    layoutParams.height = 0
                    mBinding.keyboardView.layoutParams = layoutParams
                }
                LogUtils.i("cccc","height111:"+height)
            }
        }
    }

    @Subscribe
    fun onWebViewDePinErrorEvent(event: WebViewDePinErrorEvent) {
        mBinding.ivDepinError.setVisible()
        mBinding.ivBack.setGone()
        mBinding.refreshLayout.setEnableRefresh(true)
    }

    @Subscribe
    fun onWebViewDePinRefreshErrorEvent(event: WebViewDePinRefreshErrorEvent) {
        mBinding.refreshLayout.setEnableRefresh(true)
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }
}