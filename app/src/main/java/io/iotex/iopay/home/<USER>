package io.iotex.iopay.home

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.api.BigDecimal
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.google.gson.Gson
import io.iotex.base.RetrofitClient
import io.iotex.iopay.R
import io.iotex.iopay.api.SwapApi
import io.iotex.iopay.api.UrlApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.AppStore
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.bean.Approval
import io.iotex.iopay.data.bean.MethodParameters
import io.iotex.iopay.data.bean.Slippage
import io.iotex.iopay.data.bean.SwapToken
import io.iotex.iopay.data.bean.SwapTokenRequest
import io.iotex.iopay.data.bean.UniApprovalRequest
import io.iotex.iopay.data.bean.UniQuote
import io.iotex.iopay.data.bean.UniSwapRequest
import io.iotex.iopay.data.bean.UniSwapTokenRequest
import io.iotex.iopay.data.bean.UniSwapTokenResp
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.LIKE_STATUS_LIKE
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.STATUS_FAILED
import io.iotex.iopay.data.db.STATUS_SUCCESS
import io.iotex.iopay.data.db.TokenCacheEntry
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.home.SwapFragment.Companion.EXACT_INPUT
import io.iotex.iopay.repo.NativeTokenRepo
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.ZERO_ADDRESS
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.splitList
import io.iotex.iopay.util.extension.toJson
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.web3.Web3Delegate
import org.greenrobot.eventbus.EventBus
import org.web3j.tx.gas.DefaultGasProvider
import java.math.BigInteger

class SwapViewModel(application: Application) : BaseLaunchVM(application) {

    private val nativeTokenRepo by lazy {
        NativeTokenRepo()
    }

    private val swapService by lazy {
        RetrofitClient.createApiService(Config.SWAP_API_BASE_URL, SwapApi::class.java)
    }

    private val uniSwapService by lazy {
        RetrofitClient.createApiService(Config.UNI_SWAP_API_BASE_URL, SwapApi::class.java)
    }

    private val apiService by lazy {
        RetrofitClient.createApiService(Config.IOPAY_URL, UrlApi::class.java)
    }

    private val uniSwapApiHead = HashMap<String, String>().apply {
        put("content-type", "application/json")
        put("accept", "*/*")
        put(
            "user-agent",
            "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
        )
        put("x-api-key", "JoyCGj29tT4pymvhaGciK4r1aIPvqW6W53xT1fwo")
        put("x-request-source", "uniswap-web")
        put("x-universal-router-version", "2.0")
        put("origin", "https://app.uniswap.org")
        put("referer", "https://app.uniswap.org")
    }

    val chainIds = arrayListOf(4689, 1, 56, 137)

    val networkListLiveData = MutableLiveData<List<RPCNetwork>>()
    val fromTokenLiveData = MutableLiveData<TokenEntry>()
    val toTokenLiveData = MutableLiveData<TokenEntry>()

    val loadingLiveData = MutableLiveData<Boolean>()
    val priceImpactWarningLiveData = MutableLiveData<Boolean>()
    val priceImpactLiveData = MutableLiveData<String>()
    val priceLiveData = MutableLiveData<String>()
    val fromValueLiveData = MutableLiveData<String>()
    val fromMaxLiveData = MutableLiveData<String>()
    val toValueLiveData = MutableLiveData<String>()
    val toMinLiveData = MutableLiveData<String>()
    val feeLiveData = MutableLiveData<String>()
    val tokenRouterListLiveData = MutableLiveData<List<List<TokenEntry>>>()
    val methodParametersListLiveData = MutableLiveData<MethodParameters?>()
    val uniSwapTokenRespLiveData = MutableLiveData<UniSwapTokenResp?>()
    val uniSwapMethodParametersLiveData = MutableLiveData<MethodParameters>()

    val approveAmountLiveData = MutableLiveData<BigInteger>()
    val approveLiveData = MutableLiveData<Approval?>()
    val gasLiveData = MutableLiveData<BigInteger>()
    val actionLiveData = MutableLiveData<Boolean>()

    var hash:String?=null

    fun getNetwork() {
        addLaunch {
            val rpcNetworks = ArrayList<RPCNetwork>()
            chainIds.forEach { chainId ->
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(chainId)?.let {
                        rpcNetworks.add(it)
                    }
            }
            networkListLiveData.postValue(rpcNetworks)
        }
    }

    fun getDefaultToken(chainId:Int, address: String?,from:Boolean) {
        addLaunch {
            val token = if (address.isNullOrBlank()) {
                nativeTokenRepo.getNetworkToken(
                    UserStore.getWalletAddress(),
                    chainId
                )?.apply {
                    val balances = Web3Delegate.getCurrencyBalance(UserStore.getWalletAddress())
                    if(balances.isNotEmpty()){
                        balance = balances.firstOrNull().toString()
                    }
                }
            } else {
                AppDatabase.getInstance(Utils.getApp()).tokenDao()
                    .queryByAddress(chainId, address)?.apply {
                        val balances = Web3Delegate.getErc20Balance(address)
                        if(balances.isNotEmpty()){
                            balance = balances.firstOrNull().toString()
                        }
                    }
            }
            token?.let {
                if(from){
                    fromTokenLiveData.postValue(it)
                } else {
                    toTokenLiveData.postValue(it)
                }
                AppStore.sortToken[it.chainId]?.map { old ->
                    if (old.chainId == it.chainId && old.address == it.address) {
                        it
                    } else {
                        old
                    }
                }?.let { tokens ->
                    AppStore.sortToken[it.chainId] = tokens
                }
            }
        }
    }

    fun getSwap(
        chainId: Int?,
        tokenEntry0: TokenEntry?,
        tokenEntry1: TokenEntry?,
        amount: String,
        slip: BigDecimal,
        tradeType: String,
        protocols: String
    ) {
        if (chainId == null || tokenEntry0 == null || tokenEntry1 == null) return
        if (amount.asBigDecimal().compareTo(java.math.BigDecimal.ZERO) == 0) return
        if (tokenEntry0.address == tokenEntry1.address) return
        val token0Address =
            tokenEntry0.address.ifEmpty { "IOTX" }
        val token1Address =
            tokenEntry1.address.ifEmpty { "IOTX" }
        addLaunchSingle(onError = {
            methodParametersListLiveData.postValue(null)
            loadingLiveData.postValue(false)
        }) {
            loadingLiveData.postValue(true)
            val resp = swapService.postSwapApi(
                Config.SWAP_API_URL, SwapTokenRequest(
                    chainId,
                    protocols,
                    SwapToken(token0Address, tokenEntry0.decimals),
                    SwapToken(token1Address, tokenEntry1.decimals),
                    UserStore.getWalletAddress(),
                    amount,
                    Slippage((slip.toFloat() * 100).toInt(), 10000),
                    tradeType
                )
            )
            priceImpactWarningLiveData.postValue(
                resp.trade.priceImpact.asBigDecimal() > BigDecimal(
                    2
                )
            )
            priceImpactLiveData.postValue(resp.trade.priceImpact + "%")
            if (tradeType == EXACT_INPUT) {
                showToValue(
                    amount,
                    resp.quote.numerator,
                    resp.trade.priceImpact,
                    tokenEntry0,
                    tokenEntry1
                )
            } else {
                showFromValue(
                    resp.quote.numerator,
                    amount,
                    resp.trade.priceImpact,
                    tokenEntry1,
                    tokenEntry0
                )
            }
            showIotxFee(tradeType, amount, resp.quote.numerator, tokenEntry0, tokenEntry1)
            val tokens = ArrayList<TokenEntry>()
            resp.route[0].tokenPath.forEach {
                val token = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                    .queryByAddress(it.chainId, it.address.lowercase())
                token?.let { it1 -> tokens.add(it1) }
            }
            tokenRouterListLiveData.postValue(tokens.splitList(3))
            methodParametersListLiveData.postValue(resp.methodParameters)
            loadingLiveData.postValue(false)
        }
    }

    private fun showIotxFee(
        tradeType: String,
        amount: String,
        numerator: String,
        tokenEntry0: TokenEntry,
        tokenEntry1: TokenEntry
    ) {
        if (tradeType == EXACT_INPUT) {
            val fee = TokenUtil.displayPrice(
                TokenUtil.weiToTokenBN(
                    (amount.asBigDecimal() * BigDecimal(0.003)).toString(),
                    tokenEntry0.decimals.toLong()
                )
            )
            feeLiveData.postValue(fee + " " + tokenEntry0.symbol)
        } else {
            val fee = TokenUtil.displayPrice(
                TokenUtil.weiToTokenBN(
                    (numerator.asBigDecimal() * BigDecimal(0.003)).toString(),
                    tokenEntry1.decimals.toLong()
                )
            )
            feeLiveData.postValue(fee + " " + tokenEntry1.symbol)
        }
    }

    private fun showToValue(
        fromAmount: String,
        toAmount: String,
        priceImpact: String,
        tokenIn: TokenEntry,
        tokenOut: TokenEntry
    ) {
        val fromValue =
            TokenUtil.weiToTokenBN(fromAmount, tokenIn.decimals.toLong())
        val toValue =
            TokenUtil.weiToTokenBN(toAmount, tokenOut.decimals.toLong())
        toValueLiveData.postValue(toValue)
        val receiveMin =
            toValue.asBigDecimal() * (BigDecimal(100) - priceImpact.asBigDecimal()).divide(
                BigDecimal(100)
            )
        toMinLiveData.postValue(TokenUtil.displayPrice(receiveMin.toString()) + " " + tokenOut.symbol)
        val price = toValue.asBigDecimal() / fromValue.asBigDecimal()
        priceLiveData.postValue(
            "1 " + tokenIn.symbol + " = " + TokenUtil.displayPrice(
                price.toString()
            ) + " " + tokenOut.symbol
        )
    }

    private fun showFromValue(
        fromAmount: String,
        toAmount: String,
        priceImpact: String,
        tokenIn: TokenEntry,
        tokenOut: TokenEntry
    ) {
        val fromValue =
            TokenUtil.weiToTokenBN(fromAmount, tokenIn.decimals.toLong())
        val toValue =
            TokenUtil.weiToTokenBN(toAmount, tokenOut.decimals.toLong())
        fromValueLiveData.postValue(fromValue)
        val payMax =
            fromValue.asBigDecimal() * (BigDecimal(100) + priceImpact.asBigDecimal()).divide(
                BigDecimal(100)
            )
        fromMaxLiveData.postValue(TokenUtil.displayPrice(payMax.toString()) + " " + tokenIn.symbol)
        val price = toValue.asBigDecimal() / fromValue.asBigDecimal()
        priceLiveData.postValue(
            "1 " + tokenIn.symbol + " = " + TokenUtil.displayPrice(
                price.toString()
            ) + " " + tokenOut.symbol
        )
    }

    fun getUniSwap(
        chainId: Int?,
        tokenIn: TokenEntry?,
        tokenOut: TokenEntry?,
        amount: String,
        tradeType: String,
    ) {
        if (chainId == null || tokenIn == null || tokenOut == null) return
        if (amount.asBigDecimal().compareTo(java.math.BigDecimal.ZERO) == 0) return
        if (tokenIn.address == tokenOut.address) return
        val tokenInAddress =
            tokenIn.address.ifEmpty { ZERO_ADDRESS }
        val tokenOutAddress =
            tokenOut.address.ifEmpty { ZERO_ADDRESS }
        addLaunch(onError = {
            ToastUtils.showShort(R.string.please_switch_your_vpn)
            uniSwapTokenRespLiveData.postValue(null)
            loadingLiveData.postValue(false)
        }) {
            loadingLiveData.postValue(true)
            val request = UniSwapTokenRequest(
                amount,
                UserStore.getWalletAddress(),
                tokenInAddress,
                chainId,
                tokenOutAddress,
                chainId,
                tradeType,
            )
            val resp = uniSwapService.uniSwapQuote(request, uniSwapApiHead)
            val quote = Gson().fromJson(resp.quote.toJson(),UniQuote::class.java)
            priceImpactLiveData.postValue(quote.priceImpact + "%")
            if (tradeType == EXACT_INPUT) {
                showToValue(
                    quote.input.amount,
                    quote.output.amount,
                    quote.priceImpact,
                    tokenIn,
                    tokenOut
                )
            } else {
                showFromValue(
                    quote.input.amount,
                    quote.output.amount,
                    quote.priceImpact,
                    tokenIn,
                    tokenOut
                )
            }
            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId)
            feeLiveData.postValue(
                TokenUtil.weiToTokenBN(
                    quote.gasFee,
                    network?.currencyDecimals?.toLong() ?: 18
                ) + " " + network?.currencySymbol
            )
            val tokens = ArrayList<TokenEntry>()
            quote.route[0].forEach {
                val token = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                    .queryByAddress(it.tokenIn.chainId, it.tokenIn.address.lowercase())
                token?.let { it1 -> tokens.add(it1) }
            }
            val lastToken = quote.route[0].last().tokenOut
            val token = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                .queryByAddress(lastToken.chainId, lastToken.address.lowercase())
            token?.let { it1 -> tokens.add(it1) }
            tokenRouterListLiveData.postValue(tokens.splitList(3))
            uniSwapTokenRespLiveData.postValue(resp)
            loadingLiveData.postValue(false)
        }
    }

    fun checkUniApprove(amount: String, token: String?, chainId: Int?, tokenOut: String?) {
        if (chainId == null || token == null || tokenOut == null) return
        val tokenOutAddress =
            tokenOut.ifEmpty { ZERO_ADDRESS }
        addLaunch(onError = {
            ToastUtils.showShort(R.string.please_switch_your_vpn)
        }) {
            val request = UniApprovalRequest(
                UserStore.getWalletAddress(),
                amount,
                token,
                chainId,
                tokenOutAddress,
                chainId
            )
            val resp = uniSwapService.checkApproval(request, uniSwapApiHead)
            approveLiveData.postValue(resp.approval)
        }
    }

    fun uniSwapApi(quote: Any, permitData: Any?, signature: String?) {
        addLaunch(true,onError = {
            ToastUtils.showShort(R.string.please_switch_your_vpn)
        }) {
            val request = UniSwapRequest(quote, permitData, signature)
            val resp = uniSwapService.uniSwap(request, uniSwapApiHead)
            uniSwapMethodParametersLiveData.postValue(
                MethodParameters(
                    resp.swap.data,
                    resp.swap.value,
                    resp.swap.to
                )
            )
        }
    }

    fun checkApprove(contract: String, spender: String) {
        addLaunch {
            val amount = Web3Delegate.checkApprove(contract, spender)
            approveAmountLiveData.postValue(amount)
        }
    }

    fun checkResAndAddToken(contract: String) {
        addLaunch {
            hash?.let { res->
                val action = AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
                    .queryByHash(WalletHelper.getCurChainId(), res) ?: return@addLaunch
                if (action.status == STATUS_SUCCESS) {
                    actionLiveData.postValue(true)
                    if(contract.isNotEmpty()){
                        var tokenCache = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao()
                            .queryByAddress(UserStore.getWalletAddress(), UserStore.getChainId(), contract)
                        if (tokenCache == null) {
                            tokenCache = TokenCacheEntry(
                                UserStore.getWalletAddress(),
                                UserStore.getChainId(),
                                contract,
                                Web3Delegate.getErc20Balance(contract).toString(),
                                LIKE_STATUS_LIKE,
                            )
                        } else {
                            tokenCache.likeStatus = LIKE_STATUS_LIKE
                        }
                        addTokenToHome(tokenCache)
                    }
                    hash = null
                } else if(action.status == STATUS_FAILED) {
                    hash = null
                    ToastUtils.showShort(Utils.getApp().getString(R.string.may_be_due_to_price_fluctuation))
                }
            }
        }
    }

    private fun addTokenToHome(token: TokenCacheEntry) {
        addLaunch {
            token.likeStatus = LIKE_STATUS_LIKE
            val balances = Web3Delegate.getErc20Balance(token.address)
            if (balances.isNotEmpty()) {
                token.balance = balances.firstOrNull().toString()
            }
            AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(token)
            EventBus.getDefault().post(FavoriteOrDislikeERC20Event())
            Utils.getApp().getString(R.string.saved_successfully).toast()
        }
    }

    fun defaultGasFee() {
        val gasLimit = DefaultGasProvider.GAS_LIMIT
        val gasPrice = DefaultGasProvider.GAS_PRICE
        addLaunch(onError = {
            gasLiveData.postValue(gasLimit * gasPrice)
        }) {
            val response = apiService.getGasTracker(UserStore.getNetWorkGasStation())
            val marketPrice = response.result.ProposeGasPrice.toBigIntegerOrNull()
            if (response.status == "1" && marketPrice != null && marketPrice.compareTo(BigInteger.ZERO) != 0) {
                gasLiveData.postValue(gasLimit * marketPrice)
            } else {
                gasLiveData.postValue(gasLimit * gasPrice)
            }
        }
    }
}