package io.iotex.iopay.home

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.contentEquals

class MemeViewModel(application: Application) : BaseLaunchVM(application) {

    val tokenListLiveData = MutableLiveData<List<TokenEntry>>()
    var category = ""
    fun getMemeList() {
        if (category.isEmpty()) return
        addLaunch {
            val token = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryCategoryToken(
                UserStore.getChainId(),
                category.lowercase()
            )
            token.forEach {
                val tokenCache =
                    AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().queryByAddress(
                        UserStore.getWalletAddress(),
                        UserStore.getChainId(),
                        it.address
                    )
                it.balance = tokenCache?.balance ?: ""
            }
            val sortList = TokenUtil.sortToken(token)
            if(!tokenListLiveData.value.contentEquals(sortList)){
                tokenListLiveData.postValue(sortList)
            }
        }
    }
}