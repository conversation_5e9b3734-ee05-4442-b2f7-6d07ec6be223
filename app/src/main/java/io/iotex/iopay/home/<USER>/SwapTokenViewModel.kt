package io.iotex.iopay.home.dialog

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.AppStore
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.repo.NativeTokenRepo
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.TokenUtil

class SwapTokenViewModel(application: Application) : BaseLaunchVM(application) {

    val defaultNetworkListLiveData = MutableLiveData<RPCNetwork>()
    val networkListLiveData = MutableLiveData<List<RPCNetwork>>()
    val allTokenListLiveData = MutableLiveData<Boolean>()
    val tokenListLiveData = MutableLiveData<List<TokenEntry>>()

    private val nativeTokenRepo by lazy {
        NativeTokenRepo()
    }

    val chainIds = arrayListOf(4689, 1, 56, 137)

    var search = ""
    var chainId = 0
    var isDepin = false
    var isMeMe = false

    fun getNetwork() {
        addLaunch {
            val rpcNetworks = ArrayList<RPCNetwork>()
            chainIds.forEach { chainId ->
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(chainId)?.let {
                        rpcNetworks.add(it)
                    }
            }
            networkListLiveData.postValue(rpcNetworks)
        }
    }

    fun getNetworkByChainId(chainId: Int) {
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId)?.let {
                    defaultNetworkListLiveData.postValue(it)
                }
        }
    }

    fun loadSortToken() {
        addLaunchNoCancel {
            val allToken = ArrayList<TokenEntry>()
            chainIds.forEach {
                val chainToken = ArrayList<TokenEntry>()
                val nativeToken =
                    nativeTokenRepo.getNetworkToken(UserStore.getWalletAddress(), it)
                nativeToken?.let {
                    chainToken.add(nativeToken)
                }
                chainToken.addAll(
                    AppDatabase.getInstance(Utils.getApp()).tokenDao().querySwapToken(it)
                )
                chainToken.forEach { token ->
                    if (token.address.isEmpty()) {
                        val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                            .queryWalletCache(UserStore.getWalletAddress(), token.chainId)
                        token.balance = walletCache?.balance ?: ""
                    } else {
                        val tokenCache = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao()
                            .queryByAddress(
                                UserStore.getWalletAddress(),
                                token.chainId,
                                token.address
                            )
                        token.balance = tokenCache?.balance ?: ""
                    }
                }
                allToken.addAll(chainToken)
                val sortTokens = TokenUtil.sortToken(chainToken)
                AppStore.sortToken[it] = sortTokens
            }
            AppStore.swapAllSortToken = TokenUtil.sortToken(allToken)
            allTokenListLiveData.postValue(true)
        }
    }

    fun getToken(update: Boolean = true) {
        addLaunch {
            var chainTokens: List<TokenEntry>? = null
            if (chainId == 0) {
                if (AppStore.swapAllSortToken != null) {
                    chainTokens = AppStore.swapAllSortToken
                } else {
                    if (update) loadSortToken()
                }
            } else if (chainId == Config.IOTEX_CHAIN_ID) {
                val sortToken = AppStore.sortToken[chainId]
                if (sortToken.isNullOrEmpty()) {
                    if (update) loadSortToken()
                } else {
                    chainTokens = if (isDepin) {
                        sortToken.filter { it.isDepinToken }
                    } else if (isMeMe) {
                        sortToken.filter { it.isMeme }
                    } else {
                        sortToken
                    }
                }

            } else {
                val sortToken = AppStore.sortToken[chainId]
                if (sortToken.isNullOrEmpty()) {
                    if (update) loadSortToken()
                } else {
                    chainTokens = sortToken.filter { it.chainId == chainId }
                }
            }
            val searchTokens = if (search.isEmpty()) {
                chainTokens
            } else {
                chainTokens?.filter {
                    it.symbol.contains(search, true) || it.address.contains(search, true)
                }
            }
            searchTokens?.let {
                tokenListLiveData.postValue(it)
            }
        }
    }
}