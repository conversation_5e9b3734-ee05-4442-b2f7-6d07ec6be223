package io.iotex.iopay.network

import android.content.Intent
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.RPCNetworkNode
import io.iotex.iopay.databinding.ActivityNetworkNodeSettingBinding
import io.iotex.iopay.network.dialog.AddNodeDialog
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.RefreshRpcNodeListEvent
import io.iotex.iopay.ui.RemoveWarningDialog
import io.iotex.iopay.ui.binder.RpcNetworkNodeBinder
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.NetworkUtils
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.util.extension.updateItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.uiThread

class NetworkNodeSettingActivity : BaseBindToolbarActivity<BaseViewModel,ActivityNetworkNodeSettingBinding>(R.layout.activity_network_node_setting) {

    private val mDefaultAdapter = MultiTypeAdapter()
    private val mCustomAdapter = MultiTypeAdapter()
    override fun initView() {
        EventBus.getDefault().register(this)
        setToolbarTitle(getString(R.string.network))
        mBinding.tvAddNode.setOnClickListener {
            addNode()
            FireBaseUtil.logFireBase("action_add_custom_node")
        }

        mBinding.llSwitchNetwork.setOnClickListener {
            startActivity(Intent(this, SwitchNetworkActivity::class.java))
        }

        mBinding.rvDefaultNode.adapter = mDefaultAdapter
        mDefaultAdapter.register(RPCNetworkNode::class, RpcNetworkNodeBinder().apply {
            onItemClick = { selectedNode ->
                selectRpcNode(selectedNode)
            }
        })

        mBinding.rvCustomNode.adapter = mCustomAdapter
        mCustomAdapter.register(RPCNetworkNode::class, RpcNetworkNodeBinder().apply {
            onItemClick = { selectedNode ->
                selectRpcNode(selectedNode)
            }

            onItemRemoveClick = {
                deleteNode(it)
            }

            onItemEditClick = {
                addNode(it)
            }
        })
    }

    override fun initData() {
        loadNetwork()
        lifecycleScope.launch {
            loadRpcNode()

            checkRpcUrl(mDefaultAdapter.items as List<RPCNetworkNode>, mCustomAdapter.items as List<RPCNetworkNode>)
        }
    }

    private fun loadNetwork(){
        doAsync {
            WalletHelper.getCurNetwork()?.let { network ->
                uiThread {
                    mBinding.ivLogo.loadSvgOrImage(network.logo,R.drawable.ic_network_default)
                    mBinding.tvName.text = network.name
                    mBinding.tvChain.text = getString(R.string.chain_id_dot) + network.chainId
                    mBinding.tvChain.isVisible = !WalletHelper.isBitcoinNetwork() && !WalletHelper.isSolanaNetwork()
                    mBinding.tvAddNode.isVisible = !WalletHelper.isBitcoinNetwork()
                    mBinding.tvCustom.isVisible = !WalletHelper.isBitcoinNetwork()
                }
            }
        }
    }

    private suspend fun loadRpcNode() {
        val chainId = WalletHelper.getCurChainId()
        val rpcNodes = withContext(Dispatchers.IO) {
            AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .queryRPCNodesByChainId(chainId)
        }
        val defaultNodes = rpcNodes.filter {
            it.immutable
        }.sortedBy {
            it.rpcStatus
        }

        mDefaultAdapter.items = defaultNodes
        mDefaultAdapter.notifyDataSetChanged()

        val customNodes = rpcNodes.filter {
            !it.immutable
        }.sortedBy {
            it.rpcStatus
        }

        mCustomAdapter.items = customNodes
        mCustomAdapter.notifyDataSetChanged()
    }

    private suspend fun checkRpcUrl(defaultNodes: List<RPCNetworkNode>, customNodes: List<RPCNetworkNode>) {
        defaultNodes.forEach {
            lifecycleScope.launch {
                val result = pingNode(it)
                mDefaultAdapter.updateItem(result) { node ->
                    node.rpcUrl == result.rpcUrl
                }
            }
        }
        customNodes.forEach {
            lifecycleScope.launch {
                val result = pingNode(it)
                mCustomAdapter.updateItem(result) { node ->
                    node.rpcUrl == result.rpcUrl
                }
            }
        }
    }

    private suspend fun pingNode(node: RPCNetworkNode): RPCNetworkNode {
        return withContext(Dispatchers.IO) {
            val host = NetworkUtils.getDomain(node.rpcUrl)
            val status = NetworkUtils.pingNetwork(host)
            node.rpcStatus = status.status
            val target = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .queryRPCNodeByUrl(WalletHelper.getCurChainId(), node.rpcUrl)
            target?.rpcStatus = status.status
            AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .update(target ?: node)
            return@withContext target ?: node
        }
    }

    private fun selectRpcNode(selectedNode: RPCNetworkNode) {
        lifecycleScope.launch {
            val chainId = WalletHelper.getCurChainId()
            val lastSelectedNode = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                    .queryRPCNodeActivated(chainId)
            }
            val nodes = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                    .queryRPCNodesByChainId(chainId)
            }
            "node: $nodes".i()
            if (lastSelectedNode != null) {
                lastSelectedNode.active = false
                updateNodeItem(lastSelectedNode)
            }
            selectedNode.active = true
            updateNodeItem(selectedNode)

            withContext(Dispatchers.IO) {
                if (lastSelectedNode != null) {
                    AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                        .update(lastSelectedNode)
                }
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                    .update(selectedNode)
                val network = WalletHelper.getCurNetwork() ?: return@withContext
                WalletHelper.switchNetwork(network)
            }
        }
    }

    private fun updateNodeItem(node: RPCNetworkNode) {
        if (node.immutable) {
            mDefaultAdapter.updateItem(node) {
                it.rpcUrl == node.rpcUrl
            }
        } else {
            mCustomAdapter.updateItem(node) {
                it.rpcUrl == node.rpcUrl
            }
        }
    }

    private fun deleteNode(node: RPCNetworkNode) {
        RemoveWarningDialog(getString(R.string.delete_network_tips))
            .apply {
                onConfirmClick = {
                    doDeleteNode(node) { result ->
                        if (result) {
                            dismiss()
                        }
                    }
                }
            }.show(supportFragmentManager,System.currentTimeMillis().toString())
    }

    private fun doDeleteNode(node: RPCNetworkNode, cb: (Boolean) -> Unit) {
        doAsync {
            val nodeList = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .queryRPCNodesByChainId(node.chainId)
            val defNodeList = nodeList.filter { it.immutable }
            val customNodeList = nodeList.filter { !it.immutable }

            if (defNodeList.isEmpty() && customNodeList.size <= 1) {
                uiThread {
                    getString(R.string.delete_node_error).toast()
                    cb.invoke(false)
                }
                return@doAsync
            }

            AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .delete(node)
            val list = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .queryRPCNodesByChainId(node.chainId)
                .filter { !it.immutable }
            uiThread {
                mCustomAdapter.items = list
                mCustomAdapter.notifyDataSetChanged()
                cb.invoke(true)
            }
        }
    }

    private fun addNode(node: RPCNetworkNode? = null) {
        AddNodeDialog(node)
            .show(supportFragmentManager,System.currentTimeMillis().toString())
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshNodeList(event: RefreshRpcNodeListEvent) {
        lifecycleScope.launch {
            loadRpcNode()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNetworkSwitchEvent(event: NetworkSwitchEvent) {
        lifecycleScope.launch {
            loadNetwork()
            loadRpcNode()
        }
    }
}