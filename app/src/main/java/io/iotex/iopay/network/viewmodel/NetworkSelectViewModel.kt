package io.iotex.iopay.network.viewmodel

import android.app.Application
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.RetrofitClient
import io.iotex.iopay.api.UrlApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.*
import io.iotex.iopay.support.eventbus.RefreshNetworkListEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.IOPAY_URL
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.WalletHelper
import org.greenrobot.eventbus.EventBus
import org.web3j.utils.Numeric
import java.math.BigInteger
import java.util.*

class NetworkSelectViewModel(application: Application) : BaseLaunchVM(application) {

    private val apiService by lazy {
        RetrofitClient.createApiService(IOPAY_URL, UrlApi::class.java)
    }

    val chanListLiveData = MutableLiveData<List<NetworkChain>?>()

    val rpcAvail = MutableLiveData<NetworkChain?>()

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        getChainList("")
    }

    fun getChainList(search: String) {
        addLaunch {
            val chanList = if (search.isEmpty()) {
                AppDatabase.getInstance(Utils.getApp()).networkChainDao().queryAll()
            } else {
                AppDatabase.getInstance(Utils.getApp()).networkChainDao().querySearch(search)
            }

            val rpcList = chanList?.filterNot {
                it.rpc.contains("INFURA_API_KEY")
            }
            if (search.isEmpty() && rpcList.isNullOrEmpty()) {
                getChainListFromNetWork()
            }
            chanListLiveData.postValue(rpcList)
        }
    }

    fun getChainListFromNetWorkOneTime(){
        addLaunch {
            val count = AppDatabase.getInstance(Utils.getApp()).networkChainDao().count()
            if(count == 0){
              getChainListFromNetWork()
            }
        }
    }

    fun getChainListFromNetWork() {
        addLaunchNoCancel {
            val list = apiService.requestCustomNetwork(Config.ALL_CHAIN_URL)
            val chanList = ArrayList<NetworkChain>()
            list.forEach { bean ->
                val rpc = bean.rpc?.filterNot { rpc -> rpc.contains("INFURA_API_KEY") }
                if (!rpc.isNullOrEmpty()) {
                    val networkChain = NetworkChain(
                        bean.name ?: "",
                        bean.icon,
                        bean.chain ?: "", bean.chainId ?: "",
                        rpc[0], bean.infoURL ?: "", bean.nativeCurrency?.symbol ?: ""
                    )
                    chanList.add(networkChain)
                    AppDatabase.getInstance(Utils.getApp()).networkChainDao()
                        .insertOrReplace(networkChain)
                }
            }
            chanListLiveData.postValue(chanList)
        }
    }

    private fun changeNetWork(networkChain: NetworkChain, node: RPCNetworkNode) {
        addLaunch {
            val id = TimeUtils.getNowMills().toString()
            val chainId = networkChain.chainId.trim().toInt()
            val rPCNetwork = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId)
            if (rPCNetwork != null) {
                WalletHelper.switchNetwork(rPCNetwork)
                return@addLaunch
            }
            val name = networkChain.name.trim()
            val rpcUrl = RegexUtils.getReplaceAll(networkChain.rpc.trim(), "/*$", "")
                .lowercase(Locale.getDefault())
            val explorerUrl = networkChain.infoURL.trim()
            val currencySymbol = networkChain.symbol.trim()
            val order = TimeUtils.getNowMills().toInt()
            val network = RPCNetwork(
                id, chainId, name, name, "", rpcUrl, explorerUrl, networkChain.icon,
                currencySymbol, currencySymbol, "", 18, "0",
                "", "", explorerUrl, MODE_MAIN, "#617AFF", "#617AFF",
                name, order, immutable = false, bg_color_start = "#617480",
                bg_color_end = "#3D477E"
            )
            AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .insertRPCNetworkDao(network)

            AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .insertRPCNetworkDao(node)

            EventBus.getDefault().post(RefreshNetworkListEvent())
            WalletHelper.switchNetwork(network)
        }
    }

    fun getRpcStatus(networkChain: NetworkChain) {
        iView?.showLoading()
        val rpcUrl = RegexUtils.getReplaceAll(networkChain.rpc.trim(), "/*$", "")
            .lowercase(Locale.getDefault())

        addLaunch(true,onError = {
            rpcAvail.postValue(null)
        }) {
            val id = TimeUtils.getNowMills().toString()
            val map = HashMap<String, String>().apply {
                put("id",id)
                put("jsonrpc","2.0")
                put("method","eth_chainId")
            }
            val start = TimeUtils.getNowMills()
            val result = apiService.requestEthChainId(rpcUrl,map).result?:""
            val end = TimeUtils.getNowMills()
            val chainId = BigInteger(Numeric.cleanHexPrefix(result), 16).toInt()
            val rpcStatus = DateTimeUtils.getRpcState(end-start)
            val node =
                RPCNetworkNode(chainId, rpcUrl, rpcStatus, immutable = true, active = true)
            if (node.chainId == -1) {
                rpcAvail.postValue(null)
            } else {
                changeNetWork(networkChain, node)
                rpcAvail.postValue(networkChain)
            }
        }
    }
}