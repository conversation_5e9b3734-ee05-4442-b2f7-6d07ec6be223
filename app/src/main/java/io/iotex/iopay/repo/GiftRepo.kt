package io.iotex.iopay.repo

import android.os.Bundle
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.api.IoPayPointTaskQuery
import io.iotex.base.RetrofitClient
import io.iotex.base.language.SP_LANGUAGE
import io.iotex.base.okHttpClient
import io.iotex.iopay.api.GiftApi
import io.iotex.iopay.base.BaseLaunchRepo
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.bean.GiftAction
import io.iotex.iopay.data.bean.GiftResponse
import io.iotex.iopay.data.bean.GiftUserInfo
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.PointTaskEntry
import io.iotex.iopay.support.eventbus.GiftUploadEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.Constant.AVATAR_THEME_BLOCKIES
import io.iotex.iopay.util.Constant.AVATAR_THEME_JAZZICONS
import io.iotex.iopay.util.Constant.AVATAR_THEME_ROBOTS
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant.SP_WALLET_AVATAR
import io.iotex.iopay.util.WalletHelper
import io.iotex.iotex.AppVersionQuery
import io.iotex.iotex.type.Order_by
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.bouncycastle.util.encoders.Base64
import org.greenrobot.eventbus.EventBus
import java.security.KeyFactory
import java.security.interfaces.RSAPrivateKey
import java.security.spec.PKCS8EncodedKeySpec
import java.util.Date

class GiftRepo : BaseLaunchRepo() {

    private val ioPayApolloClient: ApolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    private val apiService by lazy {
        RetrofitClient.createApiService(Config.GIFT_POINT_API, GiftApi::class.java)
    }

    companion object {
        init {
            System.loadLibrary("point")
        }
    }

    external fun key(): String

    fun idDeviceInvited(): Boolean {
        return false
    }

    fun getUpDatePoint(): Boolean {
        return false
    }

    fun hasSignInTask(): PointTaskEntry? {
        val pointTaskEntry =
            AppDatabase.getInstance(Utils.getApp()).pointTaskDao().queryByActionType(Config.GIFT_CENTER_SIGN_IN)
        return pointTaskEntry
    }

    fun hasSendTask(): PointTaskEntry? {
        val pointTaskEntry =
            AppDatabase.getInstance(Utils.getApp()).pointTaskDao().queryByTransaction(Config.GIFT_CENTER_SEND)
        return pointTaskEntry
    }

    fun hasSwapTask(): PointTaskEntry? {
        val pointTaskEntry =
            AppDatabase.getInstance(Utils.getApp()).pointTaskDao().queryByTransaction(Config.GIFT_CENTER_SWAP)
        return pointTaskEntry
    }

    fun hasStakeTask(): PointTaskEntry? {
        val pointTaskEntry =
            AppDatabase.getInstance(Utils.getApp()).pointTaskDao().queryByTransaction(Config.GIFT_CENTER_STAKE)
        return pointTaskEntry
    }

    fun hasDAppTask(): PointTaskEntry? {
        val pointTaskEntry =
            AppDatabase.getInstance(Utils.getApp()).pointTaskDao().queryByActionType(Config.GIFT_CENTER_BROWSER)
        return pointTaskEntry
    }

    suspend fun updateEvent(taskId: Int, address:String): GiftResponse? {
        if (!UserStore.getSwitchGiftCenter()) {
            return null
        }
        if (!WalletHelper.isEvmAddress(address)) return null
        if (Constant.currentWallet?.isWatch == true || Constant.currentWallet?.isSolanaPrivateWallet() == true) {
            return null
        }
        val giftAction = GiftAction(taskId, DeviceUtils.getUniqueDeviceId())
        var error = ""
        val result = kotlin.runCatching {
            apiService.requestGift("Bearer ${generateJWT(address)}", giftAction)
        }.onFailure {
            error = it.message?:""
        }.getOrNull()
        LogUtils.i("upload_event", "result：$result")
        if (result?.ok == true) {
            EventBus.getDefault().post(GiftUploadEvent(taskId))
        }
        if (result == null) {
            val bundle = Bundle()
            bundle.putString("address", address)
            bundle.putString("device_id", DeviceUtils.getUniqueDeviceId())
            bundle.putString("task_id", taskId.toString())
            bundle.putString("error", error)
            FireBaseUtil.logFireBase(FireBaseEvent.POINT_TASK_API_ERROR, bundle)
        }
        return result
    }

    suspend fun getGiftUserInfo(address: String): GiftUserInfo {
        delay(2000)
        return GiftUserInfo("6ygg", "1000")
    }

    fun getTaskRemote() {
        addLaunch {
            val ioPayPointTaskQuery = IoPayPointTaskQuery.builder().build()
            ioPayApolloClient.query(ioPayPointTaskQuery).await().data?.point_task()?.forEach {
                AppDatabase.getInstance(Utils.getApp()).pointTaskDao().insert(
                    PointTaskEntry(
                        it.id(),
                        it.action_type().toString(),
                        it.transaction().toString(),
                        it.point().toString(),
                        it.released()
                    )
                )
            }
        }
    }

    fun fetchAppVersion(callBack: ((AppVersionQuery.Version_control_android_2) -> Unit)? = null) {
        val appVersionQuery = AppVersionQuery.builder()
            .order_by(Order_by.DESC)
            .build()

        addLaunch {
            ioPayApolloClient.query(appVersionQuery).await().data
                ?.version_control_android_2()?.let {
                    if (it.isNotEmpty()) {
                        callBack?.invoke(it[0])
                    }
                }
        }
    }

    private fun getPrivateKeyFromRawResource(): RSAPrivateKey {
        val privateKeyPEM = key()
        val privateKeyBase64 = privateKeyPEM
            .replace("privateKeyPEM:", "")
            .replace("\n", "")

        val keyBytes = Base64.decode(privateKeyBase64)
        val keySpec = PKCS8EncodedKeySpec(keyBytes)
        val keyFactory = KeyFactory.getInstance("RSA")
        return keyFactory.generatePrivate(keySpec) as RSAPrivateKey
    }

    private suspend fun generateJWT(address: String): String {
        val rSAPrivateKey = withContext(Dispatchers.IO) {
            getPrivateKeyFromRawResource()
        }
        val algorithm = Algorithm.RSA256(null, rSAPrivateKey)
        return JWT.create()
            .withIssuer("iopay")
            .withSubject(address)
            .withExpiresAt(Date(System.currentTimeMillis() + 3600 * 1000)) // 1小时有效期
            .sign(algorithm)
    }

    fun getGiftCenterUrl(): String {
        val address = Constant.currentWallet?.address ?: ""
        val url = UserStore.getGiftCenterUrl().ifEmpty {
            Config.GIFT_CAMPAGIN_URL
        }
        val theme = if (UserStore.isDarkTheme()) {
            "dark"
        } else {
            "light"
        }
        val avatar =
            when (SPUtils.getInstance().getInt(SP_WALLET_AVATAR, AVATAR_THEME_ROBOTS)) {
                AVATAR_THEME_BLOCKIES -> {
                    "Blockies"
                }

                AVATAR_THEME_JAZZICONS -> {
                    "Jazzicons"
                }

                else -> {
                    "Robots"
                }
            }
        if (!WalletHelper.isEvmAddress(address)) {
            return "$url?theme=$theme"
        }
        val language = SPUtils.getInstance().getString(SP_LANGUAGE, "en")
        return "$url?theme=$theme&language=$language&avatar=$avatar&address=$address"
    }
}