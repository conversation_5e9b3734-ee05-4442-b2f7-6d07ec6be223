package io.iotex.iopay.repo

import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchRepo
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.data.db.WalletCache
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.wallet.home.bean.WalletStakeBean
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.web3.Web3Delegate
import org.greenrobot.eventbus.EventBus
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.generated.Uint32
import java.math.BigDecimal
import java.math.BigInteger
import kotlin.math.min

class NativeTokenRepo : BaseLaunchRepo() {

    private val bitcoinRepo by lazy { BitcoinRepo() }

    fun getNetworkToken(address: String, chainId: Int): TokenEntry? {
        val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
            .queryRPCNetworkByChainId(chainId)
        network?.let {
            val cache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(address, chainId)
            val token = TokenEntry(
                network.currencyId,
                chainId,
                "",
                network.currencyName,
                network.currencySymbol,
                network.currencyDecimals,
                network.logo,
                network.currencyPrice,
                true,
                "",
                false,
                0,
                network.explorer,
                false,
                isOfficial = true,
                "",
                network.price_change_24h,
                network.sparkline_in_7d,
                "",
            ).apply {
                balance = cache?.balance ?: ""
            }
            return token
        }
        return null
    }

    fun getAllNetworkToken(
        update: Boolean = true,
        wallet: Wallet? = null,
        callBackToken: ((List<TokenEntry>) -> Unit)? = null,
        callBackTotal: ((BigDecimal) -> Unit)? = null
    ) {
        addLaunch {
            val curWallet = wallet ?: Constant.currentWallet ?: return@addLaunch
            val tokens = ArrayList<TokenEntry>()
            val balanceMoney = ArrayList<BigDecimal>()
            val chains = if (curWallet.isAAWallet()) {
                arrayListOf(
                    Config.IOTEX_CHAIN_ID,
                )
            } else if (curWallet.isBitcoinWatchWallet()) {
                arrayListOf(
                    Config.BITCOIN_MAIN_CHAIN_ID,
                )
            } else if (curWallet.isSolanaPrivateWallet() || curWallet.isSolanaWatchWallet()) {
                arrayListOf(
                    Config.SOLANA_MAIN_CHAIN_ID,
                )
            } else if (curWallet.isEvmWatchWallet()) {
                arrayListOf(
                    Config.IOTEX_CHAIN_ID,
                    Config.ETH_CHAIN_ID,
                    Config.POLYGON_CHAIN_ID,
                    Config.BSC_CHAIN_ID,
                    Config.AVAX_CHAIN_ID,
                    Config.FTM_CHAIN_ID,
                    Config.ARB_CHAIN_ID,
                    Config.BASE_CHAIN_ID,
                    Config.LINEA_CHAIN_ID,
                    Config.GRAVITY_CHAIN_ID,
                )
            } else if (curWallet.isEvmPrivateWallet()) {
                arrayListOf(
                    Config.IOTEX_CHAIN_ID,
                    Config.ETH_CHAIN_ID,
                    Config.BITCOIN_MAIN_CHAIN_ID,
                    Config.POLYGON_CHAIN_ID,
                    Config.BSC_CHAIN_ID,
                    Config.AVAX_CHAIN_ID,
                    Config.FTM_CHAIN_ID,
                    Config.ARB_CHAIN_ID,
                    Config.BASE_CHAIN_ID,
                    Config.LINEA_CHAIN_ID,
                    Config.GRAVITY_CHAIN_ID,
                )
            } else {
                arrayListOf(
                    Config.IOTEX_CHAIN_ID,
                    Config.ETH_CHAIN_ID,
                    Config.BITCOIN_MAIN_CHAIN_ID,
                    Config.SOLANA_MAIN_CHAIN_ID,
                    Config.POLYGON_CHAIN_ID,
                    Config.BSC_CHAIN_ID,
                    Config.AVAX_CHAIN_ID,
                    Config.FTM_CHAIN_ID,
                    Config.ARB_CHAIN_ID,
                    Config.BASE_CHAIN_ID,
                    Config.LINEA_CHAIN_ID,
                    Config.GRAVITY_CHAIN_ID,
                )
            }

            val networks = ArrayList<RPCNetwork>()
            chains.forEach { chainId ->
                val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(chainId)
                network?.let {
                    val token = getNetworkToken(curWallet.getCurNetworkAddress(chainId), chainId)
                        ?: return@forEach
                    tokens.add(token)
                    val value =
                        TokenUtil.weiToTokenBN(token.balance, token.decimals.toLong())
                            .asBigDecimal()
                    balanceMoney.add(token.price.asBigDecimal() * value)
                    networks.add(network)
                }
            }
            if (update) {
                getNetworkTokenBalance(networks, curWallet){
                    getAllNetworkToken(false, curWallet, callBackToken, callBackTotal)
                }
            }
            callBackToken?.invoke(tokens)
            var moneyAll = BigDecimal.ZERO
            balanceMoney.forEach {
                moneyAll += it
            }
            callBackTotal?.invoke(moneyAll)
        }
    }

    private fun getNetworkTokenBalance(network: List<RPCNetwork>?, wallet: Wallet?,callback:()->Unit){
        addLaunch {
            network?.forEach {
                getNetworkTokenBalance(it, wallet)
            }
            callback.invoke()
        }
    }

    suspend fun getNetworkTokenBalance(
        network: RPCNetwork?, wallet: Wallet?,
    ): BigInteger? {
        if (network == null || wallet == null) return null
        return if (WalletHelper.isBitcoinNetwork(network.chainId)) {
            var btcBalance = BigInteger.ZERO
            wallet.bitcoinWallets.forEach {
                val balance = bitcoinRepo.getBitcoinBalance(it.bitcoinAddress) ?: BigInteger.ZERO
                btcBalance += balance
            }
            btcBalance
        } else if (WalletHelper.isSolanaNetwork(network.chainId)) {
            SolanaWeb3.getBalance(wallet.solanaWallet?.publicKeyBase58 ?: wallet.address, network.chainId)
        } else {
            Web3Delegate.getNetworkBalance(network.chainId, network.rpc, wallet.address)
        }
    }

    fun getWalletStake(
        address: String,
        chainId: Int,
    ): TokenEntry? {
        if (chainId != Config.IOTEX_CHAIN_ID && chainId != Config.IOTEX_TEST_CHAIN_ID) return null
        val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
            .queryWalletCache(address, chainId)
        getWalletStakeRemote(address, chainId) {
            if (address == UserStore.getWalletAddress() && chainId == UserStore.getChainId()
                && it.stake.toString() != walletCache?.stake
            ) {
                EventBus.getDefault().post(FavoriteOrDislikeERC20Event())
            }
        }
        if (walletCache?.stake.asBigDecimal().compareTo(BigDecimal.ZERO) == 0) {
            return null
        } else {
            val stakeToken = getNetworkToken(address, chainId)
            stakeToken?.balance =
                TokenUtil.toWei(walletCache?.stake ?: "", stakeToken?.decimals ?: 18).toString()
            stakeToken?.isStake = true
            return stakeToken
        }
    }

    private fun getWalletStakeRemote(
        address: String,
        chainId: Int,
        callBack: ((WalletStakeBean) -> Unit)? = null
    ) {
        addLaunch {
            var totalStaked: Double
            val function = Function(
                "compositeBucketsByVoterV3",
                listOf(
                    Address(address),
                    Uint32(0),
                    Uint32(1000)
                ),
                listOf()
            )
            val data = FunctionEncoder.encode(function)
            val result = Web3Delegate.executeCall(Config.CONTRACT_STAKE, data)
            result?.let {
                val length: Int = it.cleanHexPrefix().length
                val chunkCount: Int = (length + 64 - 1) / 64
                val chunks = mutableListOf<String>()
                for (i in 0 until chunkCount) {
                    val startIndex: Int = i * 64
                    val endIndex = min(startIndex + 64, length)
                    chunks.add(it.cleanHexPrefix().substring(startIndex, endIndex))
                }
                if (chunks.size > 2) {
                    chunks.removeAt(0)
                    chunks.removeAt(1)
                }
                val buckets = mutableListOf<MutableList<String>>()
                for (i in 0 until chunks.size / 15) {
                    buckets.add(chunks.subList(i * 15, (i + 1) * 15))
                }
                totalStaked = buckets.sumOf { bucket ->
                    TokenUtil.weiToTokenBN(BigInteger(bucket[2], 16).toString()).toDouble()
                }
                if (chainId != WalletHelper.getCurChainId()) return@addLaunch
                callBack?.invoke(
                    WalletStakeBean(
                        address,
                        chainId,
                        buckets.isEmpty(),
                        totalStaked,
                    )
                )
                var walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                    .queryWalletCache(address, chainId)
                if (walletCache == null) {
                    walletCache = WalletCache(
                        chainId,
                        address,
                        stake = totalStaked.toString()
                    )
                } else {
                    walletCache.stake = totalStaked.toString()
                }
                AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                    .insertOrUpdate(walletCache)
            }
        }

    }

}