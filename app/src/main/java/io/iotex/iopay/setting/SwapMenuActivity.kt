package io.iotex.iopay.setting

import android.app.Activity
import android.content.Intent
import android.view.MotionEvent
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.databinding.ActivitySwapMenuBinding
import io.iotex.iopay.reactnative.ReactNativeActivity
import io.iotex.iopay.reactnative.ReactScene
import io.iotex.iopay.setting.book.AddressBookActivity
import io.iotex.iopay.support.eventbus.MessageRedDotEvent
import io.iotex.iopay.support.eventbus.RenameWalletEvent
import io.iotex.iopay.support.eventbus.WalletAvatarChangeEvent
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.ViewPrivateKeyActivity
import io.iotex.iopay.wallet.aawallet.AAWalletChooserActivity
import io.iotex.iopay.wallet.add.NameWalletActivity
import io.iotex.iopay.wallet.add.PrivateKeyVerifyActivity
import io.iotex.iopay.wallet.add.WalletAddActivity
import io.iotex.iopay.wallet.add.WalletAddPrivateKeyActivity
import io.iotex.iopay.setting.dialog.InvitedCodeDialog
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.wallet.qrcode.IoScanQRCodeActivity
import io.iotex.iopay.wallet.receive.ReceiveActivity
import io.iotex.iopay.wallet.receive.ReceiveDetailActivity
import io.iotex.iopay.network.NetworkNodeSettingActivity
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity

class SwapMenuActivity :
    BaseBindActivity<SwapMenuViewModel, ActivitySwapMenuBinding>(R.layout.activity_swap_menu) {


    private val emptyWallet by lazy {
        intent?.getBooleanExtra(KEY_EMPTY_WALLET, false) ?: false
    }

    override fun initView() {
        EventBus.getDefault().register(this)
        mBinding.ivBack.setOnClickListener {
            finish()
            overridePendingTransition(0, R.anim.slide_out_left)
        }

        mBinding.ivNotice.setOnClickListener {
            val intent = Intent(this, ReactNativeActivity::class.java)
            intent.putExtra(
                ReactNativeActivity.REACT_COMPONENT_NAME,
                ReactScene.Notification.name
            )
            startActivity(intent)
        }

        mBinding.llAddWallet.setOnClickListener {
            if (mViewModel.recoveryLiveData.value.isNullOrEmpty()) {
                WalletAddActivity.startActivity(this)
            } else {
                WalletAddPrivateKeyActivity.startActivity(this)
            }
        }

        mBinding.ivEdit.setOnClickListener {
            NameWalletActivity.start(
                this,
                NameWalletActivity.ACTION_RENAME,
                mBinding.tvWalletName.text.toString(),
                title = Utils.getApp().getString(R.string.edit_wallet_name)
            )
        }

        mBinding.ivScan.setOnClickListener {
            IoScanQRCodeActivity.startActivity(this)
        }

        mBinding.llReceive.setOnClickListener {
            if (!UserStore.getAllNetwork()) {
                val addressType = Constant.currentWallet?.addressType ?: AddressType_Legacy
                ReceiveDetailActivity.startActivity(this, UserStore.getChainId(), addressType)
            } else {
                ReceiveActivity.startActivity(this)
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_MENU_RECEIVE)
        }
        mBinding.llBackUp.setOnClickListener {
            if (Constant.currentWallet?.isWatch == true) {
                ActivityUtils.getTopActivity()?.let {
                    WalletHelper.showWatchAddress(it)
                }
            } else {
                startActivity<ViewPrivateKeyActivity>()
            }
            FireBaseUtil.logFireBase(FireBaseEvent.NAVIGATION_TO_PRIVATE_KEY)
            PageEventUtil.logEvent(PageEventUtil.BACKUP)
        }

        mBinding.llAddressBook.setOnClickListener {
            AddressBookActivity.startActivity(this, true)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_ADDRESS_BOOK)
            PageEventUtil.logEvent(PageEventUtil.ADDRESSBOOK)
        }

        mBinding.llNetwork.setOnClickListener {
            startActivity<NetworkNodeSettingActivity>()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_NETWORK)
            PageEventUtil.logEvent(PageEventUtil.NETWORK)
        }

        mBinding.llGeneral.setOnClickListener {
            GeneralActivity.startActivity(this, emptyWallet)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_GENERAL)
            PageEventUtil.logEvent(PageEventUtil.GENERAL)
        }

        mBinding.llManageWallet.setOnClickListener {
            startActivity<ManageActionActivity>()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_MANAGER_WALLET)
        }

        mBinding.llGiftCode.setOnClickListener {
            InvitedCodeDialog().show(supportFragmentManager, System.currentTimeMillis().toString())
        }
        mBinding.llPrivate.setOnClickListener {
            startActivity<SecurityPrivacyActivity>()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_SECURITY_PRIVACY)
            PageEventUtil.logEvent(PageEventUtil.SECURITYPRIVACY)
        }

        mBinding.llAbout.setOnClickListener {
            startActivity<AboutIoPayActivity>()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_ABOUT)
            PageEventUtil.logEvent(PageEventUtil.ABOUTIOPAY)
        }

        mBinding.addLayout.mSelectorCreateWallet.setOnClickListener {
            NameWalletActivity.start(this, NameWalletActivity.CREATE_VIA_MNEMONIC)
        }
        mBinding.addLayout.clPriKeyWallet.setOnClickListener {
            PrivateKeyVerifyActivity.startActivity(this)
        }
        mBinding.addLayout.clAAWallet.setOnClickListener {
            startActivity<AAWalletChooserActivity>()
        }

        if (emptyWallet) {
            mBinding.addLayout.root.setVisible()
            mBinding.llWallet.setGone()
            mBinding.llBasic.setGone()
            mBinding.llNetwork.setGone()
            mBinding.llManageWallet.setGone()
            mBinding.llGiftCode.setGone()
            mBinding.llPrivate.setGone()
        } else {
            mBinding.addLayout.root.setGone()
        }
    }

    override fun initData() {
        mBinding.ivUpdateTag.isVisible = UserStore.getUpdateApp()
        mBinding.tvVersion.text = AppUtils.getAppVersionName()
        mViewModel.getWallet()
        mViewModel.getRecoveryWallet()
        mViewModel.walletLiveData.observe(this) {
            mBinding.ivWalletIcon.loadSvgOrImage(it?.avatar, R.drawable.icon_wallet_default)
            mBinding.tvWalletName.text = it?.alias
        }

        mViewModel.getDeviceInvite()
        mViewModel.deviceInviteLiveData.observe(this){
            mBinding.llGiftCode.isVisible = it
        }
        showNoticeRed()
    }

    private var initialX = 0f
    private var finalX = 0f
    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                initialX = event.rawX
            }

            MotionEvent.ACTION_MOVE -> {
                finalX = event.rawX
                val dx = finalX - initialX
                if (dx < (-10).dp2px()) {
                    onBackPressed()
                }
                initialX = finalX
            }
        }
        return super.dispatchTouchEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageRedDotEvent(event: MessageRedDotEvent) {
        showNoticeRed()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletAvatarChangeEvent(event: WalletAvatarChangeEvent) {
        mViewModel.getWallet()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRRenameWalletEvent(event: RenameWalletEvent) {
        mViewModel.getWallet()
    }

    private fun showNoticeRed() {
        mBinding.viewNoticeRed.isVisible = UserStore.getMessageCount() != 0
    }

    override fun onBackPressed() {
        finish()
        overridePendingTransition(0, R.anim.slide_out_left)
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    companion object {
        private const val KEY_EMPTY_WALLET = "key_empty_wallet"
        fun startActivity(context: Activity, emptyWallet: Boolean) {
            val intent = Intent(context, SwapMenuActivity::class.java)
            intent.putExtra(KEY_EMPTY_WALLET, emptyWallet)
            context.startActivity(intent)
            context.overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_bg)
        }
    }
}