package io.iotex.iopay.token

import android.app.Application
import android.net.Uri
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.api.CoingakoMarketQuery
import io.iotex.base.RetrofitClient
import io.iotex.base.language.SP_LANGUAGE
import io.iotex.base.okHttpClient
import io.iotex.iopay.R
import io.iotex.iopay.api.StockApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.bean.getStockList
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.DiscoverDApps
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.formatScale
import io.iotex.iopay.util.extension.toDisplayUnit
import io.iotex.iopay.wallet.home.bean.TokenDetailBean
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class TokenDetailViewModel(application: Application) : BaseLaunchVM(application) {

    val marketLiveData = MutableLiveData<List<OptionEntry>>()
    val launchDAppLiveData = MutableLiveData<DiscoverDApps>()
    val networkLiveData = MutableLiveData<RPCNetwork>()
    val balanceLiveData = MutableLiveData<String>()
    val stockLiveData = MutableLiveData<ArrayList<Float>?>()
    val questionLiveData = MutableLiveData<List<String>>()

    private val apolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    private val apiService by lazy {
        RetrofitClient.createApiService(Config.GIFT_POINT_API, StockApi::class.java)
    }

    fun getDAppTools(website: String) {
        addLaunch(false) {
            var list = AppDatabase.getInstance(getApplication()).discoverDapps()
                .queryByUrl(website)

            if (list.isNullOrEmpty()) {
                var host = Uri.parse(website).host
                if (host.isNullOrEmpty()) {
                    host = website
                }
                list = AppDatabase.getInstance(getApplication()).discoverDapps()
                    .queryLikeHost("https://$host")
                if (list?.size == 1) {
                    launchDAppLiveData.postValue(list[0])
                } else if ((list?.size ?: 0) > 1) {
                    list?.sortedWith { o1, o2 ->
                        (o1.id.toIntOrNull() ?: 0) - (o2.id.toIntOrNull() ?: 0)
                    }?.get(0)?.let {
                        launchDAppLiveData.postValue(it)
                    }
                }
            } else {
                launchDAppLiveData.postValue(list[0])
            }
        }
    }

    fun getMarketValue(id: String) {
        if (id.isEmpty()) return
        addLaunch {
            val query = CoingakoMarketQuery.builder().id(id).build()
            apolloClient.query(query).await().data?.coingeko_market()?.first()?.let {
                val marketOption = ArrayList<OptionEntry>()
                val totalVolume = it.total_volume().toString()
                if (totalVolume.asBigDecimal() > BigDecimal.ZERO) {
                    marketOption.add(
                        OptionEntry(
                            Utils.getApp().getString(R.string.market_value),
                            "$"+totalVolume.toDisplayUnit()
                        )
                    )
                }

                val dailyVolume = it.daily_volume_usd().toString()
                if (dailyVolume.asBigDecimal() > BigDecimal.ZERO) {
                    marketOption.add(
                        OptionEntry(
                            Utils.getApp().getString(R.string.total_transaction_volume),
                            dailyVolume.toDisplayUnit()
                        )
                    )
                }
                val present = kotlin.runCatching {
                    it.daily_volume_usd().toString().asBigDecimal()
                        .multiply(BigDecimal(100)) / it.market_cap().toString().asBigDecimal()
                }.getOrNull()?.formatScale(2)
                if (present.asBigDecimal() > BigDecimal.ZERO) {
                    marketOption.add(
                        OptionEntry(
                            Utils.getApp().getString(R.string.turnover_market_value),
                            "$present%"
                        )
                    )
                }
                val circulatingSupply = it.circulating_supply().toString()
                if (circulatingSupply.asBigDecimal() > BigDecimal.ZERO) {
                    marketOption.add(
                        OptionEntry(
                            Utils.getApp().getString(R.string.circular_supply),
                            circulatingSupply.toDisplayUnit()
                        )
                    )
                }
                val ath = it.ath().toString().asBigDecimal()
                if (ath > BigDecimal.ZERO) {
                    marketOption.add(
                        OptionEntry(
                            Utils.getApp().getString(R.string.all_time_high),
                            "$" + ath.formatScale()
                        )
                    )
                }
                val atl = it.atl().toString().asBigDecimal()
                if (atl > BigDecimal.ZERO) {
                    marketOption.add(
                        OptionEntry(
                            Utils.getApp().getString(R.string.record_low),
                            "$" + atl.formatScale()
                        )
                    )
                }

                val fullyDilutedValuation = it.fully_diluted_valuation().toString()
                if (fullyDilutedValuation.asBigDecimal() > BigDecimal.ZERO) {
                    marketOption.add(
                        OptionEntry(
                            Utils.getApp().getString(R.string.fully_diluted),
                            fullyDilutedValuation.toDisplayUnit()
                        )
                    )
                }

                marketLiveData.postValue(marketOption)
            }
        }
    }

    fun getBalance(tokenDetailBean: TokenDetailBean?) {
        if (tokenDetailBean == null) {
            balanceLiveData.postValue("")
            return
        }
        addLaunch {
            if (tokenDetailBean.address.isEmpty()) {
                val chainId = tokenDetailBean.chainId
                val cache =
                    AppDatabase.getInstance(Utils.getApp()).walletCacheDao().queryWalletCache(
                        Constant.currentWallet?.getCurNetworkAddress(chainId)
                            ?: UserStore.getWalletAddress(), chainId
                    )
                val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(chainId)
                val balance = TokenUtil.displayBalance(
                    TokenUtil.weiToTokenBN(
                        cache?.balance ?: "",
                        network?.currencyDecimals?.toLong() ?: 18L
                    )
                )
                balanceLiveData.postValue(balance)
                return@addLaunch
            }

            if (tokenDetailBean.isVisitorToken) {
                balanceLiveData.postValue(tokenDetailBean.symbol)
            }

            val wallet = WalletHelper.getCurWallet()
            val chainId = WalletHelper.getCurChainId()
            val erc20 = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).tokenCacheDao()
                    .queryByAddress(wallet?.address ?: "", chainId, tokenDetailBean.address)
            }
            val token = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).tokenDao()
                    .queryByAddress(chainId, tokenDetailBean.address)
            }
            if (erc20 != null) {
                var balanceText = TokenUtil.displayBalance(
                    TokenUtil.weiToTokenBN(
                        erc20.balance,
                        token?.decimals?.toLong() ?: 18
                    )
                )
                balanceLiveData.postValue(balanceText)
                val balance = if (WalletHelper.isSolanaNetwork()) {
                    SolanaWeb3.getTokenBalance(erc20.pubkey)
                } else {
                    val balanceList = Web3Delegate.getErc20Balance(erc20.address)
                    if (balanceList.isNotEmpty()) balanceList[0].toString() else null
                }
                if (erc20.balance != balance && balance != null) {
                    erc20.balance = balance
                    AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(erc20)
                    balanceText = TokenUtil.displayBalance(
                        TokenUtil.weiToTokenBN(
                            erc20.balance,
                            token?.decimals?.toLong() ?: 18
                        )
                    )
                    balanceLiveData.postValue(balanceText)
                }
            } else {
                balanceLiveData.postValue("")
            }

        }
    }

    fun getTokenStock(tokenDetailBean:TokenDetailBean?,start:Long,end:Long){
        if (tokenDetailBean==null) return
        addLaunch(onError = {
            stockLiveData.postValue(null)
        })  {
            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryRPCNetworkByChainId(tokenDetailBean.chainId)
            val map = HashMap<String, Any>().apply {
                put("id",tokenDetailBean.id)
                put("platform", network?.platform?:"")
                if(tokenDetailBean.address.isNotEmpty()){
                    put("contract", tokenDetailBean.address)
                }
                put("start",start)
                put("end",end)
            }
            val resp = apiService.requestStock(map)
            val list = getStockList(resp.string())
            stockLiveData.postValue(list)
        }
    }

    fun getQuestion(symbol:String?){
        if (symbol.isNullOrEmpty()) return
        addLaunch {
            val lang = SPUtils.getInstance().getString(SP_LANGUAGE, "en")
            val question = apiService.requestQuestion(mapOf(Pair("symbol",symbol),Pair("lang",lang)))
            questionLiveData.postValue(question)
        }
    }
}