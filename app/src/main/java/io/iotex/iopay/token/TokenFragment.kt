package io.iotex.iopay.token

import android.view.View
import androidx.core.view.isVisible
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.databinding.FragmentTokenBinding
import io.iotex.iopay.home.item.HomeTokenBinder
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.support.eventbus.MainCardEyeRefresh
import io.iotex.iopay.support.eventbus.MainPullRefresh
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.SwitchAllNetworkEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.support.eventbus.TokenLoadFinishEvent
import io.iotex.iopay.ui.RemoveTokenYesDialog
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.wallet.home.bean.fromToken
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class TokenFragment :
    BaseBindFragment<TokenListViewModel, FragmentTokenBinding>(R.layout.fragment_token) {
    private val mAdapter = MultiTypeAdapter()

    override fun useEventBus(): Boolean {
        return true
    }

    override fun initView() {
        val binder = HomeTokenBinder(true).apply {
            onItemClickCallback = {
                if (it.isStake) {
                    SchemeUtil.goto(requireContext(), SchemeUtil.SCHEME_STAKE_PAGE)
                } else {
                    TokenDetailActivity.startActivity(
                        requireActivity(),
                        fromToken(it)
                    )
                    if (it.isDepinToken) {
                        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_TOKEN_LIST_DEPIN_TOKEN)
                    }
                    if (UserStore.getAllNetwork()) {
                        mViewModel.switchNetworkOnAllNet(
                            it.chainId
                        )
                    }
                }
            }
        }
        mAdapter.register(TokenEntry::class, binder)
        mBinding.mRvErc20.adapter = mAdapter

        mBinding.mLlEmpty.setOnClickListener {
            //nothing. just cover click
        }
    }

    override fun initEvent() {
        mViewModel.checkChainTokenList()
        mViewModel.queryLikeTokenList(true)
        mViewModel.getBalanceToken()
        mViewModel.tokenListLiveData.observe(this) {
            mBinding.shimmer.shimmerLayout.setGone()
            if (it.isNullOrEmpty()) {
                mBinding.mRvErc20.isVisible = false
                if (WalletHelper.isBitcoinNetwork()) {
                    mBinding.mLlEmpty.visibility = View.GONE
                    mBinding.mLlEmptyBitcoin.visibility = View.VISIBLE
                } else {
                    mBinding.mLlEmpty.visibility = View.VISIBLE
                    mBinding.mLlEmptyBitcoin.visibility = View.GONE
                }
            } else {
                mBinding.mLlEmpty.visibility = View.GONE
                mBinding.mRvErc20.visibility = View.VISIBLE
                mBinding.mLlEmptyBitcoin.visibility = View.GONE
                mAdapter.items = it
                mAdapter.notifyDataSetChanged()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: TokenLoadFinishEvent) {
        mViewModel.getBalanceToken()
        mViewModel.queryLikeTokenList(true)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFavoriteOrDislikeERC20Event(event: FavoriteOrDislikeERC20Event) {
        mViewModel.queryLikeTokenList()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNetworkSwitchEvent(event: NetworkSwitchEvent) {
        mViewModel.cancelAllJob()
        mViewModel.checkChainTokenList()
        mViewModel.getBalanceToken()
        mViewModel.queryLikeTokenList(true)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSwitchAllNetworkEvent(event: SwitchAllNetworkEvent) {
        mViewModel.getAllNetworkTokens()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMainCardEyeRefresh(event: MainCardEyeRefresh) {
        mAdapter.notifyDataSetChanged()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletSwitchEvent(event: SwitchWalletEvent) {
        mViewModel.cancelAllJob()
        mViewModel.getBalanceToken(true)
        mViewModel.queryLikeTokenList(true)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMainPullRefresh(event: MainPullRefresh) {
        mViewModel.checkChainTokenList(true)
        mViewModel.queryLikeTokenList(true)
        mViewModel.getBalanceToken(true)
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshActionListEvent(event: ActionRefreshEvent) {
        mViewModel.queryLikeTokenList(true)
    }
}