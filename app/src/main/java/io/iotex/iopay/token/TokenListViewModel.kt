package io.iotex.iopay.token

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.walletconnect2.WC2Config.IOPAY_LOGO
import io.iotex.base.RetrofitClient
import io.iotex.iopay.api.IoPayRestApi
import io.iotex.iopay.api.StockApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.bean.TokenListResult.SourceToken
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.LIKE_STATUS_LIKE
import io.iotex.iopay.data.db.LIKE_STATUS_UNLIKE
import io.iotex.iopay.data.db.TokenCacheEntry
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.repo.NativeTokenRepo
import io.iotex.iopay.support.eventbus.AllBalanceEvent
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.support.eventbus.SwitchAllNetworkEvent
import io.iotex.iopay.support.eventbus.TokenLoadFinishEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.FileUtils
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.contentEquals
import io.iotex.iopay.util.extension.splitList
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import java.math.BigDecimal
import java.math.BigInteger

class TokenListViewModel(application: Application) : BaseLaunchVM(application) {

    private val apiRestService by lazy {
        RetrofitClient.createApiService(Config.IOPAY_REST_API_URL, IoPayRestApi::class.java)
    }

    private val apiService by lazy {
        RetrofitClient.createApiService(Config.GIFT_POINT_API, StockApi::class.java)
    }

    private val tokenRepo by lazy { NativeTokenRepo() }

    val tokenListLiveData = MutableLiveData<List<TokenEntry>>()
    val allTokenListLiveData = MutableLiveData<List<TokenEntry>>()
    fun getAllToken(search: String) {
        addLaunch {
            val chainId = UserStore.getChainId()
            val list = if (WalletHelper.isValidAddress(search)) {
                val token = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                    .queryByAddress(chainId, search.lowercase())
                if (token == null) emptyList() else arrayListOf(token)
            } else {
                AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByLike(chainId, search)
            }
            val tokenCacheList = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().queryByWallet(UserStore.getWalletAddress(),chainId)
            tokenCacheList.forEach { tokenCache ->
                list.find { it.address == tokenCache.address }?.apply {
                    status = tokenCache.likeStatus
                    balance = tokenCache.balance
                }
            }
            val sortList = TokenUtil.sortToken(list)
            allTokenListLiveData.postValue(TokenUtil.sortCustomToken(sortList))
        }
    }

    fun likeOrUnlikeToken(tokenEntry: TokenEntry) {
        addLaunch {
            var tokenCache = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().queryByAddress(
                UserStore.getWalletAddress(),
                tokenEntry.chainId,
                tokenEntry.address
            )
            if(tokenCache == null){
                tokenCache = TokenCacheEntry(
                    UserStore.getWalletAddress(),
                    UserStore.getChainId(),
                    tokenEntry.address,
                )
            }
            tokenCache.apply {
                likeStatus = if (tokenCache.likeStatus == LIKE_STATUS_LIKE) {
                    LIKE_STATUS_UNLIKE
                } else {
                    LIKE_STATUS_LIKE
                }
                AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(tokenCache)
                EventBus.getDefault().post(FavoriteOrDislikeERC20Event())
            }
        }
    }

    fun deleteToken(tokenEntry: TokenEntry) {
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).tokenDao().delete(tokenEntry)
        }
    }

    private suspend fun getErc20Balance(list: List<TokenEntry>) {
        withContext(Dispatchers.IO) {
            val tokeCacheList = ArrayList<TokenCacheEntry>()
            list.splitList(10).forEach { entryList ->
                val contractList = entryList.map {
                    it.address.toEvmAddress()
                }
                val balanceList = Web3Delegate.getErc20Balance(*contractList.toTypedArray())
                if (balanceList.size == entryList.size) {
                    var update = false
                    entryList.forEachIndexed { index, token ->
                        val address = UserStore.getWalletAddress()
                        //time too long. token maybe STATUS_UNLIKE. query again.
                        var tokeCache = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao()
                            .queryByAddress(address, token.chainId, token.address)
                        if (tokeCache == null && balanceList[index].toString()
                                .asBigDecimal() != BigDecimal.ZERO
                        ) {
                            tokeCache = TokenCacheEntry(
                                address,
                                token.chainId,
                                token.address,
                                balanceList[index].toString(),
                                LIKE_STATUS_LIKE
                            )
                            update = true
                        }
                        if (address == tokeCache?.walletAddress && tokeCache.chainId == WalletHelper.getCurChainId()) {
                            tokeCache.balance = balanceList[index].toString()
                            if ((balanceList[index]
                                    ?: BigInteger.ZERO) > BigInteger.ZERO && tokeCache.likeStatus != LIKE_STATUS_UNLIKE
                            ) {
                                tokeCache.likeStatus = LIKE_STATUS_LIKE
                            }
                            tokeCacheList.add(tokeCache)
                        }
                    }
                    if (update) {
                        AppDatabase.getInstance(Utils.getApp()).tokenCacheDao()
                            .insertOrReplace(*tokeCacheList.toTypedArray())
                        EventBus.getDefault().post(FavoriteOrDislikeERC20Event())
                        tokeCacheList.clear()
                    }
                }
            }

            AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(*tokeCacheList.toTypedArray())
        }
    }

    private var loading = false
    private val actions = mutableMapOf<String, Boolean>()
    fun getBalanceToken(refresh: Boolean = false) {
        addLaunch(onError = {
            loading = false
        }) {
            if (loading) return@addLaunch
            loading = true
            kotlin.runCatching {
                val address = UserStore.getWalletAddress()
                val chainId = WalletHelper.getCurChainId()
                if (WalletHelper.isSolanaNetwork()) {
                    SolanaWeb3.getTokenAccountsByOwner()
                } else {
                    val tokenList = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByChain(chainId)
                    if (actions["$address$chainId${tokenList.size}"] == true && !refresh) {
                        return@addLaunch
                    }
                    getErc20Balance(tokenList)
                    actions["$address$chainId${tokenList.size}"] = true
                }
            }
            loading = false
        }
    }

    fun queryLikeTokenList(updateBalance: Boolean = false) {
        if (UserStore.getAllNetwork()) {
            getAllNetworkTokens()
            return
        }
        var error = false
        addLaunch(onError = {
            if (!error) queryLikeTokenList(false)
            error = true
        }) {
            val wallet = WalletHelper.getCurWallet()
            val network = WalletHelper.getCurNetwork()
            wallet?.address?.let { address ->
                val list = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao()
                    .queryByStatus(address, WalletHelper.getCurChainId(), LIKE_STATUS_LIKE)
                val tokens = list.mapNotNull {
                    AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(it.chainId, it.address)
                        ?.apply {
                            balance = it.balance
                        }
                }
                var sortTokens = TokenUtil.sortToken(tokens).toMutableList()
                if (UserStore.getHideAssetsCheck()) {
                    sortTokens =
                        sortTokens.filter { it.displayValue.asBigDecimal() > BigDecimal.ONE }
                            .toMutableList()
                }
                var moneyAll = BigDecimal.ZERO
                sortTokens.forEach {
                    moneyAll += it.displayValue.asBigDecimal()
                }
                val stakeToken = tokenRepo.getWalletStake(wallet.address,WalletHelper.getCurChainId())
                if (stakeToken != null) {
                    sortTokens.add(0, stakeToken)
                    moneyAll += stakeToken.displayValue.asBigDecimal()
                }
                val networkToken = tokenRepo.getNetworkToken(wallet.getCurNetworkAddress(WalletHelper.getCurChainId()), WalletHelper.getCurChainId())
                if (networkToken != null) {
                    sortTokens.add(0, networkToken)
                    moneyAll += networkToken.displayValue.asBigDecimal()
                }
                if (UserStore.getAllNetwork() || wallet.address != UserStore.getWalletAddress() || network?.chainId != UserStore.getChainId()) return@addLaunch
                if(!tokenListLiveData.value.contentEquals(sortTokens)){
                    tokenListLiveData.postValue(sortTokens)
                    EventBus.getDefault().post(AllBalanceEvent(moneyAll))
                }
                if (updateBalance) {
                    kotlin.runCatching { tokenRepo.getNetworkTokenBalance(network, wallet) }
                    if (WalletHelper.isSolanaNetwork()) {
                        kotlin.runCatching { getSolanaSplBalance(list) }
                    } else {
                        kotlin.runCatching { getErc20Balance(tokens) }
                    }
                    kotlin.runCatching { refreshTokenPrice(tokens) }
                    queryLikeTokenList(false)
                }
            }
        }
    }

    private suspend fun refreshTokenPrice(list: List<TokenEntry>){
        list.forEach {
            val tokenPrice = apiService.queryTokenPrice(it.id)
            if (tokenPrice.price != it.price) {
                it.price = tokenPrice.price
                AppDatabase.getInstance(Utils.getApp()).tokenDao().insertOrReplace(it)
            }
        }
    }

    private suspend fun getSolanaSplBalance(list: List<TokenCacheEntry>) {
        list.map {
            async { SolanaWeb3.getTokenBalance(it.pubkey) }
        }.map {
            it.await()
        }.mapIndexed { index, balance ->
            if (balance != null) {
                list[index].balance = balance
                AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(list[index])
            }
        }
    }

    fun getAllNetworkTokens() {
        addLaunch {
            val wallet = WalletHelper.getCurWallet()
            val network = WalletHelper.getCurNetwork()
            tokenRepo.getAllNetworkToken(callBackToken = { tokens ->
                if (!UserStore.getAllNetwork() || wallet?.address != UserStore.getWalletAddress() || network?.chainId != UserStore.getChainId()) return@getAllNetworkToken
                if(!tokenListLiveData.value.contentEquals(tokens)){
                    tokenListLiveData.postValue(tokens)
                }
            }, callBackTotal = {
                if (!UserStore.getAllNetwork() || wallet?.address != UserStore.getWalletAddress() || network?.chainId != UserStore.getChainId()) return@getAllNetworkToken
                EventBus.getDefault().post(AllBalanceEvent(it))
            })
        }
    }

    fun loadTokenFromJson() {
        addLaunchNoCancel {
            val load = UserStore.getLoadTokenFromJson()
            if (load) return@addLaunchNoCancel
            arrayListOf(4689, 1, 56, 137, 19998).forEach { chainId ->
                val tokens =
                    AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByChain(chainId)
                if (tokens.isEmpty()) {
                    val json = FileUtils.readJsonFromAssets("token_$chainId.json")
                    val type = GsonUtils.getListType(SourceToken::class.java)
                    val source = GsonUtils.fromJson<List<SourceToken>>(json, type)
                    saveToken(chainId, source)
                }
            }
            UserStore.setLoadTokenFromJson(true)
            EventBus.getDefault().post(TokenLoadFinishEvent())
        }
    }

    fun checkChainTokenList(update: Boolean = false,chainId:Int = UserStore.getChainId()) {
        addLaunchNoCancel {
            val tokenListOld = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByChain(chainId)
            if (tokenListOld.isEmpty() || update) {
                val networkEntry = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(chainId)
                val platform = if (networkEntry?.platform.isNullOrBlank()) {
                    when (chainId) {
                        4689 -> "iotex"
                        1 -> "ethereum"
                        56 -> "binance-smart-chain"
                        137 -> "polygon-pos"
                        else -> null
                    }
                } else {
                    networkEntry?.platform
                } ?: return@addLaunchNoCancel
                apiService.queryTokenList(platform)?.let {
                    saveToken(chainId, it)
                }
            }
            EventBus.getDefault().post(TokenLoadFinishEvent())
        }
    }

    private fun saveToken(chainId: Int, tokenBeanList:List<SourceToken>?){
        val updateTime = System.currentTimeMillis().toString()
        if (tokenBeanList.isNullOrEmpty()) {
            UserStore.setTokenEmpty(UserStore.getChainId(), true)
            return
        }
        val tokenList = ArrayList<TokenEntry>()
        tokenBeanList.forEach { token ->
            kotlin.runCatching {
                val isValid = WalletHelper.isValidAddress(token.contract.toString())
                if (!isValid) return@forEach
                val web3Address = WalletHelper.convertWeb3Address(token.contract.toString())
                val contract = if (WalletHelper.isSolanaNetwork(chainId))
                    web3Address
                else web3Address.lowercase()

                val oldToken =
                    AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(chainId, contract)
                val decimals =
                    if (WalletHelper.isSolanaNetwork(chainId) && oldToken != null) oldToken.decimals
                    else token.decimals?.asBigDecimal()?.toBigInteger()?.toInt() ?: 18
                val logo = if(token.symbol.equals("IOTX",true)){
                    IOPAY_LOGO
                } else {
                    token.logo ?: ""
                }
                val tokenEntry = TokenEntry(
                    token.id?:"",
                    chainId,
                    contract,
                    token.name ?: "",
                    token.symbol ?: "",
                    decimals,
                    logo,
                    oldToken?.price?.ifEmpty { token.current_price ?: "" }?:"",
                    oldToken?.isTop ?: false,
                    oldToken?.riskStatus ?: "",
                    false,
                    token.weight ?: 0,
                    token.website ?: "",
                    token.is_depin_token ?: false,
                    token.is_official ?: false,
                    updateTime,
                    token.price_change_24h.toString(),
                    token.sparkline_in_7d.toString(),
                    token.rank_point ?: "",
                    token.tags.toString(),
                    token.custom?.meme ?: false,
                    token.custom?.robotpump ?: false,
                    token.custom?.mimo_disabled ?: false,
                    token.custom?.category.toString().lowercase(),
                )
                tokenList.add(tokenEntry)
            }
            AppDatabase.getInstance(Utils.getApp()).tokenDao().insertOrReplace(*tokenList.toTypedArray())
        }
        AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByChain(chainId).forEach {
            if (DateTimeUtils.formatYMD(
                    it.updateTime.toLongOrNull() ?: 0L
                ) != DateTimeUtils.formatYMD(updateTime.toLongOrNull() ?: 0L)
                && it.updateTime != Config.TYPE_CUSTOM
            ) {
                AppDatabase.getInstance(Utils.getApp()).tokenDao().delete(it)
            }
        }
    }

    fun switchNetworkOnAllNet(chainId: Int) {
        addLaunch {
            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId)
            network?.let {
                WalletHelper.switchNetwork(network)
                UserStore.setAllNetwork(true)
                EventBus.getDefault().post(SwitchAllNetworkEvent())
            }
        }
    }
}