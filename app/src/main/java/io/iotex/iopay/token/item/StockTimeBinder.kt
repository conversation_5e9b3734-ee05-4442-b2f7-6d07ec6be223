package io.iotex.iopay.token.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemStockTimeBinding
import java.util.Calendar

class StockTimeBinder :
    ItemViewBinder<String, BaseBindVH<ItemStockTimeBinding>>() {

    companion object {
        data class StockTime(
            val start: Long,
            val end: Long
        )

        fun getPositionTime(position: Int): StockTime {
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val day = 24 * 3600 * 1000L
            var start = calendar.time.time
            var end = calendar.time.time
            when (position) {
                0 -> {
                    end = calendar.time.time
                    start = calendar.time.time - day
                }

                1 -> {
                    end = calendar.time.time
                    start = calendar.time.time - 7 * day
                }

                2 -> {
                    end = calendar.time.time
                    start = calendar.time.time - 30 * day
                }

                3 -> {
                    end = calendar.time.time
                    start = calendar.time.time - 365 * day
                }
            }
            return StockTime(start / 1000, end / 1000)
        }
    }

    var onItemClick: ((StockTime) -> Unit)? = null
    private var selectPosition = 0

    override fun onBindViewHolder(holder: BaseBindVH<ItemStockTimeBinding>, item: String) {
        holder.bind.tvItem.text = item
        if (selectPosition == holder.adapterPosition) {
            holder.bind.tvItem.setBackgroundResource(R.drawable.shape_4d855eff_r4)
        } else {
            holder.bind.tvItem.setBackgroundResource(0)
        }
        holder.bind.root.setOnClickListener {
            selectPosition = holder.adapterPosition
            onItemClick?.invoke(getPositionTime(holder.adapterPosition))
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemStockTimeBinding> {
        return BaseBindVH(ItemStockTimeBinding.inflate(inflater, parent, false))
    }
}