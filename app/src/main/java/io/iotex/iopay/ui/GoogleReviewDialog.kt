package io.iotex.iopay.ui

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import io.iotex.iopay.R
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil

class GoogleReviewDialog(
    private val context: Context,
    private val cancelAction: () -> Unit,
    private val confirmAction: () -> Unit
) {

    private var dialog: PopupWindow? = null

    fun show() {
        val content = LayoutInflater.from(context).inflate(R.layout.dialog_google_review, null)
        dialog = PopupWindow(content, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT).apply {
            this.showAtLocation(content, Gravity.CENTER, 0, 0)
        }
        content.findViewById<View>(R.id.btn_no).setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SCORE_IOPAY_CLICK_CANCEL)
            confirmAction()
            dialog?.dismiss()
        }
        content.findViewById<View>(R.id.iv_close).setOnClickListener {
            cancelAction()
            dialog?.dismiss()
        }

        content.findViewById<View>(R.id.btn_yes).setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SCORE_IOPAY_CLICK_CONFIRM)
            confirmAction()
            dialog?.dismiss()
        }
    }
}