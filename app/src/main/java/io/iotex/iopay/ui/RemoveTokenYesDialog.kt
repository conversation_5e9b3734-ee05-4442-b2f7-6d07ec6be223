package io.iotex.iopay.ui

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupWindow
import androidx.appcompat.widget.AppCompatButton
import io.iotex.iopay.R

class RemoveTokenYesDialog(
    private val context: Context,
    private val confirmAction: () -> Unit
) {

    private var dialog: PopupWindow? = null

    fun show() {
        val content = LayoutInflater.from(context).inflate(R.layout.dialog_remove_token_yes, null)
        dialog = PopupWindow(content, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT).apply {
            this.showAtLocation(content, Gravity.CENTER, 0, 0)
        }

        content.findViewById<AppCompatButton>(R.id.btn_yes).setOnClickListener {
            dialog?.dismiss()
        }
        content.findViewById<ImageView>(R.id.iv_close).setOnClickListener {
            dialog?.dismiss()
        }

        dialog?.setOnDismissListener {
            confirmAction()
        }
    }
}