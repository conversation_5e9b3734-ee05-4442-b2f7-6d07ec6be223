package io.iotex.iopay.util


object Config {

    const val IOTEX_RPC_URL = "https://babel-api.mainnet.iotex.io"
    const val IOTEX_CHAIN_ID = 4689
    const val BITCOIN_TEST_CHAIN_ID = 999
    const val BITCOIN_MAIN_CHAIN_ID = 1999
    const val SOLANA_TEST_CHAIN_ID = 19999
    const val SOLANA_MAIN_CHAIN_ID = 19998
    const val BITCOIN_TEST_NETWORK = "testnet"
    const val BITCOIN_MAIN_NETWORK = "livenet"
    const val ETH_CHAIN_ID = 1
    const val BSC_CHAIN_ID = 56
    const val POLYGON_CHAIN_ID = 137
    const val IOTEX_TEST_CHAIN_ID = 4690
    const val IOTEX_NIGHT_CHAIN_ID = 4691
    const val AVAX_CHAIN_ID = 43114
    const val FTM_CHAIN_ID = 250
    const val ARB_CHAIN_ID = 42161
    const val BASE_CHAIN_ID = 8453
    const val LINEA_CHAIN_ID = 59144
    const val GRAVITY_CHAIN_ID = 1625
    const val IOTEX_NAME = "IoTeX"
    const val DEFAULT_METHOD_NAME = "Execution"
    const val TYPE_CUSTOM = "type_custom"
    const val HTTPS_SCHEME_HEAR = "https://"

    const val SWITCH_GIFT_CENTER = "gift_center"
    const val SWITCH_BINO_AI = "bino_ai"
    const val SWITCH_BUCKET_V3 = "bucket_v3"
    const val SWITCH_RN_FAKE_DEPIN = "rn_fake_depin_page"
    const val GIFT_CENTER_SIGN_IN = "sign_in"
    const val GIFT_CENTER_SEND = "send"
    const val GIFT_CENTER_SWAP = "swap"
    const val GIFT_CENTER_STAKE = "stake"
    const val GIFT_CENTER_BROWSER = "browser"

    const val SIGNATURE_MD5 = "EF:6A:B0:3F:3D:90:5B:A4:B3:CA:52:85:F1:44:A1:87"

    const val FioApi = "https://fio.blockpane.com/v1/chain"

    const val STAKE_RATE = "6-11%"

    const val stakeGatewayUrl = "https://nsv3-hasura.iotex.io/v1/graphql"
    const val IoPayUrl = "https://api.iopay.me/v1/graphql"

    const val CONTRACT_STAKE = "******************************************"
    const val CONTRACT_STAKE_NFT = "******************************************"
    const val CONTRACT_STAKE_NFT_V2 = "0x8ee521d2179576bcc4bd33a00904e96a11678052"
    const val CONTRACT_STAKE_NFT_V3 = "0x92b8a065cA39218dB5a6ee1181B86Dc60FF1471f"

    const val POLICY_URL = "https://iotex.io/policy?type=privacy"

    const val FEATURE_REQUESTS_URL = "https://feedback.iopay.me/feature-requests"

    const val TERM_URL = "https://iotex.io/policy?type=terms"

    const val FEEDBACK_EMAIL = "<EMAIL>"

    const val TRANSLATION_CONTRIBUTORS_URL = "https://iopay.me/translation-contributors"

    const val TRANSFER_TIP_URL = "https://iopay.me/exchange-support"

    const val ALL_CHAIN_URL = "https://chainid.network/chains.json"

    const val NFT_QUERY_URL = "https://nft.iopay.me/"

    const val BIP_44_URL = "https://github.com/bitcoin/bips/blob/master/bip-0044.mediawiki"

    const val GEO_W3BSTREAM_URL = "https://geo.w3bstream.com/"

    const val IOPAY_URL = "https://iopay.me/"

    const val IOPAY_REST_API_URL = "https://api.iopay.me/api/rest/"

    const val IOPAY_GATEWAY_URL_1 = "https://gateway1.iotex.me"

    const val GOOGLE_PLAY_OPEN_TEST_URL = "https://static.iopay.me/doc/open-testing"

    const val MIMO_NFT_MARKET_PLACE_URL = "https://nft.mimo.exchange/collections/"

    const val AA_WALLET_RECOVER_EMAIL = "<EMAIL>"

    const val AA_WALLET_SERVICE = "https://graph.mainnet.iotex.io/"

    const val GAS_TRACKER_IOTEX = "https://index.iotexscan.io/api?module=gastracker&action=gasoracle"

    const val GAS_TIME_TRACKER_IOTEX = "https://index.iotexscan.io/api?module=gastracker&action=gasestimate&gasprice="

    const val GAS_TIME_TRACKER_EVM = "https://api.etherscan.io/v2/api?chainid=1&module=gastracker&action=gasestimate&apikey=VNSZID2JZJF287Z2J4821TJ5FEFVQUES9I&gasprice="

    const val FIREBASE_EVENT_URL = "https://ga.dapp.works/api/event"

    const val ACTION_BY_ADDRESS_URL = "https://analyser-api.iotex.io/"

    fun applyGasLink(address: String): String {
        return "https://static.iopay.me/iopay-web/paymaster-claim?address=$address"
    }

    const val TWITTER_URL = "https://twitter.com/iotex_io"
    const val TELEGRAM_URL = "https://t.me/IoTeXGroup"
    const val FACEBOOK_URL = "https://www.facebook.com/iotex.io/"
    const val REDDIT_URL = "https://www.reddit.com/r/IoTeX/"
    const val YOUTOBE_URL = "https://www.youtube.com/channel/UCdj3xY3LCktuamvuFusWOZw"
    const val MEDIUM_URL = "https://medium.com/iotex"
    const val FORUM_URL = "https://community.iotex.io/"

    const val PLATFORM = "Android"

    const val IOTEX_URL = "https://iotex.io/"

    const val DEPIN_SCAN_WEB_URL = "https://depinscan.io/widget/dashboard"
    const val BINO_AI_WEB_URL = "https://binoai.iopay.me/chat"

    const val FIO_METHOD_GET_PUB_ADDRESS = "/get_pub_address"

    const val LINK_SCHEME_ETHEREUM_TRANSFER = "ethereum:"
    const val LINK_SCHEME_ETHEREUM_TRANSFER_TOKEN = "transfer"

    const val MAX_AMOUNT = "ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"

    const val PARAM_BACK_URL = "next"
    const val AUTHORIZATION_HEAD = "Authorization:Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

    const val CONTRACT_FACTORY = "******************************************"
    const val CONTRACT_ENTRY_POINT = "******************************************"
    const val CONTRACT_BOUND_EMAIL = "******************************************"

    const val CONTRACT_W_IOTEX = "******************************************"

    val PAYMASTER_API_KEY_MAP = mapOf(
        Pair(4689, "p98ecac885f4406i87517263b83cb298"),
        Pair(4690, "a0a7767f2aaa4db1b385f71dd82e55ea"),
    )
    const val PAYMASTER_API_KEY = "p98ecac885f4406i87517263b83cb298"
    const val EMAIL_SERVICE = "https://email-binder.mainnet.iotex.io"
    const val BUNDLER_SERVICE = "https://bundler.mainnet.w3bstream.com"
    const val PAYMASTER_SERVICE = "https://paymaster.mainnet.w3bstream.com/rpc/p98ecac885f4406i87517263b83cb298"
    const val SUBGRAPH = "https://graph.mainnet.iotex.io/subgraphs/name/guardian/mainnet"

    const val BITCOIN_QUERY_API = "https://mempool.space/testnet/"
    const val GIFT_POINT_API = "https://gateway-prod.iopay.me"
    const val GIFT_CAMPAGIN_URL = "https://campaign.iopay.me/"
    const val AVATAR_JAZZ_ICON_URL = "https://avatar.iopay.me/jazzicon"

    const val STAKE_NFT_DETAIL_URL = "https://iotexscan.io/nft-bucket/"
    const val STAKE_NFT_V2_DETAIL_URL = "https://iotexscan.io/nft-bucket-v2/"
    const val DEX_SCREENER_URL = "https://dexscreener.com/iotex/"
    const val SWAP_API_BASE_URL = "https://swap-api.mimo.exchange"
    const val SWAP_API_URL = "https://swap-api.mimo.exchange/api/trade"
    const val UNI_SWAP_API_BASE_URL = "https://trading-api-labs.interface.gateway.uniswap.org"
    const val ZERO_ADDRESS = "******************************************"

    fun paymasterService(): String {
        val chainId = WalletHelper.getCurChainId()
        return PAYMASTER_API_KEY_MAP[chainId] ?: PAYMASTER_API_KEY
    }
}