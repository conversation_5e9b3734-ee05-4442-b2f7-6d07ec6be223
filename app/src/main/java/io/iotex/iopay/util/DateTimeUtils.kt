package io.iotex.iopay.util

import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.data.db.NETWORK_GENERAL
import io.iotex.iopay.data.db.NETWORK_POOR
import io.iotex.iopay.data.db.NETWORK_SMOOTH
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

class DateTimeUtils {

    companion object {
        val times = listOf(TimeUnit.DAYS.toMillis(365),
            TimeUnit.DAYS.toMillis(30),
            TimeUnit.DAYS.toMillis(1),
            TimeUnit.HOURS.toMillis(1),
            TimeUnit.MINUTES.toMillis(1),
            TimeUnit.SECONDS.toMillis(1))
        private val timesString = listOf("year", "month", "day", "hour", "minute", "second")

        private fun toDuration(duration: Long): String {
            val res = StringBuffer()
            for (i in 0 until times.size) {
                val current = times[i]
                val temp = duration / current
                if (temp > 0) {
                    res.append(temp).append(" ").append(timesString[i])
                        .append(if (temp != 1L) "s" else "").append(" ago")
                    break
                }
            }
            return if ("" == res.toString())
                "0 seconds ago"
            else
                res.toString()
        }

        fun formatDatetime(datetime: String): String {
            if (datetime.isEmpty()) return datetime
            val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
            val dt = parser.parse(datetime)
            var cal = Calendar.getInstance()
            val now = cal.time
            cal.add(Calendar.DATE, -1)
            val ago24h = cal.time
            if (dt.before(ago24h)) {
                cal = Calendar.getInstance()
                cal.set(Calendar.DAY_OF_YEAR, 1)
                var formatter: SimpleDateFormat = if (dt.before(cal.time))
                    SimpleDateFormat(Utils.getApp().getString(R.string.news_date_format_year))
                else
                    SimpleDateFormat(Utils.getApp().getString(R.string.news_date_format_day))
                return formatter.format(dt)
            }
            return toDuration(now.time - dt.time)
        }

        fun getDefinedDate(gap: Int, format: String = "yyyy-MM-dd"): String {
            val calendar = Calendar.getInstance()
            calendar.add(Calendar.DATE, gap)
            return TimeUtils.date2String(calendar.time, SimpleDateFormat(format))
        }

        //2022-04-09T07:28:10.279028+00:00
        fun isExPirationTime(expirationTime:String?):Boolean{
            if (expirationTime.isNullOrEmpty())  return false
            val times = expirationTime.split("T")
            if (times.size>1){
                val time = times[0]
                val exMills = TimeUtils.string2Millis(time,"yyyy-MM-dd")
                val nowMills = System.currentTimeMillis()
                return nowMills>=exMills
            }else{
                return false
            }
        }

        //2022-04-09T07:28:10.279028+00:00
        fun isActiveTime(startTime: String?, endTime: String?): Boolean {
            if (startTime.isNullOrEmpty()) return false
            val startTimes = startTime.split("T")
            if (startTimes.size > 1) {
                val start = startTimes[0]
                val startMills = TimeUtils.string2Millis(start, "yyyy-MM-dd")
                val nowMills = System.currentTimeMillis()
                if (endTime.isNullOrEmpty()) {
                    return nowMills >= startMills
                } else {
                    val endTimes = endTime.split("T")
                    if (endTimes.size > 1) {
                        val end = endTimes[0]
                        val endMills = TimeUtils.string2Millis(end, "yyyy-MM-dd")+24*3600*1000
                        return nowMills <= endMills
                    } else {
                        return nowMills >= startMills
                    }
                }
            } else {
                return false
            }
        }

        fun formatYMD(time: Long?): String {
            if (time == null) return ""
            val format = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())
            return format.format(time)
        }

        fun getRpcState(time: Long):Int{
            return when (time) {
                in (0..1000) -> NETWORK_SMOOTH
                in (1000..2000) -> NETWORK_GENERAL
                else -> NETWORK_POOR
            }
        }
    }

}
