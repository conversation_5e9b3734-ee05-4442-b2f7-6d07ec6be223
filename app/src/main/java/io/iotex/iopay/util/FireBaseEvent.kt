package io.iotex.iopay.util

object FireBaseEvent {
    const val ACTION_START_CREATE_WALLET_VIEW_MAINNET = "action_start_create_wallet_view_mainnet"
    const val ACTION_START_CREATE_RECOVERY_HD = "action_start_create_recovery_hd"
    const val ACTION_START_CREATE_RECOVERY_VERIFY_DELETE = "action_start_create_recovery_verify_delete"
    const val ACTION_START_CREATE_RECOVERY_VERIFY_CONFIRM = "action_start_create_recovery_verify_confirm"
    const val ACTION_START_IMPORT_RECOVERY_HD = "action_start_import_recovery_hd"
    const val ACTION_START_CREATE_WALLET = "action_start_create_wallet"
    const val ACTION_START_CREATE_RECOVERY_REGENERATE = "action_start_create_recovery_regenerate"
    const val ACTION_ACTIVITIES_PAGE_CLICK_EXPLORER = "action_activities_page_click_explorer"
    const val ACTION_SETTINGS_ABOUT_JOIN_BETA = "action_settings_about_joinbeta"
    const val ACTION_SETTINGS_BACKUP_MNEMOIC_SECTION1_COPY = "action_settings_backup_mnemoic_section1_copy"
    const val ACTION_SETTINGS_BACKUP_MNEMOIC_SECTION2_COPY = "action_settings_backup_mnemoic_section2_copy"
    const val ACTION_SETTINGS_BACKUP_MNEMOIC_ALL_COPY = "action_settings_backup_mnemoic_all_copy"
    const val ACTION_NFT_APPROVE_TITLE_TOOLTIPS = "action_nft_approve_title_tooltips"
    const val ACTION_NFT_APPROVE_CONTRACT_EDIT = "action_nft_approve_contract_edit"
    const val ACTION_SELL_NFT_SIGN_VIEW_DATA = "action_sell_nft_sign_view_data"
    const val ACTION_HOME_NFT_SHARE_DOWNLOAD = "action_home_nft_share_download"
    const val ACTION_HOME_NFT_SHARE_SHARE = "action_home_nft_share_share"
    const val ACTION_HOME_NFT_CONTRACT_CLICK = "action_home_nft_contract_click"
    const val ACTION_HOME_NFT_CONTRACT_COPY = "action_home_nft_contract_copy"
    const val ACTION_HOME_NFT_TOKEN_ID_COPY = "action_home_nft_tokenid_copy"
    const val ACTION_HOME_NFT_MARKETPLACE = "action_home_nft_marketplace"
    const val ACTION_BROWSER_DEPIN_BANNER = "action_browser_depin_banner"
    const val ACTION_HOME_STAKE = "action_home_stake"
    const val ACTION_TRANSFER_EXECUTE = "action_transfer_execute"
    const val NAVIGATION_TO_TRANSFER = "navigation_to_transfer"
    const val NAVIGATE_TO_RECEIVE = "navigate_to_receive"
    const val ACTION_TOKEN_DETAIL_SWAP = "action_token_detail_swap"
    const val ACTION_VITA_CLAIM = "action_vita_claim"
    const val ACTION_VITA_BID = "action_vita_bid"
    const val ACTION_HOME_TOKEN_DETAIL_TOOLS = "action_home_token_detail_tools"
    const val ACTION_TOKEN_DETAIL_CLICK_WEBSITE = "action_token_detail_click_website"
    const val ACTION_SWITCH_WALLET = "action_switch_wallet"
    const val ACTION_HOME_CLICK_COPY_ADDRESS = "action_home_click_copy_address"
    const val ACTION_HOME_CLICK_VISIBLE_BUTTON = "action_home_click_visible_button"
    const val SEND_BTN = "Send_Btn"
    const val RECEIVE_BTN = "Receive_Btn"
    const val ACTION_HOME_MENU_BUY = "action_home_menu_buy"
    const val ACTION_HOME_SWAP = "action_home_swap"
    const val ACTION_HOME_EARN = "action_home_earn"
    const val ACTION_CREATE_AA_WALLET = "action_create_aa_wallet"
    const val ACTION_RECOVERY_AA_WALLET = "action_recovery_aa_wallet"
    const val ACTION_AA_WALLET_TRANSFER = "action_aa_wallet_transfer"
    const val CLICK_DISCOVERY_BANNER = "click_discovery_banner"
    const val DAPP_QUIT = "dapp_quit"
    const val DAPP_CLICK = "dapp_click"
    const val W3BSTREAM_ENTER = "w3bStream_enter"
    const val W3BSTREAM_ACTIVATED = "w3bStream_activated"
    const val CLICK_SWITCH_LANGUAGE = "click_switch_language"
    const val ACTION_NETWORK_DEVELOPER_MODE = "action_network_developer_mode"
    const val ACTION_SETTING_GENERAL_AVATAR = "action_setting_general_avatar"
    const val ACTION_SETTING_NETWORK = "action_setting_network"
    const val ACTION_SETTING_GENERAL = "action_setting_general"
    const val ACTION_SETTING_MANAGER_WALLET = "action_setting_manager_wallet"
    const val ACTION_SETTING_W3BSTREAM = "action_setting_w3bstream"
    const val ACTION_SETTING_ADDRESS_BOOK = "action_setting_address_book"
    const val ACTION_SETTING_SECURITY_PRIVACY = "action_setting_security_privacy"
    const val ACTION_SETTING_ABOUT = "action_setting_about"
    const val ACTION_UPDATE_APP = "action_update_app"
    const val ACTION_AUTH_CLICK_BEFORE_TRANSACTION = "action_auth_click_before_transaction"
    const val ACTION_AUTH_CLICK_BEFORE_LOGIN = "action_auth_click_before_login"
    const val ACTION_ABOUT_CLICK_FEEDBACK = "action_about_click_feedback"
    const val ACTION_SETTING_ADDRESS_BOOK_ADD = "action_setting_address_book_add"
    const val ACTION_DAPP_WEB_CLICK_SEARCH = "action_dapp_web_click_search"
    const val ACTION_BROWSER_CLICK_MORE = "action_browser_click_more"
    const val ACTION_DAPP_WEB_CLICK_OPEN_IN_BROWSER = "action_dapp_web_click_open_in_browser"
    const val ACTION_BROWSER_NETWORK = "action_browser_network"
    const val ACTION_DAPP_WEB_CLICK_SWITCH_NETWORK = "action_dapp_web_click_switch_network"
    const val ACTION_DAPP_WEB_CLICK_SWITCH_WALLET = "action_dapp_web_click_switch_wallet"
    const val ACTION_DAPP_WEB_COPY_WALLET_ADDRESS = "action_dapp_web_copy_wallet_address"
    const val ACTION_SCORE_IOPAY_CLICK_CANCEL = "action_score_iopay_click_cancel"
    const val ACTION_SCORE_IOPAY_CLICK_CONFIRM = "action_score_iopay_click_confirm"
    const val ACTION_CUSTOM_TOKEN_CLICK_SAVE = "action_custom_token_click_save"
    const val ACTION_TOKEN_LIST_CLICK_CUSTOM = "action_token_list_click_custom"
    const val ACTION_SWITCH_NODE = "action_switch_node"
    const val ACTION_EDIT_NETWORK = "action_edit_network"
    const val ACTION_TRANSFER_CONFIRM = "action_transfer_confirm"
    const val ACTION_TRANSFER_SUCCESS = "action_transfer_success"
    const val ACTION_WC_CANCEL_ACTION = "action_wc_cancel_action"
    const val ACTION_WC_ALLOW_ACTION = "action_wc_allow_action"
    const val NAVIGATION_TO_EDIT_WALLET = "navigation_to_edit_wallet"
    const val ACTION_BROWSER_SEARCH_PAGE_CLICK_SEARCH = "action_browser_search_page_click_search"
    const val ACTION_IMPORT_WALLET_AA = "action_import_wallet_aa"
    const val CLICK_IMPORT_WALLET = "click_import_wallet"
    const val ACTION_IMPORT_WALLET_AA_CREATE = "action_import_wallet_aa_create"
    const val ACTION_IMPORT_WALLET_AA_RECOVER = "action_import_wallet_aa_recover"
    const val ACTION_IMPORT_WALLET_AA_CREATE_SEND_CODE = "action_import_wallet_aa_create_send_code"
    const val ACTION_IMPORT_WALLET_AA_CREATE_SUCCESS_ADDRESS_COPY = "action_import_wallet_aa_create_success_address_copy"
    const val ACTION_IMPORT_WALLET_AA_CREATE_SUCCESS_LEARN_MORE = "action_import_wallet_aa_create_success_learn_more"
    const val ACTION_IMPORT_WALLET_AA_CREATE_SUCCESS_ENTER = "action_import_wallet_aa_create_success_enter"
    const val ACTION_IMPORT_WALLET_AA_RECOVER_EMAIL_INPUT = "action_import_wallet_aa_recover_email_input"
    const val ACTION_IMPORT_WALLET_AA_RECOVER_EMAIL_QUERY_DATA_TRY_AGAIN = "action_import_wallet_aa_recover_email_query_data_try_again"
    const val ACTION_HOME_WALLET_RED_REMINDER = "action_home_wallet_red_reminder"
    const val ACTION_HOME_AA_WALLET_EXPIRED_SWITCH_WALLET = "action_home_aa_wallet_expired_switch_wallet"
    const val ACTION_HOME_AA_WALLET_EXPIRED_SEND_RECOVERY_EMAIL = "action_home_aa_wallet_expired_send_recovery_email"
    const val ACTION_WALLET_RECOVER_AA_TO_EMAIL_COPY = "action_wallet_recover_aa_to_email_copy"
    const val ACTION_WALLET_RECOVER_AA_SUBJECT_COPY = "action_wallet_recover_aa_subject_copy"
    const val ACTION_WALLET_RECOVER_AA_SEND = "action_wallet_recover_aa_send"
    const val ACTION_WALLET_RECOVER_AA_HAVE_SENT_EMAIL = "action_wallet_recover_aa_have_sent_email"
    const val ACTION_WALLET_RECOVER_AA_UNSUCCESS_RESULT_CONFIRM = "action_wallet_recover_aa_unsuccess_result_confirm"
    const val ACTION_HOME_AA_WALLET_SETUP_EMAIL_REMINDER_CONFIRM = "action_home_aa_wallet_setup_email_reminder_confirm"
    const val ACTION_RECOVER_WALLET_UNSUCCESS_SETUP_EMAIL_SEND_CODE = "action_recover_wallet_unsuccess_setup_email_send_code"
    const val ACTION_RECOVER_WALLET_UNSUCCESS_SETUP_EMAIL_CONFIRM = "action_recover_wallet_unsuccess_setup_email_confirm"
    const val ACTION_HOME_ENTRANCE_APPLY_GAS_FEE = "action_home_entrance_apply_gas_fee"
    const val ACTION_SEND_ENTRANCE_APPLY_GAS_FEE = "action_send_entrance_apply_gas_fee"
    const val ACTION_AA_WALLET_RECOVERING_COUNTDOWN_CONFIRM = "action_aa_wallet_recovering_countdown_confirm"
    const val ACTION_AA_WALLET_RECOVERING_ON_NEW_DEVICE_CONTINUE = "action_aa_wallet_recovering_on_new_device_continue"
    const val ACTION_AA_WALLET_RECOVERING_ON_NEW_DEVICE_STOP = "action_aa_wallet_recovering_on_new_device_stop"
    const val ACTION_VISITOR_HOME_ADD_WALLET = "action_visitor_home_add_wallet"
    const val ACTION_VISITOR_HOME_DEPINSCAN_ENTRANCE = "action_visitor_home_depinscan_entrance"
    const val ACTION_VISITOR_HOME_DEPINSCAN_TOKEN_LIST = "action_visitor_home_depinscan_token_list"
    const val ACTION_HOME_MENU = "action_home_menu"
    const val ACTION_VISITOR_MENU = "action_visitor_menu"
    const val ACTION_VISITOR_SETTINGS_NEW_WALLET = "action_visitor_settings_new_wallet"
    const val ACTION_VISITOR_SETTINGS_GENERAL = "action_visitor_settings_general"
    const val ACTION_VISITOR_SETTINGS_ABOUT_IOPAY = "action_visitor_settings_about_iopay"
    const val ACTION_HOME_ACTIVITIES = "action_home_activities"
    const val ACTION_SEND_NETWORK_SWITCH = "action_send_network_switch"
    const val ACTION_WALLET_RECOVERY_PHRASE_EXPAND = "action_wallet_recovery_phrase_expand"
    const val ACTION_WALLET_RECOVERY_PHRASE_NEW_WALLET = "action_wallet_recovery_phrase_new_wallet"
    const val ACTION_HOME_TOKEN_LIST_DEPIN_TOKEN = "action_home_token_list_depin_token"
    const val ACTION_ADD_TOKEN_DEPIN_TOKEN = "action_add_token_depin_token"
    const val ACTION_VISITOR_DEPIN_TOKEN_DETAILS_ADD_WALLET_BANNER = "action_visitor_depin_token_details_add_wallet_banner"
    const val NAVIGATE_TO_SCAN = "navigate_to_scan"
    const val ACTION_SWAP_APPROVE_AMOUNT_EDIT = "action_swap_approve_amount_edit"
    const val ACTION_TXN_EDIT_NORMAL_GAS_FEE = "action_txn_edit_normal_gas_fee"
    const val ACTION_TXN_EDIT_EIP1559_GAS_FEE = "action_txn_edit_eip1559_gas_fee"
    const val ACTION_SEND_AMOUNT_FORMAT_SWITCH = "action_send_amount_format_switch"
    const val ACTION_SEND_ADDRESS_BOOK_ADD_NEW_ADDRESS = "action_send_address_book_add_new_address"
    const val ACTION_SEND_ADDRESS_MY_WALLET_TAB = "action_send_address_my_wallet_tab"
    const val ACTION_SEND_ADVANCED_MODE_CLICK = "action_send_advanced_mode_click"
    const val ACTION_SEND_ADVANCED_MODE_INPUT_DATA = "action_send_advanced_mode_input_data"
    const val ACTION_ADD_WALLET_IMPORT_PRIVATE_KEY_VISIBLE_ICON = "action_add_wallet_import_private_key_visible_icon"
    const val ACTION_ADD_WALLET_IMPORT_RECOVERY_PHRASE_VISIBLE_ICON = "action_add_wallet_import_recovery_phrase_visible_icon"
    const val ACTION_AA_WALLET_PERSONAL_SIGN_NOTE_SWITCH_WALLET = "action_aa_wallet_personal_sign_note_switch_wallet"
    const val ACTION_AA_WALLET_EXPIRED_REVOCERY_CONTACT_US = "action_aa_wallet_expired_revocery_contact_us"
    const val ACTION_SETTINGS_MANAGE_WALLETS_MANAGE_WALLET_TAB = "action_settings_manage_wallets_manage_wallet_tab"
    const val ACTION_SETTINGS_MANAGE_WALLET_RECOVERY = "action_settings_manage_wallet_recovery"
    const val ACTION_SETTINGS_MANAGE_WALLETS_MANAGE_WALLET_TAB_AA_NAME_EDIT = "action_settings_manage_wallets_manage_wallet_tab_aa_name_edit"
    const val ACTION_SETTINGS_MANAGE_WALLETS_MANAGE_WALLET_TAB_PRIVATE_KEY_NAME_EDIT = "action_settings_manage_wallets_manage_wallet_tab_private_key_name_edit"
    const val ACTION_CREATE_AA_WALLET_STATUS_BIND_EMAIL_FAIL_CLICK_TRY_AGAIN = "action_create_aa_wallet_status_bind_email_fail_click_try_again"
    const val ACTION_NEWER_INSTALL_START = "action_newer_install_start"
    const val ACTION_START_IMPORT_RECOVERY = "action_start_import_recovery"
    const val NAVIGATION_TO_PRIVATE_KEY = "navigation_to_private_key"
    const val ACTION_ADD_CUSTOM_NETWORK = "action_add_custom_network"
    const val ACTION_NEWER_GUIDE_CLICK_START = "action_newer_guide_click_start"
    const val ACTION_ACTIVITIES_PENDING_TXN_CANCEL = "action_activities_pending_txn_cancel"
    const val ACTION_ACTIVITIES_PENDING_TXN_SPEED_UP = "action_activities_pending_txn_speed_up"
    const val ACTION_ACTIVITIES_TXN_INFO_SHARE = "action_activities_txn_info_share"
    const val ACTION_ACTIVITIES_TXN_INFO = "action_activities_txn_info"
    const val ACTION_ACTION_TXN_SHARE = "action_action_txn_share"
    const val ACTION_DISCOVER_CLICK_NEWS = "action_discover_click_news"
    const val ACTION_HOME_MENU_RECEIVE = "action_home_menu_receive"
    const val ACTION_FIO_NAME = "action_fio_name"
    const val NAVIGATETO_CHANGEPIN = "navigateto_changepin"
    const val NAVIGATETO_DAPPMANAGER = "navigateto_dappmanager"
    const val ACTION_SETTINGS_MANAGE_WALLET_RECOVERY_REVEAL_COPY = "action_settings_manage_wallet_recovery_reveal_copy"
    const val ACTION_SETTINGS_MANAGE_WALLET_RECOVERY_IMPORT = "action_settings_manage_wallet_recovery_import"
    const val ACTION_HOME_CLICK_ADDTOKEN = "action_home_click_addtoken"
    const val ACTION_TOKEN_DETAIL_CLICK_LAUNCH_APP = "action_token_detail_click_launch_app"
    const val ACTION_ETH_NETWORK_TXN_GAS_FEE_SLOW = "action_eth_network_txn_gas_fee_slow"
    const val ACTION_ETH_NETWORK_TXN_GAS_FEE_AVERAGE = "action_eth_network_txn_gas_fee_average"
    const val ACTION_ETH_NETWORK_TXN_GAS_FEE_FAST = "action_eth_network_txn_gas_fee_fast"
    const val ACTION_ETH_NETWORK_TXN_GAS_FEE_CUSTMOIZED = "action_eth_network_txn_gas_fee_custmoized"
    const val API_ERROR_CATCH = "api_error_catch"
    const val ACTION_ERROR_CATCH = "action_error_catch"
    const val POINT_TASK_SIGN_IN = "point_task_sign_in"
    const val POINT_TASK_BROWSER = "point_task_browser"
    const val POINT_TASK_TRANSACTION = "point_task_transaction"
    const val POINT_TASK_STAKE = "point_task_stake"
    const val POINT_TASK_SWAP = "point_task_swap"
    const val POINT_TASK_API_ERROR = "point_task_api_error"
    const val ACTION_TAB_GIFT_CENTER = "action_tab_gift_center"
    const val ACTION_HOME_GIFT_CENTER = "action_home_gift_center"
    const val ACTION_GIFT_CENTER_BANNER = "action_gift_center_banner"
    const val ACTION_TRANSFER_METHOD = "action_transfer_method"
    const val ACTION_TRANSFER_METHOD_FAILED = "action_transfer_method_failed"
    const val ACTION_WC_CLICK_FLOAT_BUTTON = "action_wc_click_float_button"
    const val ACTION_WC_CLICK_CONNECT_BUTTON = "action_wc_click_connect_button"
}