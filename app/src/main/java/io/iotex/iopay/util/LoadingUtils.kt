package io.iotex.iopay.util

import android.content.Context
import io.iotex.iopay.R
import io.iotex.iopay.wallet.dialog.LogoLoadingManager

/**
 * 加载动画工具类
 * Loading Animation Utilities
 */
object LoadingUtils {

    /**
     * 显示默认的Logo加载动画
     */
    fun showLoading(context: Context) {
        LogoLoadingManager.showLogoLoading(
            context = context,
            loadingText = context.getString(R.string.loading),
            cancelable = false,
            cancelOutside = false
        )
    }

    /**
     * 显示带自定义文本的Logo加载动画
     */
    fun showLoading(context: Context, loadingText: String) {
        LogoLoadingManager.showLogoLoading(
            context = context,
            loadingText = loadingText,
            cancelable = false,
            cancelOutside = false
        )
    }

    /**
     * 显示带自定义文本资源ID的Logo加载动画
     */
    fun showLoading(context: Context, loadingTextResId: Int) {
        LogoLoadingManager.showLogoLoading(
            context = context,
            loadingTextResId = loadingTextResId,
            cancelable = false,
            cancelOutside = false
        )
    }

    /**
     * 显示可取消的Logo加载动画
     */
    fun showCancelableLoading(
        context: Context,
        loadingText: String = context.getString(R.string.loading),
        cancelOutside: Boolean = true
    ) {
        LogoLoadingManager.showLogoLoading(
            context = context,
            loadingText = loadingText,
            cancelable = true,
            cancelOutside = cancelOutside
        )
    }

    /**
     * 隐藏加载动画
     */
    fun hideLoading() {
        LogoLoadingManager.hideLoading()
    }

    /**
     * 检查是否正在显示加载动画
     */
    fun isLoading(): Boolean {
        return LogoLoadingManager.isShowing()
    }

    /**
     * 显示网络请求加载动画
     */
    fun showNetworkLoading(context: Context) {
        showLoading(context, context.getString(R.string.network_loading))
    }

    /**
     * 显示钱包操作加载动画
     */
    fun showWalletLoading(context: Context) {
        showLoading(context, context.getString(R.string.wallet_loading))
    }

    /**
     * 显示交易处理加载动画
     */
    fun showTransactionLoading(context: Context) {
        showLoading(context, context.getString(R.string.transaction_processing))
    }

    /**
     * 显示数据同步加载动画
     */
    fun showSyncLoading(context: Context) {
        showLoading(context, context.getString(R.string.syncing_data))
    }
}
