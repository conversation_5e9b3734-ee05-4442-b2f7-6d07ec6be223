package io.iotex.iopay.util

import android.text.TextUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.asNumericStr
import org.passay.CharacterRule
import org.passay.EnglishCharacterData
import org.passay.PasswordGenerator
import java.math.BigDecimal
import java.math.BigInteger
import java.text.DecimalFormat
import java.util.Random

object TokenUtil {

    const val gasPrice: Long = 1000000000000
    const val gasLimit: Long = 1000000

    fun weiToTokenBN(balance: String, decimals: Long = 18, displayDecimals: Int = if (WalletHelper.isBitcoinNetwork() && !UserStore.getAllNetwork()) 8 else 4): String {
        if (TextUtils.isEmpty(balance)) return "0"
        try {
            val ten = BigDecimal.TEN.pow(decimals.toInt())
            if (ten == BigDecimal.ZERO) return "0"
            var decimalString =
                balance.asBigDecimal().divide(ten).toPlainString()
            if (decimalString.contains("E")) {
                val df = DecimalFormat("#0.##################")
                decimalString = df.format(decimalString.asBigDecimal())
            }
            val decimalSplitS = decimalString.replace(",", ".").split(".")
            if (decimalSplitS.isNotEmpty() && decimalSplitS.size > 1) {
                val split1 = decimalSplitS[0]
                val split2 = decimalSplitS[1]
                var disPoint = ""
                val charArray = split2.toCharArray()
                var length = 0
                for (char in charArray) {
                    disPoint += char.toString()
                    if ((char.toString().toInt() > 0 || split1.toInt() > 0) || length > 0) {
                        length += 1
                        if (length >= displayDecimals) break
                    }
                }
                return "$split1.$disPoint"
            }
            return decimalString
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
            return "0"
        }
    }

    fun toWei(balance: String, decimals: Int = 18): BigInteger {
        return balance.asBigDecimal().multiply(BigDecimal.TEN.pow(decimals)).toBigInteger()
    }

    fun getCurrencyDecimal(): Int {
        return if (WalletHelper.getCurChainId() == Config.BITCOIN_TEST_CHAIN_ID ||
            WalletHelper.getCurChainId() == Config.BITCOIN_MAIN_CHAIN_ID
        ) {
            8
        } else {
            18
        }
    }

    fun displayXrc20Balance(balance: String, decimals: Long = 18): String {
        val detailBalance = weiToTokenBN(balance, decimals)
        return displayBalance(detailBalance)
    }

    fun displayPrice(price: String, displayDecimals:Int = 4): String {
        val detailBalance = weiToTokenBN(price, 0, displayDecimals)
        return displayBalance(detailBalance)
    }

    fun displayBalance(balance: String): String {
        var detailBalance = balance.asNumericStr()
        try {
            if (detailBalance.isBlank()) return "0"
            val decimal = detailBalance.asBigDecimal()
            if (decimal.compareTo(BigDecimal.ZERO) == 0) return "0"
            val decimalTag = BigDecimal(0.000001)
            if (decimal.compareTo(BigDecimal.ZERO) == 1 && decimal.compareTo(decimalTag) == -1) {
                return formZero(decimal)
            }
            if (detailBalance.contains(".")) {
                val balanceSplitS = detailBalance.split(".")
                val split1 = balanceSplitS[0]
                var split2 = balanceSplitS[1]
                if (split2.length > 8) {
                    split2 = split2.substring(0, 8)
                }
                detailBalance = if (split1.length > 3) {
                    formatNumber(split1)
                } else {
                    val decimalPlaces =
                        Utils.getApp().getString(R.string.format_balance_decimal_places)
                    "$split1$decimalPlaces$split2"
                }
            } else {
                if (decimal.compareTo(BigDecimal(1000)) == 1) {
                    detailBalance = formatNumber(detailBalance)
                }
            }
            detailBalance = subZeroAndDot(detailBalance)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return detailBalance
    }

    private fun subZeroAndDot(num: String): String {
        var s = num
        val decimalPlaces = Utils.getApp().getString(R.string.format_balance_decimal_places)
        if (s.indexOf(decimalPlaces) > 0) {
            s = s.replace("0+?$".toRegex(), "")
            s = s.replace("[decimalPlaces]$".toRegex(), "")
        }
        if (s.endsWith(decimalPlaces)) {
            s = s.replace(decimalPlaces, "")
        }
        return s
    }

    private fun setThousandthsSplit(s: String, split: String = ","): String {
        var newString = ""
        val yu = s.length % 3
        for (i in s.indices) {
            newString += s[i]
            if ((i + 1) % 3 == yu && i != s.length - 1) {
                newString += split
            }
        }
        return newString
    }

    // format Thousandths
    private fun formatNumber(number: String): String {
        try {
            if (TextUtils.isEmpty(number)) return "0"
            val decimalPlaces = Utils.getApp().getString(R.string.format_balance_qian_decimal)
            return setThousandthsSplit(number, decimalPlaces).replace("-$decimalPlaces", "-")
        } catch (e: Exception) {
            return number
        }
    }

    fun formatDecimal(decimals: String, displayDecimals: Int): String {
        if (TextUtils.isEmpty(decimals)) return "0"
        try {
            val decimalSplitS = decimals.asBigDecimal().toString().split(".")
            if (decimalSplitS.isNotEmpty() && decimalSplitS.size > 1) {
                val split1 = decimalSplitS[0]
                val split2 = decimalSplitS[1]
                var disPoint = ""
                val charArray = split2.toCharArray()
                var length = 0
                for (char in charArray) {
                    disPoint += char.toString()
                    if (char.toString().toInt() > 0 || length > 0) {
                        length += 1
                        if (length >= displayDecimals) break
                    }
                }
                return "$split1.$disPoint"
            }
            return decimals
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
            return "0"
        }
    }

    fun textEllipsis(text: String, before: Int, after: Int): String {
        if (before > 0 && after > 0 && (before + after) < text.length)
            return text.substring(0, before) + "..." + text.substring(
                text.length - after,
                text.length
            )
        return text
    }

    /**
     * Crate a random password that at least require one digit and one special character.
     */
    fun createRandomPassword(): String {
        val rand = Random(System.currentTimeMillis())
        val generator = PasswordGenerator(rand)
        return generator.generatePassword(
            10,
            CharacterRule(EnglishCharacterData.Alphabetical),
            CharacterRule(EnglishCharacterData.Digit),
            CharacterRule(EnglishCharacterData.Special)
        )
    }

    private fun getSortRank(l: TokenEntry, r: TokenEntry):Int{
        val sortOfficial = getSortOfficial(l, r)
        return if (sortOfficial == 0) {
            val sort = l.weight.compareTo(r.weight)
            if (sort == 0) {
                val sort2 = kotlin.runCatching {
                    l.rank_point.toBigDecimalOrNull()?.compareTo(r.rank_point.toBigDecimalOrNull())
                }.getOrNull()?:0
                if (sort2 == 0) {
                    l.symbol.compareTo(r.symbol)
                } else {
                    -sort2
                }
            } else {
                -sort
            }
        } else {
            sortOfficial
        }
    }

    private fun getSortCustom(l: TokenEntry, r: TokenEntry): Int {
        return if (l.isCustomToken && r.isCustomToken || (!l.isCustomToken && !r.isCustomToken)) {
            0
        } else if (l.isCustomToken) {
            -1
        } else {
            1
        }
    }

    private fun getSortOfficial(l: TokenEntry, r: TokenEntry): Int {
        return if ((l.isOfficial && r.isOfficial)
            || (!l.isOfficial && !r.isOfficial)
        ) {
            0
        } else if (l.isOfficial) {
            -1
        } else {
            1
        }
    }

    fun sortToken(items: List<TokenEntry>): List<TokenEntry> {
        return items.sortedWith { l, r ->
            getSortToken(l, r)
        }
    }

    fun sortCustomToken(items: List<TokenEntry>): List<TokenEntry> {
        return items.sortedWith { l, r ->
            val custom = getSortCustom(l, r)
            if(custom == 0){
                getSortToken(l, r)
            } else {
                custom
            }
        }
    }

    private fun getSortToken(l: TokenEntry, r: TokenEntry): Int {
        val top = getSortIsTop(l, r)
        val value = getSortValue(l, r)
        val rank = getSortRank(l, r)
        return if (top == 0) {
            if (value == 0) {
                rank
            } else {
                value
            }
        } else {
            top
        }
    }

    private fun getSortValue(l: TokenEntry, r: TokenEntry): Int{
        var lValue = BigDecimal(0)
        var rValue = BigDecimal(0)
        if (l.balance.isEmpty() && r.balance.isEmpty()) return 0
        try {
            lValue = (l.price.toBigDecimalOrNull()?:BigDecimal.ZERO) * (weiToTokenBN(
                l.balance,
                l.decimals.toLong()
            ).toBigDecimalOrNull()?:BigDecimal.ZERO)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            rValue = (r.price.toBigDecimalOrNull()?:BigDecimal.ZERO) * (weiToTokenBN(
                r.balance,
                r.decimals.toLong()
            ).toBigDecimalOrNull()?:BigDecimal.ZERO)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return if (lValue < rValue) {
            1
        } else if (lValue > rValue) {
            -1
        } else {
            val lBalance = kotlin.runCatching {
                l.balance.toBigDecimalOrNull()
            }.getOrNull()?: BigDecimal.ZERO
            val rBalance = kotlin.runCatching {
                r.balance.toBigDecimalOrNull()
            }.getOrNull()?: BigDecimal.ZERO
            if (lBalance < rBalance) {
                1
            } else if (lBalance > rBalance) {
                -1
            } else {
                0
            }
        }
    }

    private fun getSortIsTop(l: TokenEntry, r: TokenEntry): Int {
        return if ((l.isTop && r.isTop)
            || (!l.isTop && !r.isTop)
        ) {
            0
        } else if (l.isTop) {
            -1
        } else {
            1
        }
    }

    fun getNativeCurrencySymbol(): String {
        return SPUtils.getInstance()
            .getString(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, IoPayConstant.IOTX)
    }

    private fun formZero(decimal: BigDecimal): String {
        val decimal17 = "00000000000000000"
        val decimal16 = "0000000000000000"
        val decimal15 = "000000000000000"
        val decimal14 = "00000000000000"
        val decimal13 = "0000000000000"
        val decimal12 = "000000000000"
        val decimal11 = "00000000000"
        val decimal10 = "0000000000"
        val decimal9 = "000000000"
        val decimal8 = "00000000"
        val decimal7 = "0000000"
        val decimal6 = "000000"
        val decimal5 = "00000"
        val decimalString = decimal.toString().asNumericStr()
        return when {
            decimal < "0.${decimal16}1".asBigDecimal() -> {
                decimalString.replace(decimal17, "0{17}")
            }

            decimal < "0.${decimal15}1".asBigDecimal() -> {
                decimalString.replace(decimal16, "0{16}")
            }

            decimal < "0.${decimal14}1".asBigDecimal() -> {
                decimalString.replace(decimal15, "0{15}")
            }

            decimal < "0.${decimal13}1".asBigDecimal() -> {
                decimalString.replace(decimal14, "0{14}")
            }

            decimal < "0.${decimal12}1".asBigDecimal() -> {
                decimalString.replace(decimal13, "0{13}")
            }

            decimal < "0.${decimal11}1".asBigDecimal() -> {
                decimalString.replace(decimal12, "0{12}")
            }

            decimal < "0.${decimal10}1".asBigDecimal() -> {
                decimalString.replace(decimal11, "0{11}")
            }

            decimal < "0.${decimal9}1".asBigDecimal() -> {
                decimalString.replace(decimal10, "0{10}")
            }

            decimal < "0.${decimal8}1".asBigDecimal() -> {
                decimalString.replace(decimal9, "0{9}")
            }

            decimal < "0.${decimal7}1".asBigDecimal() -> {
                decimalString.replace(decimal8, "0{8}")
            }

            decimal < "0.${decimal6}1".asBigDecimal() -> {
                decimalString.replace(decimal7, "0{7}")
            }

            decimal < "0.${decimal5}1".asBigDecimal() -> {
                decimalString.replace(decimal6, "0{6}")
            }

            else -> {
                decimalString
            }
        }
    }
}