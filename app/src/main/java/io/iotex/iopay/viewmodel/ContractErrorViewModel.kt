package io.iotex.iopay.viewmodel

import android.app.Application
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.Utils
import io.iotex.api.ContractErrorMsgQuery
import io.iotex.base.okHttpClient
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.ContractErrorMsgEntry
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.extension.toast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ContractErrorViewModel(application: Application) : BaseLaunchVM(application) {

    private val mApolloClient = ApolloClient.builder()
        .serverUrl(Config.IoPayUrl)
        .okHttpClient(okHttpClient)
        .build()

    fun fetchContractErrorOneDay(){
        if (DateTimeUtils.formatYMD(UserStore.getContractErrorTime()) != DateTimeUtils.formatYMD(System.currentTimeMillis())) {
            UserStore.setContractErrorTime(System.currentTimeMillis())
            fetchContractError()
        }
    }

    private fun fetchContractError() {
        val homeNewsBannersQuery = ContractErrorMsgQuery.builder().build()

        addLaunchNoCancel {
            mApolloClient.query(homeNewsBannersQuery).await().data
                ?.contract_error_msg()?.forEach {
                    val entry = ContractErrorMsgEntry(
                        it.id(), it.error(), it.msg(), it.msg_cn()
                    )
                    AppDatabase.getInstance(getApplication())
                        .contractErrorMsgDao().insertIfNonExist(entry)
                }
        }
    }

    fun showContractError(error: String?) {
        addLaunch {
            error?.let {
                val entry = AppDatabase.getInstance(getApplication())
                    .contractErrorMsgDao().queryByError(error)
                entry?.let {
                    if (IoPayApplication.getAppContext().resources.configuration.locale.country == "CN")
                        entry.msg_cn?.toast() else entry.msg?.toast()
                }
            }
        }
    }

}

fun showContractError(error: String?) {
    CoroutineScope(Dispatchers.IO).launch {
        error?.let {
            val entry = AppDatabase.getInstance(Utils.getApp())
                .contractErrorMsgDao().queryByError(error)
            if (entry != null) {
                if (IoPayApplication.getAppContext().resources.configuration.locale.country == "CN")
                    entry.msg_cn?.toast() else entry.msg?.toast()
            } else {
                error.toast()
            }
        }
    }
}