package io.iotex.iopay.viewmodel.setting

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.api.IopayWalletModelsQuery
import io.iotex.base.RetrofitClient
import io.iotex.base.okHttpClient
import io.iotex.iopay.api.IopayGatewayAPI
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.meta.SP_KEY_W3B_STREAM_MENU_SERVICE
import io.iotex.iopay.meta.SP_KEY_W3B_STREAM_MENU_VISIBLE
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.IOPAY_GATEWAY_URL_1
import io.iotex.iopay.util.extension.toIoAddress
import io.iotex.iotex.AppSettingPebbleMenuQuery
import io.iotex.iotex.NewsLetterPushSettingQuery

class GeneralViewModel(application: Application) : BaseLaunchVM(application) {

    private val apiService by lazy {
        RetrofitClient.createApiService(IOPAY_GATEWAY_URL_1, IopayGatewayAPI::class.java)
    }

    val menuOpenLiveData = MutableLiveData<Boolean>()
    val newsLetterPushLiveData = MutableLiveData<Boolean?>()
    val notifyPushLiveData = MutableLiveData<Boolean?>()

    private val ioPayApolloClient: ApolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    fun queryNewsLetterPush(deviceToken: String) {
        val query = NewsLetterPushSettingQuery.builder().deviceToken(deviceToken).build()

        addLaunch(onError = {
            newsLetterPushLiveData.postValue(null)
        }) {
            ioPayApolloClient.query(query).await().data?.let {
                val configList = it.iopay_device_config()
                if (configList.isEmpty()) {
                    updateNewsLetterPush(deviceToken, true)
                } else {
                    updateNewsLetterPush(deviceToken, configList[0].news_push())
                }
            }
        }
    }

    fun updateNewsLetterPush(deviceToken: String, option: Boolean) {
        if (deviceToken == DeviceUtils.getUniqueDeviceId()) return
        addLaunch(onError = {
            newsLetterPushLiveData.postValue(null)
        }) {
            val map = mapOf(
                "deviceToken" to deviceToken,
                "news_push" to option,
                "platform" to Config.PLATFORM
            )
            apiService.topicRegister(map)
            newsLetterPushLiveData.postValue(option)
        }
    }

    fun queryNotifyPush(deviceToken: String) {
        addLaunch(onError = {
            notifyPushLiveData.postValue(null)
        }) {
            AppDatabase.getInstance(Utils.getApp()).walletDao().queryAllWallets()
                .forEach { wallet ->
                    val statusQuery = IopayWalletModelsQuery.builder()
                        .address(wallet.address)
                        .deviceToken(deviceToken)
                        .build()

                    ioPayApolloClient.query(statusQuery).await().data?.let {
                        val configList = it.iopay_wallet_models()
                        if (configList.isEmpty()) {
                            updateNotifyPush(
                                wallet.alias,
                                wallet.address,
                                deviceToken,
                                UserStore.getPushRegister()
                            )
                        } else {
                            updateNotifyPush(
                                wallet.alias,
                                wallet.address,
                                deviceToken,
                                configList[0].registerPushNotification().toBoolean()
                            )
                        }
                    }
                }
        }
    }

    fun updateNotifyPushAll(
        deviceToken: String,
        registerPushNotification: Boolean
    ) {
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).walletDao().queryAllWallets().forEach { wallet ->
                updateNotifyPush(
                    wallet.alias,
                    wallet.address,
                    deviceToken,
                    registerPushNotification
                )
            }
        }
    }

    private fun updateNotifyPush(
        addressName: String,
        address: String,
        deviceToken: String,
        registerPushNotification: Boolean
    ) {
        addLaunch(onError = {
            notifyPushLiveData.postValue(null)
        }) {
            val map = if (deviceToken == DeviceUtils.getUniqueDeviceId()) {
                mapOf(
                    "address" to address.toIoAddress(),
                    "name" to addressName,
                    "registerPushNotification" to registerPushNotification,
                    "platform" to Config.PLATFORM,
                    "deviceId" to deviceToken
                )
            } else {
                mapOf(
                    "address" to address.toIoAddress(),
                    "name" to addressName,
                    "registerPushNotification" to registerPushNotification,
                    "platform" to Config.PLATFORM,
                    "deviceToken" to deviceToken
                )
            }
            apiService.registerPush(map)
            notifyPushLiveData.postValue(registerPushNotification)
        }
    }

    fun getPebbleMenu() {
        //use last before get from service
        val service = SPUtils.getInstance().getBoolean(SP_KEY_W3B_STREAM_MENU_VISIBLE, true)
        menuOpenLiveData.postValue(service)
        val appSettingPebbleMenuQuery = AppSettingPebbleMenuQuery.builder()
            .build()

        addLaunch {
            ioPayApolloClient.query(appSettingPebbleMenuQuery).await().data
                ?.setting_pebble_menu()?.forEach {
                    if (it.platform() == "android") {
                        menuOpenLiveData.postValue(it.menu_display())
                        SPUtils.getInstance().put(SP_KEY_W3B_STREAM_MENU_VISIBLE, it.menu_display())
                        SPUtils.getInstance().put(SP_KEY_W3B_STREAM_MENU_SERVICE, it.display())
                    }
                }
        }
    }
}