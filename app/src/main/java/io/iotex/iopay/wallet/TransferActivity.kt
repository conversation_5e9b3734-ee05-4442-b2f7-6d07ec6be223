package io.iotex.iopay.wallet

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.view.View
import android.view.ViewConfiguration
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.ToastUtils
import com.machinefi.w3bstream.utils.extension.ellipsis
import com.machinefi.walletconnect2.WC2Config
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivityTransferBinding
import io.iotex.iopay.network.dialog.NetworkSwitchDialog
import io.iotex.iopay.service.HttpRequestManager
import io.iotex.iopay.setting.book.AddressBookActivity
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.token.TransferTypeActivity
import io.iotex.iopay.transaction.BitcoinTransactionDialog
import io.iotex.iopay.transaction.TransactionDialog
import io.iotex.iopay.transaction.bean.ExtraExpend
import io.iotex.iopay.transaction.bean.ExtraHead
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.Config.BSC_CHAIN_ID
import io.iotex.iopay.util.Config.ETH_CHAIN_ID
import io.iotex.iopay.util.Config.IOTEX_CHAIN_ID
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.SPConstant.TRANSFER_TIPS_DIALOG
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.applyPaymasterGas
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.asNumericStr
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.fromSatoshis
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toHexByteArray
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.viewmodel.wallet.BitcoinViewModel
import io.iotex.iopay.wallet.bitcoin.BitcoinHelper
import io.iotex.iopay.wallet.dialog.TransferTipsDialog
import io.iotex.iopay.wallet.qrcode.IoScanQRCodeActivity
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import io.iotex.iopay.wallet.viewmodel.TransferBean
import io.iotex.iopay.wallet.viewmodel.TransferViewModel
import io.iotex.iopay.wallet.web3.FunctionSignData
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.iopay.xapp.trust.AddChainsUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.startActivityForResult
import org.jetbrains.anko.uiThread
import org.web3j.ens.EnsResolver
import org.web3j.utils.HexToUtf8
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode
import java.util.concurrent.TimeUnit

class TransferActivity :
    BaseBindToolbarActivity<TransferViewModel, ActivityTransferBinding>(R.layout.activity_transfer) {

    private val aaWalletViewModel by lazy {
        ViewModelProvider(this)[AAWalletViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val bitcoinViewModel by lazy {
        ViewModelProvider(this)[BitcoinViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private var transferBean: TransferBean? = null
    private var transactionDialog: TransactionDialog? = null
    private var bitcoinTransactionDialog: BitcoinTransactionDialog? = null

    private var mContract = ""

    private val mReceiveAddress by lazy {
        intent.getStringExtra(KEY_RECEIVE_ADDRESS) ?: ""
    }

    private var amountIntent = ""

    private var freeGas = "0"
    private var amount = BigDecimal.ZERO
    private var max = false

    private var advancedMode = false
    private var hexModel = false

    private fun setTips() {
        when (WalletHelper.getCurChainId()) {
            ETH_CHAIN_ID -> {
                mBinding.tvReceiptError.text = getString(R.string.fio_name_tips1)
            }

            BSC_CHAIN_ID -> {
                mBinding.tvReceiptError.text = getString(R.string.fio_name_tips2)
            }

            IOTEX_CHAIN_ID -> {
                mBinding.tvReceiptError.text = getString(R.string.fio_name_tips3)
            }

            else -> {
                mBinding.tvReceiptError.text = getString(R.string.fio_name_tips)
            }
        }
        mBinding.tvReceiptError.setTextColor(getColor(R.color.transparent_50_white))
    }


    @SuppressLint("CheckResult")
    override fun initView() {
        setToolbarTitle(getString(R.string.transfer_send))
        EventBus.getDefault().register(this)

        mBinding.tvNext.setOnClickListener {
            KeyboardUtils.hideSoftInput(this)
            doTransfer()
        }

        if (mReceiveAddress.isNotBlank()) {
            if (WalletHelper.isIoTexNetWork()) {
                mBinding.etReceipt.setText(WalletHelper.formatWalletAddress(mReceiveAddress))
            } else {
                mBinding.etReceipt.setText(mReceiveAddress)
            }
        }
        if (WalletHelper.isIoTexNetWork() &&
            !SPUtils.getInstance().getBoolean(TRANSFER_TIPS_DIALOG, false)
        ) {
            val dialog = TransferTipsDialog()
            dialog.showAllowingStateLoss(this)
        }

        mBinding.ivScan.setOnClickListener {
            IoScanQRCodeActivity.startActivity(this) {
                if (WalletHelper.isValidAddressOnCurNetwork(it)) {
                    mBinding.etReceipt.setText(WalletHelper.formatWalletAddress(it))
                }
            }
            FireBaseUtil.logFireBase(FireBaseEvent.NAVIGATE_TO_SCAN)
        }

        mBinding.llSelectToken.setOnClickListener {
            startActivityForResult<TransferTypeActivity>(REQUEST_CODE)
        }

        mBinding.tvMaxBalance.setOnClickListener {
            calculateMax()
        }

        mBinding.ivNotebook.setOnClickListener {
            AddressBookActivity.startActivity(this) { address, name, isAA ->
                isAddress = true
                nameSuccess = name
                mFioAddressSuccess = address
                mBinding.etReceipt.setText(address)
                mBinding.ivAA.isVisible = isAA
                validAddress(address, nameSuccess)
            }
        }
        mBinding.ivDelete.setOnClickListener {
            mBinding.etReceipt.setText("")
        }
        mBinding.llAddressFinish.setOnClickListener {
            if (isAddress) mBinding.etReceipt.setText(
                WalletHelper.formatWalletAddress(
                    mFioAddressSuccess ?: ""
                )
            )
            mBinding.etReceipt.setSelection(mBinding.etReceipt.text.toString().length)
            mBinding.ivDelete.visibility = View.VISIBLE
            mBinding.llAddressFinish.visibility = View.GONE
            mBinding.etReceipt.requestFocus()
            KeyboardUtils.showSoftInput(mBinding.etReceipt)
        }
        mBinding.ivAddressFinishDelete.setOnClickListener {
            mBinding.llAddressFinish.visibility = View.GONE
            isAddress = false
            nameSuccess = ""
            mFioAddressSuccess = ""
            mBinding.etReceipt.setText("")
            mBinding.etReceipt.requestFocus()
            setTips()
            KeyboardUtils.showSoftInput(mBinding.etReceipt)
        }
        mBinding.etReceipt.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus && mBinding.llAddressFinish.visibility == View.GONE) {
                displayAddress(mBinding.etReceipt.text.toString())
                checkAddressReady()
            }
        }
        mBinding.llApplyGasFee.setOnClickListener {
            this.applyPaymasterGas()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SEND_ENTRANCE_APPLY_GAS_FEE)
        }

        mBinding.tvRemainFreeGasFee.setOnClickListener {
            this.applyPaymasterGas()
        }

        KeyboardUtils.registerSoftInputChangedListener(this) {
            if (it < ViewConfiguration.get(this).scaledTouchSlop) {
                if (!mFioAddressSuccess.isNullOrEmpty()) {
                    validAddress(mFioAddressSuccess ?: "", nameSuccess)
                } else {
                    displayAddress(mBinding.etReceipt.text.toString())
                }
            }
        }

        RxUtil.textChange(mBinding.etReceipt)
            .debounce(500, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                if (!isAddress) {
                    displayAddress(it)
                } else {
                    isAddress = false
                }
            }
        amountIntent = intent.getStringExtra(KEY_RECEIVE_AMOUNT) ?: ""
        mBinding.etAmount.addTextChangedListener {
            val num = it.toString().asNumericStr().toBigDecimalOrNull() ?: BigDecimal.ZERO
            var divider = transferBean?.price ?: BigDecimal(1)
            if (divider == BigDecimal.ZERO) divider = BigDecimal(1)
            val amountInput = if (mBinding.tvMoneyType.isVisible) {
                num.divide(
                    divider, 18, RoundingMode.DOWN
                )
            } else {
                num
            }
            //max change not read form text this time.
            if (max) {
                max = false
            } else {
                amount = amountInput
            }
            calculateMoney(true)
            if (it.toString().isNotEmpty()) {
                checkAmountReady()
            }
        }

        mBinding.llSelectNetwork.setOnClickListener {
            NetworkSwitchDialog().apply {
                onItemClick = {
                    mViewModel.curNetWork(true)
                }
            }.show(supportFragmentManager, System.currentTimeMillis().toString())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SEND_NETWORK_SWITCH)
        }

        mBinding.ivChangeInput.setOnClickListener {
            mBinding.tvMoneyType.isVisible = !mBinding.tvMoneyType.isVisible
            calculateMoney()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SEND_AMOUNT_FORMAT_SWITCH)
        }

        mBinding.llChangeModel.setOnClickListener {
            advancedMode = !advancedMode
            mBinding.llAdvancedMode.isVisible = advancedMode
            mBinding.ivAdvancedIcon.rotation = if (advancedMode) 90f else 0f
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SEND_ADVANCED_MODE_CLICK)
        }

        mBinding.etAdvanced.addTextChangedListener {
            if(it.toString().startsWith("0x") && !hexModel){
                val parData = HexToUtf8.hexStringToBytes(it.toString().cleanHexPrefix())
                if (parData != it.toString()) {
                    mBinding.etAdvanced.setText(parData)
                }
            }
        }

        mBinding.tvTextModel.setOnClickListener {
            hexModel = false
            mBinding.tvTextModel.setBackgroundResource(R.drawable.shape_617aff_r4)
            mBinding.tvHexModel.setBackgroundResource(0)
            mBinding.tvTextModel.setTextColor(ColorUtils.getColor(R.color.white))
            mBinding.tvHexModel.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
            mBinding.etAdvanced.setText(HexToUtf8.hexStringToBytes(mBinding.etAdvanced.text.toString().cleanHexPrefix()))
        }

        mBinding.tvHexModel.setOnClickListener {
            hexModel = true
            mBinding.tvTextModel.setBackgroundResource(0)
            mBinding.tvHexModel.setBackgroundResource(R.drawable.shape_617aff_r4)
            mBinding.tvTextModel.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
            mBinding.tvHexModel.setTextColor(ColorUtils.getColor(R.color.white))
            val oldData = mBinding.etAdvanced.text.toString().toByteArray().toHexString()
            if (oldData != "0x") {
                mBinding.etAdvanced.setText(oldData)
            }
        }

        mContract = intent?.getStringExtra(KEY_SELECTED_ADDRESS) ?: ""
        mBinding.llAdvancedAll.isVisible = mContract.isEmpty() && !WalletHelper.isBitcoinNetwork()
    }

    override fun initData() {
        mViewModel.curNetWork(false)
        mViewModel.curTransfer(mContract)
        if (Constant.currentWallet?.isAAWallet() == true) {
            mBinding.tvRemainFreeGasFee.setVisible()
            mBinding.llApplyGasFee.setVisible()
            aaWalletViewModel.getRemainFeeGas()
        }
    }

    override fun initEvent() {
        mViewModel.spaceIdLiveData.observe(this) { address ->
            if (WalletHelper.isValidAddressOnCurNetwork(address)) {
                mFioAddressSuccess = address
                validAddress(address, mBinding.etReceipt.text.toString())
            }
        }
        aaWalletViewModel.freeGasLD.observe(this) {
            freeGas = it
            val symbol = TokenUtil.getNativeCurrencySymbol()
            mBinding.tvRemainFreeGasFee.text =
                "${getString(R.string.remain_free_gas_fee)} $it $symbol"
        }
        mViewModel.transferLiveData.observe(this) {
            transferBean = it
            mBinding.tvToken.text = it.symbol
            mBinding.ivToken.loadSvgOrImage(it.logo, R.drawable.icon_token_default)
            if(amountIntent.isNotEmpty()){
                mBinding.etAmount.setText(amountIntent)
                amountIntent = ""
            } else {
                mBinding.etAmount.setText("")
            }
            displayBalance()
            calculateMoney()
        }

        mViewModel.networkLiveData.observe(this) {
            mBinding.tvNetwork.text = it.name
            mBinding.ivNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
            setTips()
        }

        mViewModel.changeNetworkLiveData.observe(this) {
            mBinding.tvNetwork.text = it.name
            mBinding.ivNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
            mContract = ""
            mBinding.llAdvancedAll.isVisible = mContract.isEmpty()
            mViewModel.curTransfer(mContract)
            setTips()
        }

        mViewModel.receiveWarningLiveData.observe(this) {
            if (it.isNullOrEmpty()) {
                setTips()
            } else {
                mBinding.tvReceiptError.text = it
                mBinding.tvReceiptError.setTextColor(getColor(R.color.color_ec7f11))
            }
        }
        bitcoinViewModel.sendMaxLD.observe(this) {
            amount = it.fromSatoshis().toBigDecimalOrNull() ?: BigDecimal.ZERO
            calculateMoney()
        }
        bitcoinViewModel.transferResultLD.observe(this) {
            bitcoinTransactionDialog?.dismiss()
            finish()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_SUCCESS)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNetworkSwitchEvent(event: NetworkSwitchEvent) {
        mViewModel.curNetWork(true)
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    private fun calculateMoney(input: Boolean = false) {
        hideLoading()
        if (amount == BigDecimal.ZERO) {
            if (mBinding.tvMoneyType.isVisible) {
                if (!input) mBinding.etAmount.setText("")
                mBinding.tvMoney.text =
                    getString(R.string.around_value_symbol, "0", transferBean?.symbol)
            } else {
                if (!input) mBinding.etAmount.setText("")
                mBinding.tvMoney.text = getString(R.string.value_money, "0")
            }
        } else {
            val money = transferBean?.price?.multiply(amount).toString()
            if (mBinding.tvMoneyType.isVisible) {
                if (!input) {
                    mBinding.etAmount.setText(TokenUtil.displayBalance(money))
                } else {
                    mBinding.tvMoney.text = getString(
                        R.string.around_value_symbol,
                        TokenUtil.displayBalance(amount.toString()),
                        transferBean?.symbol
                    )
                }
            } else {
                if (!input) {
                    mBinding.etAmount.setText(TokenUtil.displayBalance(amount.toString()))
                } else {
                    mBinding.tvMoney.text = getString(
                        R.string.value_money,
                        TokenUtil.displayBalance(money)
                    )
                }

            }
        }
    }

    private fun checkAddressReady(): Boolean {
        val receiptAddress = mBinding.etReceipt.text.toString().trim()
        val receiptValid = WalletHelper.isValidAddressOnCurNetwork(receiptAddress)
        if (!receiptValid && mFioAddressSuccess == null) {
            if (receiptAddress.isBlank()) {
                mBinding.tvReceiptError.text = getString(R.string.receipt_address_required)
            } else {
                mBinding.tvReceiptError.text = getString(R.string.invalid_receiver)
            }
            mBinding.tvReceiptError.setTextColor(getColor(R.color.error_red))
        } else {
            if (mFioAddressSuccess != null) {
                mFioAddressSuccess?.let {
                    mViewModel.checkContract(it)
                }
            } else {
                mViewModel.checkContract(receiptAddress)
            }
        }
        return receiptValid || mFioAddressSuccess != null
    }

    fun displayBalance() {
        val value = TokenUtil.weiToTokenBN(transferBean?.tokenBalance ?: "", transferBean?.decimals?.toLong() ?: 18)
        val balanceStr = "${getString(R.string.current_balance)} ${TokenUtil.displayBalance(value)} ${transferBean?.symbol ?: ""}"
        mBinding.tvBalance.text = balanceStr

        if (WalletHelper.isBitcoinNetwork()) {
            val availableValue = TokenUtil.weiToTokenBN(transferBean?.availableBalance ?: "", transferBean?.decimals?.toLong() ?: 18)
            val availableBalanceStr = "${getString(R.string.available)}: ${TokenUtil.displayBalance(availableValue)} ${transferBean?.symbol ?: ""}"
            mBinding.tvAvailable.text = availableBalanceStr
            mBinding.tvAvailable.isVisible = true
        } else {
            mBinding.tvAvailable.isVisible = false
        }
    }

    private var mFioAddressSuccess: String? = null
    private var nameSuccess: String? = null
    private var isAddress = false
    private fun displayAddress(s: String) {
        mFioAddressSuccess = null
        if (!TextUtils.isEmpty(s)) {
            mBinding.ivDelete.visibility = View.VISIBLE
            val receiptValid = WalletHelper.isValidAddressOnCurNetwork(s)
            if (receiptValid || WalletHelper.isBitcoinNetwork()) {
                validAddress(s)
            } else {
                mBinding.llAddressFinish.visibility = View.GONE
                getFioAddress(s)
                getENSAddress(s)
                getINSAddress(s)
                mViewModel.getSpaceId(s)
            }
        } else {
            mBinding.llAddressFinish.visibility = View.GONE
        }
    }

    private fun validAddress(address: String, fioName: String? = null) {
        if (!checkAddressReady()) return

        mBinding.llAddressFinish.visibility = View.VISIBLE
        mBinding.tvAddressFinish.text = address.ellipsis(4, 6)
        mBinding.ivAvatar.loadSvgOrImage(WalletHelper.getAddressAvatar(address), R.drawable.icon_wallet_default)

        if (WalletHelper.isBitcoinNetwork()) {
            kotlin.runCatching {
                mBinding.tvNameFinish.visibility = View.VISIBLE
                mBinding.tvNameFinish.text = BitcoinHelper.resolveAddressType(address)
            }.onFailure {
                mBinding.tvNameFinish.visibility = View.GONE
            }
        } else if (fioName != null) {
            mBinding.tvNameFinish.visibility = View.VISIBLE
            mBinding.tvNameFinish.text = fioName
        } else {
            mBinding.tvNameFinish.visibility = View.GONE
        }

        KeyboardUtils.hideSoftInput(mBinding.etReceipt)
    }

    private fun calculateMax() {
        max = true
        lifecycleScope.launch {
            if (Constant.currentWallet?.isAAWallet() == true || mContract.isNotEmpty()) {
                transferBean?.let {
                    amount = TokenUtil.weiToTokenBN(
                        it.tokenBalance,
                        it.decimals.toLong(),
                        it.decimals
                    ).toBigDecimalOrNull() ?: BigDecimal.ZERO
                    calculateMoney()
                }
            } else {
                var toAddress = mBinding.etReceipt.text.toString()
                mFioAddressSuccess?.let {
                    toAddress = it
                }
                if (toAddress.isBlank()) {
                    getString(R.string.receipt_address_required).toast()
                    return@launch
                }
                if (!WalletHelper.isValidAddressOnCurNetwork(toAddress)) {
                    getString(R.string.invalid_receiver).toast()
                    return@launch
                }
                showLoading()
                if (WalletHelper.isBitcoinNetwork()) {
                    bitcoinViewModel.calculateSendMax(toAddress)
                } else if(WalletHelper.isSolanaNetwork()){
                    val free = withContext(Dispatchers.IO) {
                        SolanaWeb3.getFree()
                    }
                    val bigAmount =
                        (transferBean?.tokenBalance?.toBigIntegerOrNull()
                            ?: BigInteger.ZERO).subtract(
                            (200 * (free ?: 0L)).toBigInteger()
                        ).toString()
                    amount =
                        TokenUtil.weiToTokenBN(bigAmount, transferBean?.decimals?.toLong() ?: 0L)
                            .toBigDecimalOrNull() ?: BigDecimal.ZERO
                    calculateMoney()
                } else {
                    val data =
                        FunctionSignData.getTransferSignData(
                            toAddress,
                            transferBean?.tokenBalance?.toBigIntegerOrNull() ?: BigInteger.ZERO
                        )
                    val gasFee = withContext(Dispatchers.IO) {
                        val gasLimit = Web3Delegate.estimate(toAddress, data, BigInteger.ZERO)
                        val gasPrice = Web3Delegate.gasPrice()
                        gasLimit.multiply(gasPrice)
                    }
                    val bigAmount =
                        (transferBean?.tokenBalance?.toBigIntegerOrNull() ?: BigInteger.ZERO).subtract(
                            gasFee
                        ).toString()
                    amount = TokenUtil.weiToTokenBN(bigAmount).toBigDecimalOrNull() ?: BigDecimal.ZERO
                    calculateMoney()
                }
            }
        }
    }

    private fun checkAmountReady(): Boolean {
        try {
            if (mBinding.etAmount.text.toString().trim().isEmpty()) {
                setAmountErr(getString(R.string.input_amount_hint))
                return false
            }
            val ercBalance =
                TokenUtil.weiToTokenBN(
                    transferBean?.tokenBalance ?: "0",
                    transferBean?.decimals?.toLong() ?: 18,
                    transferBean?.decimals ?: 18
                ).asBigDecimal()
            if (amount > ercBalance) {
                setAmountErr(getString(R.string.transfer_amount_invalid))
                return false
            }
            if (amount < BigDecimal.ZERO) {
                amount = TokenUtil.weiToTokenBN(transferBean?.tokenBalance ?: "", transferBean?.decimals?.toLong() ?: 18).toBigDecimalOrNull() ?: BigDecimal.ZERO
                calculateMoney()
                return false
            }

            setAmountRight()
            return true
        } catch (e: Exception) {
            setAmountErr(getString(R.string.invalid_amount_number))
            return false
        }
    }

    private fun doTransfer() {
        lifecycleScope.launch {
            if (!checkAddressReady() || !checkAmountReady()) {
                return@launch
            }
            var toAddress = mBinding.etReceipt.text.toString()
            mFioAddressSuccess?.let {
                toAddress = it
            }
            if (toAddress.isBlank()) {
                getString(R.string.receipt_address_required).toast()
                return@launch
            }
            if (!WalletHelper.isValidAddressOnCurNetwork(toAddress)) {
                getString(R.string.invalid_receiver).toast()
                return@launch
            }

            showTransactionDialog(toAddress, amount)
        }
    }

    private fun setAmountErr(error: String) {
        mBinding.tvAmountError.visibility = View.VISIBLE
        mBinding.tvAmountError.text = error
    }

    private fun setAmountRight() {
        mBinding.tvAmountError.visibility = View.GONE
    }

    private fun showTransactionDialog(
        toAddress: String,
        amount: BigDecimal,
    ) {
        val to = mContract.ifEmpty { toAddress }.toEvmAddress()
        val amountBig =
            TokenUtil.toWei(amount.toString().asNumericStr(), transferBean?.decimals ?: 18)
        val data: String
        val value: BigInteger
        if (mContract.isNotEmpty() && !WalletHelper.isSolanaNetwork()) {
            //amount in data
            data = FunctionSignData.getTransferSignData(toAddress, amountBig)
            value = BigInteger.ZERO
        } else {
            //amount in value
            data = "0x"
            value = amountBig
        }
        if (transactionDialog != null && transactionDialog?.dialog?.isShowing == true) return
        val from = Constant.currentWallet?.getCurNetworkAddress() ?: ""
        val list = ArrayList<OptionEntry>()
        list.add(OptionEntry(getString(R.string.method), getString(R.string.transfer_send)))
        list.add(OptionEntry(getString(R.string.you_send_only), "-"+amount.toPlainString()+" "+transferBean?.symbol))
        list.add(OptionEntry(getString(R.string.from), from))
        list.add(OptionEntry(getString(R.string.to), toAddress))
        val extraHead = ExtraHead(transferBean?.logo ?: "", transferBean?.symbol ?: "", false)
        if (WalletHelper.isBitcoinNetwork()) {
            bitcoinTransactionDialog = BitcoinTransactionDialog(to, value, list, extraHead).apply {
                onCancel = { bitcoinTransactionDialog = null }
                onTransactionConfirm = { to, value, transaction ->
                    bitcoinViewModel.sendTransaction(to, value, transaction)
                }
            }
            bitcoinTransactionDialog?.show(supportFragmentManager, System.currentTimeMillis().toString())
        } else {
            val extraExpend = ExtraExpend(
                amount,
                transferBean?.price ?: BigDecimal.ZERO,
                transferBean?.symbol ?: "",
                transferBean?.decimals ?: 18
            )
            transactionDialog = TransactionDialog(to, value, data, list,"","", extraHead, extraExpend).apply {
                onCancel = {
                    transactionDialog = null
                }
                onTransactionConfirm = { gasLimit: Long, gasPrice: String, maxPriorityFeePerGas, maxFeePerGas ->
                    transfer(toAddress, gasLimit, gasPrice, maxPriorityFeePerGas, maxFeePerGas, amount.toString())
                }
            }
            transactionDialog?.show(supportFragmentManager, System.currentTimeMillis().toString())
        }
    }

    private fun transfer(
        toAddress: String,
        gasLimit: Long,
        gasPrice: String,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        amount: String,
    ) {
        var data = ""
        val advance = mBinding.etAdvanced.text.toString().trim()
        if (advance.isNotBlank()) {
            data = if (hexModel) {
                advance
            } else {
                advance.toHexByteArray().toString()
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SEND_ADVANCED_MODE_INPUT_DATA)
        }

        val nativeCurrency = SPUtils.getInstance()
            .getString(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, IoPayConstant.IOTX)
        val timestamp = TimeUtils.getNowMills().toString()
        if (transferBean?.symbol.equals(nativeCurrency, true)) {
            mViewModel.transferCurrency(
                timestamp,
                toAddress,
                amount,
                data,
                gasLimit.toBigInteger(),
                gasPrice.asBigDecimal().toBigInteger(),
                maxPriorityFeePerGas,
                maxFeePerGas,
            )
        } else {
            mViewModel.transferERC20(
                timestamp,
                toAddress,
                mContract,
                transferBean?.pubkey?:"",
                amount,
                transferBean?.decimals?.toLong() ?: 18L,
                transferBean?.symbol?:"",
                gasLimit.toBigInteger(),
                gasPrice.asBigDecimal().toBigInteger(),
                maxPriorityFeePerGas,
                maxFeePerGas
            )
        }

        mViewModel.confirmLiveData.observe(this) {
            transactionDialog?.dismiss()
            finish()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_SUCCESS)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            data?.extras?.let {
                mContract = it.getString(KEY_SELECTED_ADDRESS, "")
                mBinding.llAdvancedAll.isVisible = mContract.isEmpty()
                mViewModel.curTransfer(mContract)
            }
        }
    }

    companion object {
        const val REQUEST_CODE = 1111
        const val KEY_SELECTED_ADDRESS = "KEY_SELECTED_ADDRESS"
        const val KEY_RECEIVE_ADDRESS = "KEY_RECEIVE_ADDRESS"
        const val KEY_RECEIVE_AMOUNT = "KEY_RECEIVE_AMOUNT"

        fun start(context: Context, contract: String? = null, address: String? = null, amount: String? = null) {
            val intent = Intent(context, TransferActivity::class.java)
            intent.putExtra(KEY_SELECTED_ADDRESS, contract)
            intent.putExtra(KEY_RECEIVE_ADDRESS, address)
            intent.putExtra(KEY_RECEIVE_AMOUNT, amount)
            context.startActivity(intent)
        }

        fun startByLink(context: Context, link: String) {
            if (WalletHelper.isEvmTransferLinkToken(link)) {
                val chainId = link.substringAfter("@").substringBefore("/").toIntOrNull()?: WalletHelper.getCurChainId()
                MainScope().launch {
                    val network = withContext(Dispatchers.IO) {
                        AppDatabase.getInstance(context).rpcNetworkDao()
                            .queryRPCNetworkByChainId(chainId)
                    } ?: return@launch
                    AddChainsUtil.showSwitchNetworkDialog(
                        WC2Config.IOPAY_LOGO,
                        WC2Config.IOPAY_URL,
                        network
                    ) {
                        if (!it) return@showSwitchNetworkDialog
                        //ethereum:******************************************@4689/transfer?address=******************************************&uint256=2
                        val contract = link.substringAfter(":").substringBefore("@")
                        val amount = link.substringAfter("uint256=")
                        val receive = link.substringAfter("address=").substringBefore("&uint256=")
                        start(context, contract, receive, amount)
                    }
                }
            } else {
                val chainId = link.substringAfter("@").substringBefore("?").toIntOrNull()?:return
                MainScope().launch {
                    val network = withContext(Dispatchers.IO) {
                        AppDatabase.getInstance(context).rpcNetworkDao()
                            .queryRPCNetworkByChainId(chainId)
                    } ?: return@launch
                    AddChainsUtil.showSwitchNetworkDialog(
                        WC2Config.IOPAY_LOGO,
                        WC2Config.IOPAY_URL,
                        network
                    ) {
                        if (!it) return@showSwitchNetworkDialog
                        //ethereum:******************************************@4689?value=3e18
                        val receive = link.substringAfter(":").substringBefore("@")
                        val amount = TokenUtil.weiToTokenBN(link.substringAfter("value="))
                        start(context, address = receive, amount = amount.toString())
                    }
                }
            }
        }
    }

    private fun getFioAddress(fioName: String) {
        doAsync {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                HttpRequestManager.getFioAddress(
                    fioName,
                    network.shortName,
                    transferBean?.symbol ?: "",
                    object : HttpRequestManager.HttpResultCallBack {
                        override fun failure(error: String) {
                            if (!fioName.startsWith("@") && !fioName.endsWith("@") && fioName.contains(
                                    "@"
                                )
                            ) {
                                ToastUtils.showShort(R.string.fio_name_error)
                            }
                        }

                        override fun success(result: String) {
                            uiThread {
                                if (WalletHelper.isValidAddressOnCurNetwork(result)) {
                                    mFioAddressSuccess = result
                                    nameSuccess = fioName
                                    validAddress(result, fioName)
                                }
                            }
                        }
                    })
            }

        }

    }

    private fun getENSAddress(name: String) {
        doAsync {
            val network = WalletHelper.getCurNetwork()
            if (network?.currencySymbol == "ETH" && EnsResolver.isValidEnsName(name)) {
                val address = Web3Delegate.resolveEns(name)
                if (WalletHelper.isValidAddressOnCurNetwork(address)) {
                    uiThread {
                        mFioAddressSuccess = address
                        nameSuccess = name
                        validAddress(address, name)
                    }
                }
            }
        }
    }

    private fun getINSAddress(name: String) {
        doAsync {
            if (WalletHelper.isIoTexNetWork()) {
                val address = Web3Delegate.insToAddress(name)
                if (WalletHelper.isValidAddressOnCurNetwork(address)) {
                    uiThread {
                        mFioAddressSuccess = address
                        nameSuccess = name
                        validAddress(address, name)
                    }
                }
            }
        }
    }
}