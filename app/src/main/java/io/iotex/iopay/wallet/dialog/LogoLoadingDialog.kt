package io.iotex.iopay.wallet.dialog

import android.content.Context
import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import com.airbnb.lottie.LottieAnimationView
import io.iotex.iopay.R

/**
 * Logo Loading Dialog with Lottie Animation
 * 基于IoTeX Logo的加载动画对话框
 */
class LogoLoadingDialog @JvmOverloads constructor(
    context: Context,
    themeId: Int = R.style.LoadingDialog
) : AppCompatDialog(context, themeId) {

    private var lottieLoading: LottieAnimationView? = null
    private var tvLoadingText: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_logo_loading)
        
        lottieLoading = findViewById(R.id.lottieLoading)
        tvLoadingText = findViewById(R.id.tvLoadingText)
        
        // 设置动画属性
        lottieLoading?.apply {
            setAnimation(R.raw.lottie_logo_loading)
            playAnimation()
            loop(true)
        }
    }

    /**
     * 设置加载文本
     */
    fun setLoadingText(text: String) {
        tvLoadingText?.text = text
    }

    /**
     * 设置加载文本资源ID
     */
    fun setLoadingText(textResId: Int) {
        tvLoadingText?.setText(textResId)
    }

    override fun show() {
        super.show()
        lottieLoading?.playAnimation()
    }

    override fun dismiss() {
        lottieLoading?.pauseAnimation()
        super.dismiss()
    }

    class Builder(private val context: Context) {
        private var isCancelable = false
        private var isCancelOutside = false
        private var loadingText: String? = null
        private var loadingTextResId: Int? = null

        fun setCancelable(isCancelable: Boolean): Builder {
            this.isCancelable = isCancelable
            return this
        }

        fun setCancelOutside(isCancelOutside: Boolean): Builder {
            this.isCancelOutside = isCancelOutside
            return this
        }

        fun setLoadingText(text: String): Builder {
            this.loadingText = text
            return this
        }

        fun setLoadingText(textResId: Int): Builder {
            this.loadingTextResId = textResId
            return this
        }

        fun create(): LogoLoadingDialog {
            val loadingDialog = LogoLoadingDialog(context)
            loadingDialog.setCancelable(isCancelable)
            loadingDialog.setCanceledOnTouchOutside(isCancelOutside)
            
            // 设置加载文本
            loadingText?.let { loadingDialog.setLoadingText(it) }
            loadingTextResId?.let { loadingDialog.setLoadingText(it) }
            
            return loadingDialog
        }
    }
}

/**
 * 全局加载对话框管理
 */
object LogoLoadingManager {
    private var mLoadingDialog: LogoLoadingDialog? = null

    /**
     * 显示Logo加载动画
     */
    fun showLogoLoading(
        context: Context,
        loadingText: String = context.getString(R.string.loading),
        cancelable: Boolean = false,
        cancelOutside: Boolean = false
    ) {
        hideLogoLoading()
        
        mLoadingDialog = LogoLoadingDialog.Builder(context)
            .setLoadingText(loadingText)
            .setCancelable(cancelable)
            .setCancelOutside(cancelOutside)
            .create()
            
        mLoadingDialog?.show()
    }

    /**
     * 显示Logo加载动画（使用字符串资源ID）
     */
    fun showLogoLoading(
        context: Context,
        loadingTextResId: Int = R.string.loading,
        cancelable: Boolean = false,
        cancelOutside: Boolean = false
    ) {
        hideLogoLoading()
        
        mLoadingDialog = LogoLoadingDialog.Builder(context)
            .setLoadingText(loadingTextResId)
            .setCancelable(cancelable)
            .setCancelOutside(cancelOutside)
            .create()
            
        mLoadingDialog?.show()
    }

    /**
     * 隐藏Logo加载动画
     */
    fun hideLogoLoading() {
        mLoadingDialog?.takeIf { it.isShowing }?.dismiss()
        mLoadingDialog = null
    }

    /**
     * 检查是否正在显示加载动画
     */
    fun isShowing(): Boolean {
        return mLoadingDialog?.isShowing == true
    }
}
