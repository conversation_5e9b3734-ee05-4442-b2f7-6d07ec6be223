package io.iotex.iopay.wallet.solana

import com.blankj.utilcode.util.Utils
import com.funkatronics.encoders.Base58
import com.machinefi.w3bstream.utils.extension.ellipsis
import com.solana.Solana
import com.solana.actions.sendSOL
import com.solana.actions.sendSPLTokens
import com.solana.api.getBalance
import com.solana.api.getFeeForMessage
import com.solana.api.getRecentBlockhash
import com.solana.api.getSignatureStatuses
import com.solana.api.getTokenAccountBalance
import com.solana.api.getTokenAccountsByOwner
import com.solana.api.sendRawTransaction
import com.solana.core.HotAccount
import com.solana.core.PublicKey
import com.solana.core.Transaction
import com.solana.core.TransactionInstruction
import com.solana.core.versioned.Message
import com.solana.core.versioned.VersionedTransaction
import com.solana.models.SignatureStatusRequestConfiguration
import com.solana.networking.HttpNetworkingRouter
import com.solana.networking.Network
import com.solana.networking.RPCEndpoint
import com.solana.programs.SystemProgram
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_DAPP
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.STATUS_FAILED
import io.iotex.iopay.data.db.STATUS_SUCCESS
import io.iotex.iopay.data.db.WalletCache
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.token.insertCustomToken
import io.iotex.iopay.util.DialogUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.util.extension.toJson
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.delay
import org.bouncycastle.util.encoders.Base64
import org.greenrobot.eventbus.EventBus
import org.json.JSONArray
import org.json.JSONObject
import java.math.BigInteger
import java.net.URL

object SolanaWeb3 {
    suspend fun getFree(message:String = ""): Long? {
        val endPoint = getSolanaRpc()
        val network = HttpNetworkingRouter(endPoint)
        val solana = Solana(network)
        val messageBase64 = if (message == "0x" || message.isEmpty()) {
            getDefaultGasMessage(solana)
        } else {
            message
        }
        val fee = kotlin.runCatching {
            solana.api.getFeeForMessage(messageBase64).getOrNull()
        }.getOrNull() ?: 5000
        return fee
    }

    private suspend fun getDefaultGasMessage(solana: Solana): String {
        return kotlin.runCatching {
            val account = HotAccount()
            val instructions = SystemProgram.transfer(account.publicKey, account.publicKey, 0)
            val transaction = Transaction()
            transaction.add(instructions)
            val blockHash = solana.api.getRecentBlockhash().getOrNull() ?: ""
            transaction.setRecentBlockHash(blockHash)
            transaction.sign(listOf(account))
            Base64.toBase64String(transaction.compileMessage().serialize())
        }.getOrNull() ?: ""
    }

    suspend fun getBalance(publicKeyBase58: String, chainId:Int): BigInteger? {
        val endPoint = getSolanaRpc(chainId)
        val network = HttpNetworkingRouter(endPoint)
        val solana = Solana(network)
        val balance =
            kotlin.runCatching { solana.api.getBalance(PublicKey(publicKeyBase58)).getOrNull() }
                .getOrNull() ?: return null
        var walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
            .queryWalletCache(publicKeyBase58, chainId)
        if (walletCache == null) {
            walletCache =
                WalletCache(chainId, publicKeyBase58, balance.toString())
        } else {
            walletCache.balance = balance.toString()
        }
        AppDatabase.getInstance(Utils.getApp()).walletCacheDao().insertOrUpdate(walletCache)
        return balance.toBigInteger()
    }

    suspend fun getTokenBalance(pubkey: String): String? {
        if (pubkey.isEmpty()) return null
        val address = WalletHelper.getCurWallet()?.getCurNetworkAddress() ?: ""
        if (!WalletHelper.isSolanaAddress(address)) return null
        val endPoint = getSolanaRpc()
        val network = HttpNetworkingRouter(endPoint)
        val solana = Solana(network)
        val tokenAmountInfoResponse = kotlin.runCatching {
            solana.api.getTokenAccountBalance(PublicKey(pubkey)).getOrNull()
        }.getOrNull()
        return tokenAmountInfoResponse?.amount
    }

    suspend fun getTokenAccountsByOwner() {
        val address = WalletHelper.getCurWallet()?.getCurNetworkAddress() ?: ""
        if (!WalletHelper.isSolanaAddress(address)) return
        val endPoint = getSolanaRpc()
        val network = HttpNetworkingRouter(endPoint)
        val solana = Solana(network)
        val tokenAccountsByOwnerResponse = kotlin.runCatching {
            solana.api.getTokenAccountsByOwner(PublicKey(address))
                .getOrNull()?.value
        }.getOrNull()
        if (tokenAccountsByOwnerResponse?.isEmpty() == true) return
        tokenAccountsByOwnerResponse?.forEach {
            val info = it.account?.data?.parsed?.info
            val curAddress = WalletHelper.getCurWallet()?.getCurNetworkAddress() ?: ""
            if (curAddress != address) return
            insertCustomToken(
                info?.mint ?: "",
                it.pubkey,
                if ((info?.mint?.length ?: 0) > 4) info?.mint?.substring(0, 4)?.uppercase()
                    ?: "" else "SPL",
                info?.tokenAmount?.decimals?.toString() ?: "9",
                info?.mint?.ellipsis(5, 4) ?: "SPL",
                info?.tokenAmount?.amount ?: ""
            )
        }
    }

    suspend fun getTokenAccountDecimals(address: String): Int {
        val endPoint = getSolanaRpc()
        val network = HttpNetworkingRouter(endPoint)
        val solana = Solana(network)
        return kotlin.runCatching {
            solana.api.getTokenAccountBalance(PublicKey(address))
                .getOrNull()?.decimals ?: 9
        }.getOrNull() ?: 9
    }

    suspend fun transferSOL(
        timestamp: String,
        to: String,
        value: Long,
        onComplete: (String) -> Unit
    ) {
        val wallet = WalletHelper.getCurWallet()
        val privateKey = WalletHelper.getWalletPrivateKey(wallet)
        val endPoint = getSolanaRpc()
        val network = HttpNetworkingRouter(endPoint)
        val solana = Solana(network)
        val account = HotAccount(Base58.decode(privateKey)) // Should be founded
        val toPublicKey = PublicKey(to)
        Web3Delegate.insertTransaction(
            timestamp,
            "",
            "",
            from = account.publicKey.toBase58(),
            to = to,
            value = TokenUtil.weiToTokenBN(
                value.toString(),
                UserStore.getNetworkDecimals().toLong()
            ),
            decimals = UserStore.getNetworkDecimals().toString(),
            symbol = UserStore.getNetworkSymbol()
        )
        onComplete.invoke(timestamp)
        val hash = kotlin.runCatching {
            solana.action.sendSOL(account, toPublicKey, value).getOrNull()
        }.getOrNull()
        if (hash != null) {
            Web3Delegate.updateAction(
                timestamp,
                hash
            )
        } else {
            Web3Delegate.updateAction(
                timestamp,
                status = STATUS_FAILED
            )
        }
    }

    suspend fun transferSPL(
        timestamp: String,
        contract: String,
        pubkey: String,
        to: String,
        amount: String,
        decimals: Long,
        symbol: String,
        onComplete: (String) -> Unit
    ) {
        val wallet = WalletHelper.getCurWallet()
        val privateKey = WalletHelper.getWalletPrivateKey(wallet)
        val endPoint = getSolanaRpc()
        val network = HttpNetworkingRouter(endPoint)
        val solana = Solana(network)
        val account = HotAccount(Base58.decode(privateKey))
        val toPublicKey = PublicKey(to)
        val mintPublicKey = PublicKey(contract)
        Web3Delegate.insertTransaction(
            timestamp,
            "",
            "",
            from = account.publicKey.toBase58(),
            to = to,
            value = amount,
            decimals = decimals.toString(),
            symbol = symbol,
            contract = contract
        )
        onComplete.invoke(timestamp)
        val fromPublicKey = PublicKey(pubkey)
        val value = TokenUtil.toWei(amount, decimals.toInt())
        val hash = kotlin.runCatching {
            solana.action.sendSPLTokens(
                mintPublicKey,
                fromPublicKey,
                toPublicKey,
                value.toLong(),
                true,
                account
            ).getOrNull()
        }.getOrNull()
        if (hash != null) {
            Web3Delegate.updateAction(
                timestamp,
                hash
            )
        } else {
            Web3Delegate.updateAction(
                timestamp,
                status = STATUS_FAILED
            )
        }
    }

    suspend fun sendRawTransaction(
        timestamp: String,
        recentBlockhash: String?,
        instructions: ArrayList<TransactionInstruction>,
        trans: String?,
        name: String,
        method: String = ""
    ): String? {
        val wallet = WalletHelper.getCurWallet()
        val privateKey = WalletHelper.getWalletPrivateKey(wallet)
        if (!WalletHelper.isSolanaPrivatakey(privateKey)) return null
        val account = HotAccount(Base58.decode(privateKey))
        val endPoint = getSolanaRpc()
        val network = HttpNetworkingRouter(endPoint)
        val solana = Solana(network)
        val recentBlockHash = if (recentBlockhash.isNullOrEmpty()) {
            kotlin.runCatching {
                solana.api.getRecentBlockhash().getOrNull() ?: ""
            }.getOrNull() ?: ""
        } else {
            recentBlockhash
        }
        val serializedTransaction = if (instructions.isNotEmpty()) {
            val transaction = Transaction()
            transaction.add(*instructions.toTypedArray())
            transaction.setRecentBlockHash(recentBlockHash)
            transaction.sign(account)
            transaction.serialize()
        } else {
            val data = Base64.decode(trans ?: "")
            val versionedTransaction = VersionedTransaction.from(data)
            if (versionedTransaction.message.version == Message.MessageVersion.Legacy) {
                val transaction = Transaction.from(data)
                transaction.recentBlockhash = recentBlockHash
                transaction.sign(account)
                transaction.serialize()
            } else {
                versionedTransaction.sign(account)
                versionedTransaction.serialize()
            }
        }

        Web3Delegate.insertTransaction(
            timestamp,
            "",
            "",
            from = account.publicKey.toBase58(),
            type = ACTION_TYPE_DAPP,
            origin = name,
            method = method
        )
        val hash = kotlin.runCatching {
            solana.api.sendRawTransaction(serializedTransaction).onFailure {
                it.printStackTrace()
            }.getOrNull()
        }.onFailure {
            it.printStackTrace()
        }.getOrNull()
        if (hash != null) {
            Web3Delegate.updateAction(
                timestamp,
                hash
            )
        } else {
            Web3Delegate.updateAction(
                timestamp,
                status = STATUS_FAILED
            )
        }
        return hash
    }

    suspend fun signMessage(message: ByteArray): String {
        val wallet = WalletHelper.getCurWallet()
        val privateKey = WalletHelper.getWalletPrivateKey(wallet)
        if (!WalletHelper.isSolanaPrivatakey(privateKey)) {
            throw IllegalArgumentException("privateKey is not solana privateKey")
        }
        val account = HotAccount(Base58.decode(privateKey))
        return account.sign(message).toHexString()
    }

    suspend fun signAllTransactions(transactions: String): String? {
        val wallet = WalletHelper.getCurWallet()
        val privateKey = WalletHelper.getWalletPrivateKey(wallet)
        if (!WalletHelper.isSolanaPrivatakey(privateKey)) {
            throw IllegalArgumentException("privateKey is not solana privateKey")
        }
        val account = HotAccount(Base58.decode(privateKey))
        val txList = mutableListOf<Transaction>()
        val v0TxList = mutableListOf<VersionedTransaction>()
        val txObjArr = JSONArray(JSONObject(transactions).getString("transactions"))
        (0 until txObjArr.length()).map { index ->
            val txStr = txObjArr.getString(index)
            val versionedTransaction = VersionedTransaction.from(Base64.decode(txStr))
            if (versionedTransaction.message.version == Message.MessageVersion.Legacy) {
                val transaction = Transaction.from(Base64.decode(txStr))
                transaction.sign(account)
                txList.add(transaction)
            } else {
                versionedTransaction.sign(account)
                v0TxList.add(versionedTransaction)
            }
        }
        return if (txList.isNotEmpty()) {
            "{\"transactions\":" + txList.map { transaction ->
                Base64.toBase64String(transaction.serialize())
            }.toJson() + "}"
        } else if (v0TxList.isNotEmpty()) {
            "{\"transactions\":" + v0TxList.map { transaction ->
                Base64.toBase64String(transaction.serialize())
            }.toJson() + "}"
        } else {
            null
        }
    }

    private var tryTime = 0
    suspend fun getTransaction(signature: String, first: Boolean = true): Boolean? {
        if (signature.isEmpty()) {
            return null
        }
        val endPoint = getSolanaRpc()
        val network = HttpNetworkingRouter(endPoint)
        val solana = Solana(network)
        if (first) tryTime = 0
        tryTime += 1
        delay(10000)
        val result =
            kotlin.runCatching {
                solana.api.getSignatureStatuses(
                    listOf(signature),
                    SignatureStatusRequestConfiguration(true)
                ).getOrNull()
            }.getOrNull()
        return if (!result.isNullOrEmpty()) {
            val success = result[0].confirmationStatus == "finalized" || result[0].confirmationStatus == "confirmed"
            updateState(signature, success)
            return success
        } else {
            if (tryTime < 6) {
                getTransaction(signature, false)
            } else {
                updateState(signature, false)
                false
            }
        }
    }

    private fun updateState(hash: String, success: Boolean) {
        AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
            .queryByHash(WalletHelper.getCurChainId(), hash)?.let { action ->
                action.status = if (success) STATUS_SUCCESS else STATUS_FAILED
                AppDatabase.getInstance(Utils.getApp()).actionRecordDao().insertOrReplace(action)
                DialogUtil.showTransferLoading(action.timestamp)
                EventBus.getDefault().post(ActionRefreshEvent())
            }
    }

    private fun getSolanaRpc(chainId:Int = WalletHelper.getCurChainId()): RPCEndpoint {
        val rpcNode = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
            .queryRPCNodeActivated(chainId)
        val url = URL(rpcNode?.rpcUrl ?: WalletHelper.getCurRpcUrl())
        return RPCEndpoint.custom(url, url, Network.mainnetBeta)
    }
}