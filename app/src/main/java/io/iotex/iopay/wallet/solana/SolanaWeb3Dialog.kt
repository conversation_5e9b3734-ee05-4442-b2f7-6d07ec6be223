package io.iotex.iopay.wallet.solana

import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.LogUtils
import io.iotex.iopay.transaction.sign.SignMessageDialog
import io.iotex.iopay.util.extension.toHexByteArray
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


object SolanaWeb3Dialog {
    private var signMessageDialog: SignMessageDialog? = null
    fun signMessage(
        logo: String,
        url: String,
        message: String,
        callBack: (signedMessage: String?) -> Unit
    ) {
        signMessageDialog = SignMessageDialog(logo, url, null, message)
            .apply {
                onResult = { confirm ->
                    signMessageDialog = null
                    if (confirm) {
                        CoroutineScope(Dispatchers.IO).launch {
                            val signedMessage = kotlin.runCatching {
                                SolanaWeb3.signMessage(message.toHexByteArray())
                            }.getOrNull()
                            callBack.invoke(signedMessage)
                        }
                    } else {
                        callBack.invoke(null)
                    }
                }
            }
        (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
            signMessageDialog?.show(
                it.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }
    }

    fun signAllTransactions(
        logo: String,
        url: String,
        transactions: String,
        callBack: (signedMessage: String?) -> Unit
    ) {
        signMessageDialog = SignMessageDialog(logo, url, null, transactions)
            .apply {
                onResult = { confirm ->
                    signMessageDialog = null
                    if (confirm) {
                        CoroutineScope(Dispatchers.IO).launch {
                            val signedMessage = kotlin.runCatching {
                                SolanaWeb3.signAllTransactions(transactions)
                            }.getOrNull()
                            LogUtils.i("cccc","signAllTransactions:"+signedMessage.toString())
                            callBack.invoke(signedMessage)
                        }
                    } else {
                        callBack.invoke(null)
                    }
                }
            }
        (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
            signMessageDialog?.show(
                it.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }
    }
}