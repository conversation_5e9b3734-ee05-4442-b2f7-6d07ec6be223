package io.iotex.iopay.wallet.viewmodel

import android.app.Application
import androidx.annotation.IntDef
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.RetrofitClient
import io.iotex.iopay.api.AAWalletApi
import io.iotex.iopay.api.EmailAccount
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.ACTION_TYPE_BOUND_EMAIL
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.STATUS_FAILED
import io.iotex.iopay.data.db.STATUS_SUCCESS
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.data.db.WalletCache
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.AA_WALLET_SERVICE
import io.iotex.iopay.util.Config.BUNDLER_SERVICE
import io.iotex.iopay.util.Config.EMAIL_SERVICE
import io.iotex.iopay.util.Config.PAYMASTER_SERVICE
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.EncryptUtil
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.NetworkUtils
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.isAddress
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.iopay.wallet.web3.aawallet.JsonRpcManager
import io.iotex.iopay.wallet.web3.aawallet.P256AccountManager
import io.iotex.iopay.wallet.web3.aawallet.P256KeyManager
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import org.web3j.crypto.Hash

class AAWalletViewModel(application: Application) : BaseLaunchVM(application) {

    private val apiService by lazy {
        RetrofitClient.createApiService(AA_WALLET_SERVICE, AAWalletApi::class.java)
    }

    val walletLD = MutableLiveData<Wallet?>()
    val sendCodeLD = MutableLiveData<Boolean>()
    val verifyCodeLD = MutableLiveData<String>()
    val emailAccountsLD = MutableLiveData<List<EmailAccount>>()
    val freeGasLD = MutableLiveData<String>()
    val waitingEmailLD = MutableLiveData<Boolean>()
    val effectiveAddressLD = MutableLiveData<Boolean>()
    val saveWalletLD = MutableLiveData<Wallet>()
    val confirmLD = MutableLiveData<String>()
    val aaWalletStatusLD = MutableLiveData<AAWalletStatusWrapper>()
    val stopRecoveryLD = MutableLiveData<String>()
    val applyGasLD = MutableLiveData<Boolean>()
    val signatureLD = MutableLiveData<String>()
    val aaServiceLD = MutableLiveData<Boolean>()

    private var aaWallet: Wallet? = null

    fun createWallet() {
        addLaunch(true) {
            val name = WalletHelper.nameWallet()[0]
            aaWallet = P256AccountManager.createWallet(name)
            walletLD.postValue(aaWallet)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_CREATE_AA_WALLET)
        }
    }

    fun sendCode(email: String) {
        addLaunch(true,onError = {
            sendCodeLD.postValue(false)
        }) {
            val address = walletLD.value?.address ?: WalletHelper.getCurWallet()?.address ?: return@addLaunch
            val result = JsonRpcManager.sendEmailCode(address, email)
            sendCodeLD.postValue(!result.isNullOrBlank())
        }
    }

    fun isEffectiveAddress(address: String) {
        addLaunch(true) {
            val effective = P256AccountManager.isEffective(address)
            effectiveAddressLD.postValue(effective)
        }
    }

    fun queryAAWalletStatus() {
        addLaunch {
            val wallet = WalletHelper.getCurWallet() ?: return@addLaunch
            if (!wallet.isAAWallet()) {
                return@addLaunch
            }

            val effective = P256AccountManager.isEffective(wallet.address)
            wallet.effective = effective
            Constant.currentWallet = wallet
            AppDatabase.getInstance(Utils.getApp()).walletDao().updateWallet(wallet)

            val bound = P256AccountManager.hasBoundEmail(wallet.address)
            if (!bound) {
                aaWalletStatusLD.postValue(AAWalletStatusWrapper(AAWalletStatus.UNBOUND, 0L))
                return@addLaunch
            }
            val result = P256AccountManager.getRecoveryTimestamp(wallet.address)
            var timestamp = 0L
            var recoveryPubKey = ""
            if (result.isNotEmpty()) {
                timestamp = result[0].toLong()
                recoveryPubKey = result[1]
            }
            if (effective && timestamp == 0L) {
                aaWalletStatusLD.postValue(AAWalletStatusWrapper(AAWalletStatus.AVAILABLE, 0L))
            } else if (effective && timestamp > 0L && recoveryPubKey != P256KeyManager.getPubKey()) {
                aaWalletStatusLD.postValue(AAWalletStatusWrapper(AAWalletStatus.OTHER_RECOVERING, timestamp))
            } else if (!effective && timestamp == 0L) {
                aaWalletStatusLD.postValue(AAWalletStatusWrapper(AAWalletStatus.UNAVAILABLE, 0L))
            } else if (!effective && timestamp > 0L) {
                aaWalletStatusLD.postValue(AAWalletStatusWrapper(AAWalletStatus.SELF_RECOVERING, timestamp))
            }

        }
    }

    fun stopRecovery(address: String) {
        addLaunch(true) {
            val timestamp = TimeUtils.getNowMills().toString()
            P256AccountManager.stopRecovery(timestamp, address){
                stopRecoveryLD.postValue(timestamp)
            }
        }
    }

    fun saveAAWallet(address: String, email: String, effective: Boolean){
        addLaunch {
            var target = AppDatabase.getInstance(Utils.getApp()).walletDao().queryWalletByAddress(address)
            if (target == null) {
                val salt = P256AccountManager.computeAASalt()
                val name = WalletHelper.nameWallet()[0]
                val password = TokenUtil.createRandomPassword()
                val encodedPassword = EncryptUtil.encrypt(password)
                val wallet = Wallet(
                    address.toEvmAddress(),
                    name,
                    encodedPassword,
                    "",
                    false,
                    timestamp = TimeUtils.getNowMills().toString(),
                    aaSalt = salt,
                    email = email,
                    effective = effective
                )
                WalletHelper.saveWallet(wallet)
                target = AppDatabase.getInstance(Utils.getApp()).walletDao().queryWalletByAddress(wallet.address)
            }
            target?.let {
                WalletHelper.switchWallet(it)
                saveWalletLD.postValue(it)
            }
        }
    }

    fun verifyCode(email: String, code: String){
        addLaunch(true) {
            val address = Constant.currentWallet?.address ?: ""
            val signature = JsonRpcManager.verifyCode(address, email, code)
            signature?.let {
                verifyCodeLD.postValue(it)
            }
        }
    }

    fun bindEmail(email: String, signature: String) {
        addLaunchNoCancel {
            val timestamp = TimeUtils.getNowMills().toString()
            P256AccountManager.bindEmail(timestamp, email, signature){
                confirmLD.postValue(timestamp)
            }
        }
    }

    fun verifyCode(address: String, email: String, code: String) {
        addLaunch(true) {
            val signature = JsonRpcManager.verifyCode(address, email, code) ?: ""
            signatureLD.postValue(signature)
        }
    }

    fun bindEmailWithoutAuth(wallet: Wallet, email: String, signature: String) {
        addLaunch {
            val response = runCatching {
                P256AccountManager.bindEmailWithoutAuth(email, signature, true)
            }.getOrNull()
            val hash = response?.userOpHash
            if (!hash.isNullOrBlank()) {
                wallet.email = email
            }

            if (!hash.isNullOrBlank()) {
                val txHash = response.wait()?.transactionHash
                val status = if (!txHash.isNullOrBlank()) STATUS_SUCCESS else STATUS_FAILED
                if (status == STATUS_SUCCESS) {
                    WalletHelper.saveWallet(wallet)
                    val curWallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
                        .queryWalletByAddress(wallet.address)
                    if (curWallet != null) {
                        WalletHelper.switchWallet(curWallet)
                        SPUtils.getInstance().put(SPConstant.SP_FIRST_SWITCH_NET, false)
                        waitingEmailLD.postValue(true)
                        val timestamp = TimeUtils.getNowMills().toString()
                        Web3Delegate.insertTransaction(
                            timestamp,
                            txHash,
                            from = curWallet.address,
                            status = status,
                            type = ACTION_TYPE_BOUND_EMAIL,
                        )
                        EventBus.getDefault().post(ActionRefreshEvent())
                        return@addLaunch
                    }
                }
            }
            waitingEmailLD.postValue(false)
        }
    }

    fun queryRemainFeeGas(address: String) {
        addLaunch {
            val result = JsonRpcManager.getGasRemain(address)
            val gasFee = TokenUtil.weiToTokenBN(result?.remain ?: "0")
            freeGasLD.postValue(gasFee)
        }
    }

    fun getRemainFeeGas(address: String? = null) {
        addLaunch {
            val wallet = WalletHelper.getCurWallet()
            if (address?.isAddress() != true && wallet?.isAAWallet() != true) {
                return@addLaunch
            }
            var walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(wallet?.address ?: "", WalletHelper.getCurChainId())

            if (!walletCache?.feeGas.isNullOrEmpty()) {
                freeGasLD.postValue(walletCache?.feeGas)
            }
            val account = address ?: wallet?.address ?: return@addLaunch
            val result = JsonRpcManager.getGasRemain(account)
            val gasFee = TokenUtil.weiToTokenBN(result?.remain ?: "0")
            freeGasLD.postValue(gasFee)
            //query again, data maybe change.
            walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(wallet?.address ?: "", WalletHelper.getCurChainId())
            if (walletCache == null) {
                walletCache = WalletCache(
                    WalletHelper.getCurChainId(),
                    wallet?.address ?: "",
                    feeGas = gasFee
                )
            } else {
                walletCache.feeGas = gasFee
            }
            AppDatabase.getInstance(Utils.getApp()).walletCacheDao().insertOrUpdate(walletCache)
        }
    }

    fun applyGasFee(address: String) {
        addLaunch {
            val result = JsonRpcManager.requestGas(address)
            applyGasLD.postValue(result)
        }
    }

    fun queryBoundAddresses(email: String) {
        addLaunch(true) {
            val chainId = WalletHelper.getCurChainId()
            val config = AppDatabase.getInstance(Utils.getApp()).networkAAConfigDao().queryByChainId(chainId)
            val subgraphUrl = config?.subgraph ?: Config.SUBGRAPH
            val emailHash = Hash.sha3(email.toByteArray()).toHexString()
            val query = """
                { 
                    "query": "{emailAccounts(where: {email: \"$emailHash\"}) { id account email }}" 
                }
            """.trimIndent()
            val body = query.toRequestBody("application/json".toMediaType())
            val response = apiService.queryBoundAddresses(subgraphUrl, body)
            emailAccountsLD.postValue(response.data.emailAccounts.asReversed())
        }
    }

    fun checkService() {
        addLaunch {
            val emailStatus = NetworkUtils.pingNetwork(NetworkUtils.getDomain(EMAIL_SERVICE))
            if (!emailStatus.available) {
                aaServiceLD.postValue(false)
                return@addLaunch
            }
            val bundlerStatus = NetworkUtils.pingNetwork(NetworkUtils.getDomain(BUNDLER_SERVICE))
            if (!bundlerStatus.available) {
                aaServiceLD.postValue(false)
                return@addLaunch
            }
            val paymasterStatus = NetworkUtils.pingNetwork(NetworkUtils.getDomain(PAYMASTER_SERVICE))
            if (!paymasterStatus.available) {
                aaServiceLD.postValue(false)
                return@addLaunch
            }
            aaServiceLD.postValue(true)
        }
    }


}

@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD, AnnotationTarget.FUNCTION)
@MustBeDocumented
@IntDef(
    AAWalletStatus.SELF_RECOVERING,
    AAWalletStatus.OTHER_RECOVERING,
    AAWalletStatus.AVAILABLE,
    AAWalletStatus.UNAVAILABLE,
    AAWalletStatus.UNBOUND
)
@Retention(AnnotationRetention.SOURCE)
annotation
class AAWalletStatus {
    companion object {
        const val SELF_RECOVERING = 1
        const val OTHER_RECOVERING = 2
        const val AVAILABLE = 3
        const val UNAVAILABLE = 4
        const val UNBOUND = 5
    }
}

data class AAWalletStatusWrapper(
    @AAWalletStatus val status: Int,
    val recoveryTimestamp: Long
)