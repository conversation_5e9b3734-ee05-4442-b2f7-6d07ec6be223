package io.iotex.iopay.wallet.viewmodel

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import com.google.gson.Gson
import io.iotex.base.RetrofitClient
import io.iotex.iopay.R
import io.iotex.iopay.api.IoPayApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.IOPAY_URL
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.iopay.wallet.web3.aawallet.P256AccountManager
import java.math.BigDecimal
import java.math.BigInteger

class TransferViewModel(application: Application) : BaseLaunchVM(application) {

    private val apiService by lazy {
        RetrofitClient.createApiService(IOPAY_URL, IoPayApi::class.java)
    }

    val spaceIdLiveData = MutableLiveData<String>()
    val confirmLiveData = MutableLiveData<String>()
    val transferLiveData = MutableLiveData<TransferBean>()
    val receiveWarningLiveData = MutableLiveData<String>()
    val networkLiveData = MutableLiveData<RPCNetwork>()
    val changeNetworkLiveData = MutableLiveData<RPCNetwork>()

    fun getSpaceId(name: String) {
        addLaunch {
            val map = HashMap<String, Any>().apply {
                put("batch", 1)
                put("input", Gson().toJson(HashMap<String, Any>().apply {
                    put("0", HashMap<String, Any>().apply {
                        put("json", HashMap<String, Any>().apply {
                            put("name", name)
                            put("chainId", UserStore.getChainId().toString())
                        })
                    })
                }))
            }
            val result = apiService.getSpaceId(map)
            if (result.isNotEmpty()) {
                val address = result[0].result?.data?.json?.address ?: ""
                spaceIdLiveData.postValue(address)
            }
        }
    }

    fun checkContract(receiptAddress: String) {
        addLaunch {
            val balance = Web3Delegate.getCurrencyBalance(receiptAddress)
            val action = AppDatabase.getInstance(Utils.getApp()).actionRecordDao().queryAll(receiptAddress)
            if(Web3Delegate.isContract(receiptAddress.toEvmAddress())){
                receiveWarningLiveData.postValue(Utils.getApp().getString(R.string.receipt_contract))
            }else if(balance.isNotEmpty() && balance[0] == BigInteger.ZERO && action.isEmpty()){
                receiveWarningLiveData.postValue(Utils.getApp().getString(R.string.this_is_not_an_address_you_interacted))
            } else {
                receiveWarningLiveData.postValue("")
            }
        }
    }

    fun curTransfer(contract: String) {
        addLaunch {
            val address = WalletHelper.getCurWallet()?.getCurNetworkAddress()
            val chainId = WalletHelper.getCurChainId()
            val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(address ?: "", chainId)
            val addressEvm = WalletHelper.getCurWallet()?.address
            val token = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                .queryByAddress(chainId, contract)
            val tokenCache = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao()
                .queryByAddress(addressEvm ?: "", chainId, contract)
            if (token == null) {
                val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(WalletHelper.getCurChainId())
                transferLiveData.postValue(
                    TransferBean(
                        network?.currencyPrice.asBigDecimal(),
                        walletCache?.balance ?: "",
                        walletCache?.availableBalance ?: "",
                        network?.currencyDecimals?:18,
                        network?.currencySymbol ?: "",
                        network?.logo ?: "",
                        "",
                    )
                )
            } else {
                transferLiveData.postValue(
                    TransferBean(
                        token.price.asBigDecimal(),
                        tokenCache?.balance?:"",
                        walletCache?.availableBalance ?: "",
                        token.decimals,
                        token.symbol,
                        token.logo,
                        tokenCache?.pubkey ?: ""
                    )
                )
            }

        }
    }

    fun curNetWork(change: Boolean) {
        addLaunch {
            val network = WalletHelper.getCurNetwork() ?: AppDatabase.getInstance(Utils.getApp())
                .rpcNetworkDao().queryRPCNetworkByChainId(Config.IOTEX_CHAIN_ID)
            network?.let {
                if (change) {
                    changeNetworkLiveData.postValue(it)
                } else {
                    networkLiveData.postValue(it)
                }
            }
        }
    }

    fun transferCurrency(
        timestamp:String,
        to: String,
        amount: String,
        data: String,
        gasLimit: BigInteger,
        gasPrice: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        oldNonce: BigInteger? = null,
        cancel: Boolean = false,
    ) {
        addLaunchNoCancel {
            val wallet = WalletHelper.getCurWallet() ?: return@addLaunchNoCancel
            val value = TokenUtil.toWei(amount, UserStore.getNetworkDecimals())
            if (wallet.isAAWallet()) {
                P256AccountManager.transfer(timestamp, to.toEvmAddress(), value, data) {
                    confirmLiveData.postValue(timestamp)
                }
            } else if (WalletHelper.isSolanaNetwork()) {
                SolanaWeb3.transferSOL(timestamp,to, value.toLong()){
                    confirmLiveData.postValue(timestamp)
                }
            } else {
                if (maxFeePerGas != BigInteger.ZERO) {
                    Web3Delegate.transferCurrency1559(
                        oldNonce,
                        cancel,
                        timestamp,
                        to.toEvmAddress(),
                        value,
                        maxPriorityFeePerGas,
                        maxFeePerGas,
                        gasPrice,
                        gasLimit
                    ) {
                        confirmLiveData.postValue(timestamp)
                    }
                } else {
                    Web3Delegate.transferCurrency(
                        oldNonce,
                        cancel,
                        timestamp,
                        to.toEvmAddress(),
                        value,
                        data,
                        gasPrice,
                        gasLimit
                    ) {
                        confirmLiveData.postValue(timestamp)
                    }
                }
            }
        }
    }

    fun transferERC20(
        timestamp:String,
        to: String,
        contract: String,
        pubkey: String,
        amount: String,
        decimals: Long,
        symbol: String,
        gasLimit: BigInteger,
        gasPrice: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        oldNonce: BigInteger = BigInteger.ZERO,
        cancel: Boolean = false,
    ) {
        addLaunchNoCancel {
            val wallet = WalletHelper.getCurWallet() ?: return@addLaunchNoCancel
            if (wallet.isAAWallet()) {
                P256AccountManager.transferErc20(
                    timestamp,
                    to.toEvmAddress(),
                    contract,
                    amount,
                    decimals,
                    symbol
                ) {
                    confirmLiveData.postValue(timestamp)
                }
            } else if (WalletHelper.isSolanaNetwork()) {
                SolanaWeb3.transferSPL(timestamp, contract, pubkey, to, amount, decimals, symbol) {
                    confirmLiveData.postValue(timestamp)
                }
            } else {
                if (maxFeePerGas != BigInteger.ZERO) {
                    Web3Delegate.transferErc20By1559(
                        oldNonce,
                        cancel,
                        timestamp,
                        contract,
                        to.toEvmAddress(),
                        amount,
                        decimals,
                        symbol,
                        maxPriorityFeePerGas,
                        maxFeePerGas,
                        gasPrice,
                        gasLimit
                    ) {
                        confirmLiveData.postValue(timestamp)
                    }
                } else {
                    Web3Delegate.transferErc20(
                        oldNonce,
                        cancel,
                        timestamp,
                        contract,
                        to.toEvmAddress(),
                        amount,
                        decimals,
                        symbol,
                        gasPrice,
                        gasLimit
                    ) {
                        confirmLiveData.postValue(timestamp)
                    }
                }
            }
        }
    }

    fun transferERC721(
        timestamp:String,
        to: String,
        contract: String,
        name: String,
        symbol: String,
        tokenId: BigInteger,
        gasLimit: BigInteger,
        gasPrice: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        oldNonce: BigInteger = BigInteger.ZERO,
        cancel: Boolean = false,
    ) {
        addLaunch {
            val wallet = WalletHelper.getCurWallet() ?: return@addLaunch
            if (wallet.isAAWallet()) {
                P256AccountManager.transferERC721(
                    timestamp,
                    to.toEvmAddress(),
                    contract,
                    name,
                    symbol,
                    tokenId
                ) {
                    confirmLiveData.postValue(timestamp)
                }
            } else {
                if (maxFeePerGas != BigInteger.ZERO) {
                    Web3Delegate.transferErc721By1559(
                        oldNonce,
                        cancel,
                        timestamp,
                        contract,
                        to,
                        name,
                        symbol,
                        tokenId,
                        maxPriorityFeePerGas,
                        maxFeePerGas,
                        gasPrice,
                        gasLimit,
                    ){
                        confirmLiveData.postValue(timestamp)
                    }
                } else {
                    Web3Delegate.transferErc721(
                        oldNonce,
                        cancel,
                        timestamp,
                        contract,
                        to.toEvmAddress(),
                        name,
                        symbol,
                        tokenId,
                        gasPrice,
                        gasLimit
                    ){
                        confirmLiveData.postValue(timestamp)
                    }
                }
            }
        }
    }

    fun transferERC1155(
        timestamp:String,
        to: String,
        contract: String,
        name: String,
        symbol: String,
        tokenId: BigInteger,
        value: BigInteger,
        gasLimit: BigInteger,
        gasPrice: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        oldNonce: BigInteger = BigInteger.ZERO,
        cancel: Boolean = false,
    ) {
        addLaunch {
            val wallet = WalletHelper.getCurWallet() ?: return@addLaunch
            if (wallet.isAAWallet()) {
                P256AccountManager.transferERC1155(
                    timestamp,
                    to.toEvmAddress(),
                    contract,
                    name,
                    symbol,
                    tokenId,
                    value
                ) {
                    confirmLiveData.postValue(timestamp)
                }
            } else {
                if (maxFeePerGas != BigInteger.ZERO) {
                    Web3Delegate.transferErc1155By1559(
                        oldNonce,
                        cancel,
                        timestamp,
                        contract,
                        to,
                        name,
                        symbol,
                        tokenId,
                        value,
                        maxPriorityFeePerGas,
                        maxFeePerGas,
                        gasPrice,
                        gasLimit
                    ) {
                        confirmLiveData.postValue(timestamp)
                    }
                } else {
                    Web3Delegate.transferErc1155(
                        oldNonce,
                        cancel,
                        timestamp,
                        contract,
                        to.toEvmAddress(),
                        name,
                        symbol,
                        tokenId,
                        value,
                        gasPrice,
                        gasLimit
                    ) {
                        confirmLiveData.postValue(timestamp)
                    }
                }
            }
        }
    }

}