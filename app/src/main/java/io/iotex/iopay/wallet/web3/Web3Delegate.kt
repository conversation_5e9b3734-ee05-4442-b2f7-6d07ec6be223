package io.iotex.iopay.wallet.web3

import android.os.Bundle
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.lockscreen.LockAuthHelper
import io.iotex.base.okHttpClient
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_NFT
import io.iotex.iopay.data.db.ACTION_TYPE_TRANSFER
import io.iotex.iopay.data.db.ActionRecordEntry
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.NFT_TYPE_1155
import io.iotex.iopay.data.db.NFT_TYPE_721
import io.iotex.iopay.data.db.STATUS_FAILED
import io.iotex.iopay.data.db.STATUS_SUCCESS
import io.iotex.iopay.data.db.STATUS_WAITING
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.data.db.WalletCache
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.DialogUtil
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.WalletHelper.convertWeb3Address
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.util.transferTypeToEvent
import io.iotex.iopay.viewmodel.showContractError
import io.iotex.web3.SignerManager
import io.iotex.web3.Web3Manger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.Type
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.DefaultBlockParameterName
import org.web3j.protocol.core.Response
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.core.methods.response.EthSendTransaction
import org.web3j.protocol.core.methods.response.TransactionReceipt
import org.web3j.protocol.http.HttpService
import org.web3j.utils.Numeric
import java.math.BigInteger
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

object Web3Delegate : SignerManager {

    private lateinit var rpc: String
    private var chainId: Int = 0
    private var address = ""

    private lateinit var instance: Web3Manger

    private val web3Manger: Web3Manger
        get() = runBlocking {
            withContext(Dispatchers.IO) {
                return@withContext createWeb3Manager()
            }
        }

    private fun createWeb3Manager(): Web3Manger {
        val wallet = WalletHelper.getCurWallet()
        val network = WalletHelper.getCurNetwork()
        if (
            !this@Web3Delegate::instance.isInitialized ||
            network?.rpc != rpc ||
            network.chainId != chainId ||
            wallet?.address != address
        ) {
            rpc = network?.rpc ?: Config.IOTEX_RPC_URL
            chainId = network?.chainId ?: Config.IOTEX_CHAIN_ID
            address = wallet?.address ?: Address.DEFAULT.value
            val web3j = Web3j.build(HttpService(rpc, okHttpClient))
            instance = Web3Manger.build(web3j, chainId.toLong(), address, this)
        }
        return instance
    }

    fun getCurrencyBalance(vararg address: String): List<BigInteger> {
        val chainId = WalletHelper.getCurChainId()
        val balanceList = runCatching {
            web3Manger.getCurrencyBalance(*address)
        }.getOrNull()?: emptyList()
        balanceList.forEachIndexed { index, bigInteger ->
            var walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(address[index], chainId)
            if (walletCache == null) {
                walletCache = WalletCache(chainId, address[index], bigInteger.toString())
            } else {
                walletCache.balance = bigInteger.toString()
            }
            if (chainId != WalletHelper.getCurChainId()) return emptyList()
            AppDatabase.getInstance(Utils.getApp()).walletCacheDao().insertOrUpdate(walletCache)
        }
        return balanceList
    }

    fun getErc20Balance(vararg contract: String): List<BigInteger?> {
        return web3Manger.getErc20Balance(*contract)
    }

    fun checkApprove(contract: String,spender:String): BigInteger {
        return web3Manger.checkTokenApprove(contract,spender)
    }

    fun getNetworkBalance(chainId: Int, rpc: String, address: String): BigInteger {
        val web3j = Web3j.build(HttpService(rpc, okHttpClient))
        val request =
            web3j.ethGetBalance(address, DefaultBlockParameterName.LATEST)
        val batchResponse = runCatching { request.send() }.getOrNull() ?: return BigInteger.ZERO
        val balance = batchResponse.balance
        var cache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
            .queryWalletCache(address, chainId)
        if (cache == null) {
            cache = WalletCache(chainId, address, balance.toString())
        } else {
            cache.balance = balance.toString()
        }
        AppDatabase.getInstance(Utils.getApp()).walletCacheDao().insertOrUpdate(cache)
        return balance
    }

    suspend fun transferCurrency(
        oldNonce: BigInteger?,
        cancel: Boolean,
        timestamp:String,
        to: String,
        value: BigInteger,
        data: String,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        confirm: ()->Unit
    ) {
        val toAddress = if(cancel) UserStore.getWalletAddress() else to
        val toValue = if(cancel) BigInteger.ZERO else value
        val ethTransaction = web3Manger.transferCurrency(oldNonce, toAddress, toValue, data, gasPrice, gasLimit){
            confirm.invoke()
            insertTransaction(
                timestamp,
                "",
                it.toString(),
                WalletHelper.getCurWallet()?.address ?: "",
                to,
                TokenUtil.weiToTokenBN(value.toString()),
                data,
                gasPrice.toString(),
                gasLimit.toString(),
                "",
                "",
                STATUS_WAITING,
                ACTION_TYPE_TRANSFER,
                symbol = UserStore.getNetworkSymbol(),
            )
        }
        val hash = ethTransaction?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(ethTransaction?.error?.message)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        if(hash.isNullOrBlank() && cancel) return
        updateAction(
            timestamp,
            hash,
            status,
            cancel
        )
    }

    suspend fun transferCurrency1559(
        oldNonce: BigInteger?,
        cancel: Boolean,
        timestamp:String,
        to: String,
        value: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        confirm: ()->Unit
    ) {
        val toAddress = if(cancel) UserStore.getWalletAddress() else to
        val toValue = if(cancel) BigInteger.ZERO else value
        val ethTransaction = web3Manger.transferCurrency1559(oldNonce, toAddress, toValue, maxPriorityFeePerGas, maxFeePerGas, gasLimit){
            confirm.invoke()
            insertTransaction(
                timestamp,
                "",
                it.toString(),
                WalletHelper.getCurWallet()?.address ?: "",
                to,
                TokenUtil.weiToTokenBN(value.toString()),
                "",
                gasPrice.toString(),
                gasLimit.toString(),
                maxPriorityFeePerGas.toString(),
                maxFeePerGas.toString(),
                STATUS_WAITING,
                ACTION_TYPE_TRANSFER,
                symbol = UserStore.getNetworkSymbol(),
            )
        }
        val hash = ethTransaction?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(ethTransaction?.error?.message)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        if(hash.isNullOrBlank() && cancel) return
        updateAction(
            timestamp,
            hash,
            status,
            cancel
        )
    }

    suspend fun transferErc20(
        oldNonce: BigInteger,
        cancel: Boolean,
        timestamp:String,
        contract: String,
        to: String,
        amount: String,
        decimals: Long,
        symbol: String,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        confirm: ()->Unit
    ) {
        val value = TokenUtil.toWei(amount, decimals.toInt())
        val toAddress = if(cancel) UserStore.getWalletAddress() else to
        val toValue = if(cancel) BigInteger.ZERO else value
        val ethTransaction = web3Manger.transferErc20(oldNonce, contract, toAddress, toValue, gasPrice, gasLimit){
            confirm.invoke()
            insertTransaction(
                timestamp,
                "",
                it.toString(),
                WalletHelper.getCurWallet()?.address ?: "",
                to,
                TokenUtil.weiToTokenBN(value.toString(), decimals),
                "",
                gasPrice.toString(),
                gasLimit.toString(),
                "",
                "",
                STATUS_WAITING,
                ACTION_TYPE_TRANSFER,
                contract,
                symbol = symbol,
            )
        }
        val hash = ethTransaction?.transactionHash
        val status = if (hash.isNullOrBlank() && oldNonce == BigInteger.ZERO) {
            showContractError(ethTransaction?.error?.message)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        if(hash.isNullOrBlank() && cancel) return
        updateAction(
            timestamp,
            hash,
            status,
            cancel
        )
        val bundle = Bundle()
        bundle.putString("type", "TRANSFER")
        bundle.putString("coin", "Erc20")
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_EXECUTE, bundle)
    }

    suspend fun transferErc20By1559(
        oldNonce: BigInteger,
        cancel: Boolean,
        timestamp:String,
        contract: String,
        to: String,
        amount: String,
        decimals: Long,
        symbol: String,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        confirm: ()->Unit
    ) {
        val value = TokenUtil.toWei(amount, decimals.toInt())
        val toAddress = if(cancel) UserStore.getWalletAddress() else to
        val toValue = if(cancel) BigInteger.ZERO else value
        val ethTransaction = web3Manger.transferErc20By1559(oldNonce, contract, toAddress, toValue, maxPriorityFeePerGas, maxFeePerGas, gasLimit){
            confirm.invoke()
            insertTransaction(
                timestamp,
                "",
                it.toString(),
                WalletHelper.getCurWallet()?.address ?: "",
                to,
                TokenUtil.weiToTokenBN(value.toString(), decimals),
                "",
                gasPrice.toString(),
                gasLimit.toString(),
                maxPriorityFeePerGas.toString(),
                maxFeePerGas.toString(),
                STATUS_WAITING,
                ACTION_TYPE_TRANSFER,
                contract,
                symbol = symbol,
            )
        }
        val hash = ethTransaction?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(ethTransaction?.error?.message)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        if(hash.isNullOrBlank() && cancel) return
        updateAction(
            timestamp,
            hash,
            status,
            cancel
        )
        val bundle = Bundle()
        bundle.putString("type", "TRANSFER")
        bundle.putString("coin", "Erc20")
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_EXECUTE, bundle)
    }

    suspend fun transferErc721(
        oldNonce: BigInteger,
        cancel: Boolean,
        timestamp:String,
        contract: String,
        to: String,
        name: String,
        symbol: String,
        tokenId: BigInteger,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        confirm: ()->Unit
    ) {
        val toAddress = if(cancel) UserStore.getWalletAddress() else to
        val ethTransaction = web3Manger.transferErc721(oldNonce, contract, UserStore.getWalletAddress(), toAddress, tokenId, gasPrice, gasLimit){
            insertTransaction(
                timestamp,
                "",
                it.toString(),
                WalletHelper.getCurWallet()?.address ?: "",
                to,
                "1",
                "",
                gasPrice.toString(),
                gasLimit.toString(),
                "",
                "",
                STATUS_WAITING,
                ACTION_TYPE_NFT,
                contract,
                "",
                name,
                tokenId.toString(),
                NFT_TYPE_721,
                symbol,
            )
            confirm.invoke()
        }
        val hash = ethTransaction?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(ethTransaction?.error?.message)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        if(hash.isNullOrBlank() && cancel) return
        updateAction(
            timestamp,
            hash,
            status,
            cancel
        )
        val bundle = Bundle()
        bundle.putString("type", "NFT")
        bundle.putString("coin", "Erc721")
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_EXECUTE, bundle)
    }

    suspend fun transferErc721By1559(
        oldNonce: BigInteger,
        cancel: Boolean,
        timestamp:String,
        contract: String,
        to: String,
        name: String,
        symbol: String,
        tokenId: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        confirm: ()->Unit
    ) {
        val toAddress = if(cancel) UserStore.getWalletAddress() else to
        val ethTransaction = web3Manger.transferErc721By1559(oldNonce, contract, UserStore.getWalletAddress(), toAddress, tokenId, maxPriorityFeePerGas, maxFeePerGas, gasLimit){
            confirm.invoke()
            insertTransaction(
                timestamp,
                "",
                it.toString(),
                WalletHelper.getCurWallet()?.address ?: "",
                to,
                "1",
                "",
                gasPrice.toString(),
                gasLimit.toString(),
                maxPriorityFeePerGas.toString(),
                maxFeePerGas.toString(),
                STATUS_WAITING,
                ACTION_TYPE_NFT,
                contract,
                "",
                name,
                tokenId.toString(),
                NFT_TYPE_721,
                symbol,
            )
        }
        val hash = ethTransaction?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(ethTransaction?.error?.message)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        if(hash.isNullOrBlank() && cancel) return
        updateAction(
            timestamp,
            hash,
            status,
            cancel
        )
        val bundle = Bundle()
        bundle.putString("type", "NFT")
        bundle.putString("coin", "Erc721")
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_EXECUTE, bundle)
    }

    suspend fun transferErc1155(
        oldNonce: BigInteger,
        cancel: Boolean,
        timestamp: String,
        contract: String,
        to: String,
        name: String,
        symbol: String,
        tokenId: BigInteger,
        value: BigInteger,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        confirm: ()->Unit
    ) {
        val toAddress = if(cancel) UserStore.getWalletAddress() else to
        val toValue = if(cancel) BigInteger.ZERO else value
        val ethTransaction =
            web3Manger.transferErc1155(oldNonce, contract, UserStore.getWalletAddress(), toAddress, tokenId, toValue, gasPrice, gasLimit){
                confirm.invoke()
                insertTransaction(
                    timestamp,
                    "",
                    it.toString(),
                    WalletHelper.getCurWallet()?.address ?: "",
                    to,
                    value.toString(),
                    "",
                    gasPrice.toString(),
                    gasLimit.toString(),
                    "",
                    "",
                    STATUS_WAITING,
                    ACTION_TYPE_NFT,
                    contract,
                    "",
                    name,
                    tokenId.toString(),
                    NFT_TYPE_1155,
                    symbol,
                )
            }
        val hash = ethTransaction?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(ethTransaction?.error?.message)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        if(hash.isNullOrBlank() && cancel) return
        updateAction(
            timestamp,
            hash,
            status,
            cancel
        )
        val bundle = Bundle()
        bundle.putString("type", "NFT")
        bundle.putString("coin", "Erc1155")
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_EXECUTE, bundle)
    }

    suspend fun transferErc1155By1559(
        oldNonce: BigInteger,
        cancel: Boolean,
        timestamp: String,
        contract: String,
        to: String,
        name: String,
        symbol: String,
        tokenId: BigInteger,
        value: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        confirm: ()->Unit
    ) {
        val toAddress = if(cancel) UserStore.getWalletAddress() else to
        val toValue = if(cancel) BigInteger.ZERO else value
        val ethTransaction =
            web3Manger.transferErc1155By1559(oldNonce, contract, UserStore.getWalletAddress(), toAddress, tokenId, toValue, maxPriorityFeePerGas, maxFeePerGas, gasLimit){
                confirm.invoke()
                insertTransaction(
                    timestamp,
                    "",
                    it.toString(),
                    WalletHelper.getCurWallet()?.address ?: "",
                    to,
                    value.toString(),
                    "",
                    gasPrice.toString(),
                    gasLimit.toString(),
                    maxPriorityFeePerGas.toString(),
                    maxFeePerGas.toString(),
                    STATUS_WAITING,
                    ACTION_TYPE_NFT,
                    contract,
                    "",
                    name,
                    tokenId.toString(),
                    NFT_TYPE_1155,
                    symbol,
                )
            }
        val hash = ethTransaction?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(ethTransaction?.error?.message)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        if(hash.isNullOrBlank() && cancel) return
        updateAction(
            timestamp,
            ethTransaction?.transactionHash ?: "",
            status,
            cancel
        )
        val bundle = Bundle()
        bundle.putString("type", "NFT")
        bundle.putString("coin", "Erc1155")
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_EXECUTE, bundle)
    }

    suspend fun executeCall(contract: String, data: String): String? {
        return web3Manger.executeCall(contract, data)
    }

    fun <R> executeCall(contract: String, function: Function, returnType: Class<R>): R? {
        return web3Manger.executeCall(contract, function, returnType)
    }

    fun executeCallForMultipleValue(contract: String, function: Function): List<Type<*>> {
        return web3Manger.executeCallForMultipleValue(contract, function)
    }

    suspend fun executeRawTransaction1559(
        contract: String,
        value: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        gasLimit: BigInteger,
        `data`: String
    ): EthSendTransaction? {
        val ethTransaction = web3Manger.executeRawTransaction1559(contract, value, maxPriorityFeePerGas, maxFeePerGas, gasLimit, data)
        showContractError(ethTransaction?.error?.message)
        return ethTransaction
    }

    suspend fun executeRawTransactionWithAction(
        timestamp: String,
        to: String,
        value: BigInteger,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        `data`: String,
        type: Int,
        decodeTo: String?,
        decodeContract: String?,
        decodeValue: String?,
        origin: String,
        confirm: ()->Unit,
        response: ((String?, Response.Error?) -> Unit)
    ) {
        val methodCode = resolveMethodCode(`data`)
        val displayValue =
            if (decodeValue.isNullOrEmpty())
                TokenUtil.weiToTokenBN(value.toString())
            else decodeValue
        var symbol = UserStore.getNetworkSymbol()
        var nameApprove = ""
        if(value == BigInteger.ZERO){
            val erc20Entry = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                .queryByAddress(WalletHelper.getCurChainId(), decodeContract ?: "")
            symbol = erc20Entry?.symbol?:""
            nameApprove = erc20Entry?.symbol?:""
        }
        val ethTransaction = web3Manger.executeRawTransaction(to, value, gasPrice, gasLimit, data){
            confirm.invoke()
            insertTransaction(
                timestamp,
                "",
                "",
                WalletHelper.getCurWallet()?.address ?: "",
                decodeTo?:to,
                displayValue,
                data,
                gasPrice.toString(),
                gasLimit.toString(),
                "",
                "",
                STATUS_WAITING,
                type,
                decodeContract?:"",
                methodCode,
                nameApprove,
                "",
                "",
                symbol,
                "",
                origin
            )
        }
        val hash = ethTransaction?.transactionHash
        if (hash.isNullOrBlank()) {
            if(ethTransaction?.error?.code == -32000){
                response.invoke(hash, Response.Error(-32000, Utils.getApp().getString(R.string.insufficient_balance_for_this_action)))
            } else{
                response.invoke(hash, Response.Error(100, ethTransaction?.error?.message))
            }
        } else {
            response.invoke(hash, null)
        }
        val status = if (hash.isNullOrBlank()) {
            if(ethTransaction?.error?.code == -32000){
                showContractError(Utils.getApp().getString(R.string.insufficient_balance_for_this_action))
            } else{
                showContractError(ethTransaction?.error?.message)
            }
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }

        updateAction(
            timestamp,
            hash,
            status,
        )
        val bundle = Bundle()
        bundle.putString("type", transferTypeToEvent(type))
        bundle.putString("origin", origin)
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_EXECUTE, bundle)
    }

    suspend fun executeRawTransactionWithAction1559(
        timestamp: String,
        contract: String,
        value: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        gasLimit: BigInteger,
        `data`: String,
        type: Int,
        decodeContract: String?,
        decodeValue: String?,
        origin: String
    ): EthSendTransaction? {
        val ethSendTransaction =
            executeRawTransaction1559(
                contract, value, maxPriorityFeePerGas, maxFeePerGas, gasLimit, data
            ) ?: return null
        if (ethSendTransaction.error != null) {
            showContractError(ethSendTransaction.error.message ?: "")
        } else {
            CoroutineScope(Dispatchers.IO).launch {
                queryTransactionReceipt(ethSendTransaction.transactionHash)
            }
        }
        val bundle = Bundle()
        bundle.putString("type", transferTypeToEvent(type))
        bundle.putString("origin", origin)
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_EXECUTE, bundle)
        return ethSendTransaction
    }

    fun insertTransaction(
        timestamp: String,
        hash: String? = "",
        nonce: String = "",
        from: String = "",
        to: String = "",
        value: String = "",
        data: String = "",
        gasPrice: String = "",
        gasLimit: String = "",
        maxPriorityFee: String = "",
        maxFeePerGas: String = "",
        status: Int = STATUS_WAITING,
        type: Int = ACTION_TYPE_TRANSFER,
        contract: String = "",
        method: String = "",
        name: String = "",
        tokenId: String = "",
        tokenType: String = "",
        symbol: String = "",
        decimals: String = "18",
        origin: String = "",
        cancel: Boolean = false,
        showDialog: Boolean = true,
    ) {
        val chainId = WalletHelper.getCurChainId()
        ActionRecordEntry(
            timestamp,
            chainId,
            hash ?: "",
            nonce,
            from.toEvmAddress(),
            to.toEvmAddress(),
            value,
            data,
            gasPrice,
            gasLimit,
            maxPriorityFee,
            maxFeePerGas,
            status,
            type,
            contract,
            method,
            name,
            tokenId,
            tokenType,
            symbol,
            decimals,
            origin,
            cancel
        ).apply {
            val action = AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
                .queryByTimestamp(chainId, timestamp)
            if (action?.status == STATUS_SUCCESS) return
            if (!action?.hash.isNullOrBlank()) {
                this.hash = action?.hash ?: ""
            }
            if (action?.cancel == true) {
                this.cancel = true
            }
            AppDatabase.getInstance(Utils.getApp()).actionRecordDao().insertOrReplace(this)
            if(showDialog){
                DialogUtil.showTransferLoading(timestamp)
                EventBus.getDefault().post(ActionRefreshEvent())
            }
        }
    }

    fun updateAction(
        timestamp: String,
        hash: String? = "",
        status: Int = STATUS_WAITING,
        cancel: Boolean = false
    ){
        val action = AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
            .queryByTimestamp(WalletHelper.getCurChainId(), timestamp)
        if (action?.status == STATUS_SUCCESS && status == STATUS_FAILED) return
        //iotex new hash query status fail.
        if (WalletHelper.isIoTexNetWork() && !action?.hash.isNullOrEmpty()) {
            action?.hash = action?.hash ?: ""
        } else {
            action?.hash = hash ?: ""
        }
        action?.status = status
        if (action?.cancel == false) {
            action.cancel = cancel
        }
        action?.let {
            AppDatabase.getInstance(Utils.getApp()).actionRecordDao().insertOrReplace(action)
            DialogUtil.showTransferLoading(timestamp)
            EventBus.getDefault().post(ActionRefreshEvent())
        }
    }

    fun queryTransactionReceipt(transactionHash: String?): TransactionReceipt? {
        if (transactionHash.isNullOrBlank()) return null
        val receipt = web3Manger.queryTransactionReceipt(transactionHash)
        return receipt?.apply {
            val action = AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
                .queryByHash(WalletHelper.getCurChainId(), transactionHash) ?: return@apply
            action.status = if (receipt.isStatusOK) STATUS_SUCCESS else STATUS_FAILED
            AppDatabase.getInstance(Utils.getApp()).actionRecordDao().insertOrReplace(action)
            EventBus.getDefault().post(ActionRefreshEvent())
            if(!receipt.isStatusOK){
                val bundleEvent = Bundle()
                bundleEvent.putString("hash", receipt.blockHash)
                bundleEvent.putString("errorMsg", receipt.revertReason)
                FireBaseUtil.logFireBase(
                    FireBaseEvent.ACTION_ERROR_CATCH,
                    bundleEvent
                )
            }
        }
    }

    fun gasPrice(): BigInteger {
        return web3Manger.gasPrice()
    }

    fun estimate(to: String, `data`: String,value:BigInteger): BigInteger {
        return web3Manger.estimate(to, `data`,value)
    }

    suspend fun signMessage(message: ByteArray, addPrefix: Boolean = false): String {
        return web3Manger.signMessage(message, addPrefix)
    }

    suspend fun signTypedData(data: String): String {
        return web3Manger.signTypedData(data)
    }

    fun isContract(contract: String): Boolean {
        return web3Manger.isContract(contract)
    }

    fun transactionNonce(): BigInteger? {
        return web3Manger.transactionNonce()
    }

    fun erc20Symbol(contract: String): String {
        return web3Manger.erc20Symbol(contract)
    }

    fun erc20Name(contract: String): String {
        return web3Manger.erc20Name(contract)
    }

    fun erc20Decimals(contract: String): Int {
        return web3Manger.erc20Decimals(contract)
    }

    fun nftOwnerOf(contract: String, tokenId: BigInteger): String? {
        return web3Manger.nftOwnerOf(contract, tokenId)
    }

    fun nftBalanceOf(contract: String, tokenId: BigInteger): Int {
        return web3Manger.nftBalanceOf(contract, tokenId)
    }

    fun resolveMethodCode(`data`: String?, method: String = Config.DEFAULT_METHOD_NAME): String {
        if (`data`.isNullOrEmpty()) return Utils.getApp().getString(R.string.transfer_send)
        if (!Numeric.containsHexPrefix(`data`)) return method

        if (`data`.length >= 10) {
            return `data`.substring(0, 10)
        }

        return method
    }

    fun resolveEns(contractId: String): String {
        return web3Manger.resolveEns(contractId)
    }

    fun insToAddress(name: String): String {
        return web3Manger.insToAddress(name)
    }

    fun addressToIns(address: String): String {
        val web3Address = convertWeb3Address(address)
        val reverseName = web3Address.lowercase().substring(2) + Constant.ADDRESS_REVERSE
        return web3Manger.addressToIns(reverseName)
    }

    fun bucketOf(contract: String, tokenId: BigInteger): List<String> {
        return web3Manger.bucketOf(contract,tokenId)
    }

    fun getLogs(ethFilter: EthFilter): EthLog? {
        return web3Manger.getLogs(ethFilter)
    }

    fun getBlockNumber(): BigInteger {
        return web3Manger.getBlockNumber()
    }

    @Throws(IllegalArgumentException::class)
    override suspend fun providePrivateKey(): String {
        return suspendCoroutine { continuation ->
            val wallet = WalletHelper.getCurWallet()
            val context = ActivityUtils.getTopActivity()
            if (wallet == null || context == null) {
                Utils.getApp().getString(R.string.error_get_account).toast()
                continuation.resumeWithException(IllegalArgumentException(Utils.getApp().getString(R.string.error_get_account)))
                return@suspendCoroutine
            }
            LockAuthHelper.showTransferAuth(
                context,
                onSuccess = {
                    resolvePrivateKey(wallet, continuation)
                },
                onCancel = {
                    continuation.resumeWithException(IllegalArgumentException(Utils.getApp().getString(R.string.cancel)))
                }
            )
        }
    }

    private fun resolvePrivateKey(wallet: Wallet, continuation: Continuation<String>) {
        WalletHelper.getAccountByWallet(wallet)?.let {
            continuation.resume(it.privateKey().toHexString())
        } ?: kotlin.run {
            Utils.getApp().getString(R.string.error_get_account).toast()
            continuation.resumeWithException(IllegalArgumentException(Utils.getApp().getString(R.string.error_get_account)))
        }
    }
}