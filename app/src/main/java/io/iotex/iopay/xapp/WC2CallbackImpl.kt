package io.iotex.iopay.xapp

import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.w3bstream.utils.extension.ellipsis
import com.machinefi.walletconnect2.WC2CallbackInterface
import com.machinefi.walletconnect2.WC2Config
import com.machinefi.walletconnect2.WC2Helper
import com.machinefi.walletconnect2.bean.ChainInfo
import com.machinefi.walletconnect2.bean.SolanaTransactionPam
import com.machinefi.walletconnect2.bean.TransactionBean
import com.solana.core.AccountMeta
import com.solana.core.PublicKey
import com.solana.core.TransactionInstruction
import io.iotex.iopay.R
import io.iotex.iopay.dapp.dialog.DAppNewWalletTipsDialog
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.network.widget.NetworkChainLogoView
import io.iotex.iopay.transaction.TransactionDialog
import io.iotex.iopay.transaction.bean.ExtraHead
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toHexByteArray
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.solana.SolanaWeb3Dialog
import io.iotex.iopay.xapp.wc.WcAuthActivity
import io.iotex.iopay.wallet.web3.Web3Repository
import io.iotex.iopay.xapp.trust.AddChainsUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.bitcoinj.core.Base58
import org.web3j.protocol.core.Response
import java.math.BigInteger

class WC2CallbackImpl : WC2CallbackInterface {

    private var dAppNewWalletTipsDialog: DAppNewWalletTipsDialog? = null
    override fun showConnect(
        icon: String,
        name: String,
        url: String,
        risk: String,
        chains: List<String>?
    ) {
        val address = UserStore.getWalletAddress()
        if (address.isEmpty() && dAppNewWalletTipsDialog == null) {
            dAppNewWalletTipsDialog = DAppNewWalletTipsDialog()
            val context = ActivityUtils.getTopActivity() as AppCompatActivity
            dAppNewWalletTipsDialog?.show(
                context.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
            return
        }
        NetworkChainLogoView.parseChainNetwork(chains){ networks->
            if (WalletHelper.isSolanaNetwork()
                && (chains?.contains(WC2Config.CONTRACT_SOLANA_MAIN) == true
                        || chains?.contains(WC2Config.CONTRACT_SOLANA_TEST) == true)
            ) {
                showWcAuthActivity(icon, name, url, risk, chains, networks)
            } else if (chains?.contains("${WC2Config.CONTRACT_EIP155_DOT}${WalletHelper.getCurChainId()}") == false) {
                networks.forEach { network->
                    checkChain("${WC2Config.CONTRACT_EIP155_DOT}${network.chainId}") {
                        if(it){
                            showWcAuthActivity(icon, name, url, risk, chains, networks)
                            return@checkChain
                        }
                    }
                }
            } else {
                showWcAuthActivity(icon, name, url, risk, chains, networks)
            }
        }
    }

    private fun showWcAuthActivity(
        icon: String,
        name: String,
        url: String,
        risk: String,
        chains: List<String>?,
        networks: ArrayList<RPCNetwork>
    ){
        val chainId = kotlin.runCatching { chains?.first()?.split(":")?.get(1) }.getOrNull()
        if (chainId != null && networks.isEmpty()) {
            ToastUtils.showShort(
                Utils.getApp().getString(
                    com.machinefi.walletconnect2.R.string.please_add_the_network_with_chain,
                    chainId
                )
            )
        } else {
            WcAuthActivity.start(icon, name, url, risk, chains)
        }
    }

    override fun sendTransaction(transactionBean: TransactionBean) {
        Web3Repository.signTransaction(
            transactionBean.id,
            transactionBean.from,
            transactionBean.to,
            transactionBean.data,
            transactionBean.value,
            transactionBean.name,
            transactionBean.logo,
            transactionBean.url,
            null,
            transactionBean.gas,
            "",
            null,
        ) { _, res, _ ->
            if (res != null) {
                WC2Helper.approveSession(transactionBean.id, res)
            } else {
                WC2Helper.rejectSession(transactionBean.id)
            }
        }
    }

    override fun signMessage(
        id: Long,
        name: String,
        logo: String,
        url: String,
        data: String,
        addPrefix: Boolean
    ) {
        if (data.toEvmAddress().lowercase() == WalletHelper.getWeb3Address().lowercase()) return
        Web3Repository.handleSignMessage(
            id,
            data.toHexByteArray(),
            addPrefix,
            data,
            appUrl = url,
            appLogo = logo,
            response = { _: Long, res: String?, _: Response.Error? ->
                if (res != null) {
                    WC2Helper.approveSession(id, res)
                } else {
                    WC2Helper.rejectSession(id)
                }
            }
        )
    }

    override fun signTypedDataV4(id: Long, name: String, logo: String, url: String, data: String) {
        Web3Repository.handleSignTypedData(
            id,
            data,
            appUrl = url,
            appLogo = logo,
            response = { _: Long, res: String?, _: Response.Error? ->
                if (res != null) {
                    WC2Helper.approveSession(id, res)
                } else {
                    WC2Helper.rejectSession(id)
                }
            }
        )
    }

    override fun addOrSwitchChain(
        id: Long,
        logo: String,
        name: String,
        url: String,
        chainInfo: ChainInfo
    ) {
        AddChainsUtil.addOrSwitchEthereumChain(logo, name, url, chainInfo) {
            if (it) {
                WC2Helper.approveSession(id,"")
                WC2Helper.updateSession(WalletHelper.getCurChainId(), UserStore.getWalletAddress())
            } else {
                WC2Helper.rejectSession(id)
            }
        }
    }

    private var transactionDialog: TransactionDialog? = null
    override fun solanaTranstion(
        id: Long,
        logo: String,
        name: String,
        url: String,
        solanaTransactionPam: SolanaTransactionPam
    ) {
        MainScope().launch {
            val list = ArrayList<OptionEntry>()
            list.add(
                OptionEntry(
                    Utils.getApp().getString(R.string.method),
                    Utils.getApp().getString(R.string.other_method)
                )
            )
            list.add(
                OptionEntry(
                    Utils.getApp().getString(R.string.contract_addr),
                    solanaTransactionPam.transaction?.ellipsis()?:""
                )
            )
            val extraHead = ExtraHead(logo, url, false)
            if (transactionDialog != null) return@launch
            transactionDialog =
                TransactionDialog("0x", BigInteger.ZERO, "", list, "", "", extraHead).apply {
                    onCancel = {
                        transactionDialog = null
                        WC2Helper.rejectSession(id)
                    }
                    onTransactionConfirm =
                        { _: Long, _: String, _, _ ->
                            transactionDialog?.dismiss()
                            transactionDialog = null
                            sendRawTransaction(id,name, solanaTransactionPam)
                        }
                }
            (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
                transactionDialog?.show(
                    it.supportFragmentManager,
                    System.currentTimeMillis().toString()
                )
            }
        }

    }

    override fun solanaSignMessage(
        id: Long,
        name: String,
        logo: String,
        url: String,
        data: String
    ) {
        SolanaWeb3Dialog.signMessage(logo, url, data) {
            if (it == null) {
                WC2Helper.rejectSession(id)
            } else {
                WC2Helper.approveSession(
                    id,
                    "{\"signature\": ${Base58.encode(it.toHexByteArray())}}"
                )
            }
        }
    }

    override fun solanaSignAllTransactions(
        id: Long,
        name: String,
        logo: String,
        url: String,
        transactions: String
    ) {
        SolanaWeb3Dialog.signAllTransactions(logo, url, transactions) {
            if (it == null) {
                WC2Helper.rejectSession(id)
            } else {
                WC2Helper.approveSession(
                    id,
                    it
                )
            }
        }
    }

    override fun checkChain(chain: String?, callback: ((Boolean) -> Unit)?) {
        if (chain?.startsWith(WC2Config.CONTRACT_EIP155_DOT) == true) {
            val chainId = chain.replace(WC2Config.CONTRACT_EIP155_DOT, "").toIntOrNull()
            if (chainId == null || chainId == WalletHelper.getCurChainId()) {
                callback?.invoke(true)
            } else {
                requestChangeChain(chainId){
                    if (it) {
                        callback?.invoke(true)
                    } else {
                        callback?.invoke(false)
                    }
                }
            }
        } else if(chain?.startsWith(WC2Config.CONTRACT_SOLANA_MAIN) == true){
            if (Config.SOLANA_MAIN_CHAIN_ID == WalletHelper.getCurChainId()) {
                callback?.invoke(true)
            } else {
                requestChangeChain(Config.SOLANA_MAIN_CHAIN_ID){
                    if(it){
                        callback?.invoke(true)
                    } else {
                        callback?.invoke(false)
                    }
                }
            }
        } else if(chain?.startsWith(WC2Config.CONTRACT_SOLANA) == true){
            if (Config.SOLANA_TEST_CHAIN_ID== WalletHelper.getCurChainId()) {
                callback?.invoke(true)
            } else {
                requestChangeChain(Config.SOLANA_TEST_CHAIN_ID){
                    if(it){
                        callback?.invoke(true)
                    } else {
                        callback?.invoke(false)
                    }
                }
            }
        } else {
            callback?.invoke(true)
        }
    }

    override fun getCapabilities(id: Long, address: String, chainId:String) {
        LogUtils.i("address:"+address)
        WC2Helper.approveSession(id, "{\n" +
                "  \"$chainId\": {\n" +
                "    \"supported\": true,\n" +
                "    \"methods\": [\"eth_sign\", \"eth_sendTransaction\"],\n" +
                "    \"events\": [\"chainChanged\", \"accountsChanged\"]\n" +
                "  }\n" +
                "}")
    }

    private fun requestChangeChain(chainId: Int, callback:((Boolean)->Unit)?=null) {
        CoroutineScope(Dispatchers.IO).launch {
            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId)
            if (network == null) {
                ToastUtils.showShort(Utils.getApp().getString(com.machinefi.walletconnect2.R.string.please_add_the_network_with_chain, chainId.toString()))
                return@launch
            }
            AddChainsUtil.showSwitchNetworkDialog(
                WC2Config.IOPAY_LOGO,
                WC2Config.IOPAY_URL,
                network,
                callback
            )
        }
    }

    private fun sendRawTransaction(requestId: Long,name: String, solanaTransactionPam: SolanaTransactionPam) {
        CoroutineScope(Dispatchers.IO).launch {
            val instructions = ArrayList<TransactionInstruction>()
            solanaTransactionPam._json?.instructions?.forEach {
                val keys = ArrayList<AccountMeta>()
                it.keys.forEach { metaAccount ->
                    keys.add(
                        AccountMeta(
                            PublicKey(metaAccount.pubkey),
                            metaAccount.isSigner,
                            metaAccount.isWritable
                        )
                    )
                }
                instructions.add(TransactionInstruction(PublicKey(it.programId), keys, it.data))
            }

            val timestamp = TimeUtils.getNowMills().toString()
            val res = SolanaWeb3.sendRawTransaction(
                timestamp,
                solanaTransactionPam.recentBlockhash?:"",
                instructions,
                solanaTransactionPam.transaction,
                name
            )
            if (res != null) {
                WC2Helper.approveSession(requestId, "{\"signature\": $res}")
            } else {
                WC2Helper.rejectSession(requestId)
            }
        }
    }

    fun isAccountSigner(index: Int,numRequiredSignatures:Int): Boolean {
        return index < numRequiredSignatures
    }

    fun isAccountWritable(index: Int,numRequiredSignatures:Int,numReadonlySignedAccounts:Int,count:Int,numReadonlyUnsignedAccounts:Int): Boolean {
        return index < numRequiredSignatures - numReadonlySignedAccounts ||
                (index >= numRequiredSignatures &&
                        index < count - numReadonlyUnsignedAccounts)
    }

}