package io.iotex.iopay.xapp.solana

import android.webkit.JavascriptInterface
import android.webkit.WebView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.funkatronics.encoders.Base58
import com.machinefi.walletconnect2.WC2Config
import com.machinefi.walletconnect2.bean.Instructions
import com.machinefi.walletconnect2.bean.SolanaTransactionPam
import com.solana.core.AccountMeta
import com.solana.core.HotAccount
import com.solana.core.PublicKey
import com.solana.core.Transaction
import com.solana.core.TransactionInstruction
import com.solana.core.versioned.Message
import com.solana.core.versioned.VersionedTransaction
import io.iotex.iopay.R
import io.iotex.iopay.dapp.dialog.DAppNewWalletTipsDialog
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.transaction.TransactionDialog
import io.iotex.iopay.transaction.bean.ExtraHead
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.transaction.connect.ConnectWalletDialog
import io.iotex.iopay.transaction.sign.SignMessageDialog
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.UrlUtils
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.util.extension.toHexByteArray
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.xapp.DappBridge
import io.iotex.iopay.xapp.trust.AddChainsUtil
import io.iotex.iopay.xapp.trust.sendSolanaCancel
import io.iotex.iopay.xapp.trust.sendSolanaError
import io.iotex.iopay.xapp.trust.sendSolanaSignAllTransaction
import io.iotex.iopay.xapp.trust.sendSolanaSignMessageResult
import io.iotex.iopay.xapp.trust.sendSolanaSignature
import io.iotex.iopay.xapp.trust.sendSolanaTransactionSignature
import io.iotex.iopay.xapp.trust.sendSolanaV0TransactionSignature
import io.iotex.iopay.xapp.trust.setSolanaAddress
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.bouncycastle.util.encoders.Base64
import org.json.JSONArray
import org.json.JSONObject
import java.math.BigInteger
import java.nio.charset.StandardCharsets

class SolanaDappBridge(
    private val webview: WebView,
    private val context: FragmentActivity,
    private val dappName: String,
    private val dappLogo: String
) : DappBridge(webview, context, dappName, dappLogo) {

    private var transactionDialog: TransactionDialog? = null
    private var signMessageDialog: SignMessageDialog? = null

    @JavascriptInterface
    override fun postMessage(json: String) {
        if(!WalletHelper.isSolanaNetwork()){
            requestChangeChain(Config.SOLANA_MAIN_CHAIN_ID){
               handPostMessage(json)
            }
        }
    }

    private fun handPostMessage(json: String) {
        "postMessage: $json".i()
        val obj = JSONObject(json)
        when (obj.getString("name")) {
            "requestAccounts" -> {
                requestAccount(obj)
            }

            "signMessage" -> {
                handleSignMessage(obj)
            }

            "signTransaction" -> {
                handleSignTransaction(obj)
            }

            "signAllTransactions" -> {
                handleSignAllTransactions(obj)
            }

            "signAndSendTransaction" -> {
                signAndSendTransaction(obj)
            }

            else -> {}
        }
    }

    private fun requestChangeChain(chainId: Int, callback:((Boolean)->Unit)?=null) {
        CoroutineScope(Dispatchers.IO).launch {
            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId)
            if (network == null) {
                ToastUtils.showShort(Utils.getApp().getString(com.machinefi.walletconnect2.R.string.please_add_the_network_with_chain, chainId.toString()))
                return@launch
            }
            AddChainsUtil.showSwitchNetworkDialog(
                WC2Config.IOPAY_LOGO,
                WC2Config.IOPAY_URL,
                network,
                callback
            )
        }
    }

    private fun requestAccount(jsonObj: JSONObject) {
        MainScope().launch {
            val id = jsonObj.getLong("id")
            val address = withContext(Dispatchers.IO) {
                WalletHelper.getCurWallet()?.solanaWallet?.publicKeyBase58
                    ?: WalletHelper.getCurWallet()?.address
            }
            if (address.isNullOrBlank()) {
                if (dAppNewWalletTipsDialog == null) {
                    dAppNewWalletTipsDialog = DAppNewWalletTipsDialog()
                }
                dAppNewWalletTipsDialog?.show(
                    context.supportFragmentManager,
                    System.currentTimeMillis().toString()
                )
                return@launch
            }

            val host = UrlUtils.getDomain(webview.url ?: "")
            val trust = withContext(Dispatchers.IO) {
                val trustDApp = AppDatabase.getInstance(context)
                    .trustDapp()
                    .queryTrustDApp(address, host)
                trustDApp != null
            }
            if (trust) {
                connectAddress = address
                webview.setSolanaAddress(id, address)
            } else {
                if (connectWalletDialog == null) {
                    val url = Config.HTTPS_SCHEME_HEAR + host
                    connectWalletDialog = ConnectWalletDialog(
                        dappLogo, url, address
                    ).apply {
                        onResult = {
                            connectWalletDialog = null
                            if (it) {
                                connectAddress = address
                                webview.setSolanaAddress(id, address)
                                saveTrustDApp(address, host)
                            } else {
                                webview.sendSolanaCancel(id)
                            }
                        }
                    }
                }
                if (connectWalletDialog?.isAdded == false) {
                    connectWalletDialog?.show(
                        context.supportFragmentManager,
                        System.currentTimeMillis().toString()
                    )
                    connectWalletDialog = null
                }
            }
        }
    }

    private fun signAndSendTransaction(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        val errorHandler = CoroutineExceptionHandler { _, e ->
            webview.sendSolanaError(id, "Failed to sign transaction")
        }
        MainScope().launch(errorHandler) {
            val obj = jsonObj.getJSONObject("object")
            val dataStr = obj.getString("data")
            var v0SolanaTransactionPam: SolanaTransactionPam? = null
            var transaction: Transaction? = null
            if (JSONObject(dataStr).has("instructions")) {
                transaction = resolveLegacyPayload(dataStr)
            } else {
                v0SolanaTransactionPam = resolveV0Payload(obj.toString())
            }
            val list = ArrayList<OptionEntry>()
            list.add(
                OptionEntry(
                    Utils.getApp().getString(R.string.method),
                    "Sign and Send Transaction"
                )
            )
            val extraHead = ExtraHead(dappLogo, UrlUtils.getDomain(webview.url ?: ""), false, placeholder = R.drawable.ic_dapp_placeholder)
            val extraMessage = dataStr.replace("\\\\", "")
            if (transactionDialog != null) return@launch
            val message = kotlin.runCatching {
                val data = Base64.decode(v0SolanaTransactionPam?.transaction ?: "")
                val versionedTransaction = VersionedTransaction.from(data)
                if (versionedTransaction.message.version == Message.MessageVersion.V0) {
                    Base64.toBase64String(versionedTransaction.message.serialize())
                } else {
                    ""
                }
            }.getOrNull() ?: ""
            transactionDialog =
                TransactionDialog("0x", BigInteger.ZERO, message, list, "", "", extraHead, extraMessage = extraMessage).apply {
                    onCancel = {
                        transactionDialog = null
                        webview.sendSolanaCancel(id)
                    }
                    onTransactionConfirm =
                        { _: Long, _: String, _, _ ->
                            transactionDialog?.dismiss()
                            transactionDialog = null
                            sendRawTransaction(id, v0SolanaTransactionPam, transaction)
                        }
                }
            (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
                transactionDialog?.show(
                    it.supportFragmentManager,
                    System.currentTimeMillis().toString()
                )
            }
        }
    }

    private fun resolveLegacyPayload(transactionStr: String): Transaction {
        val transactionObj = JSONObject(transactionStr)
        val feePayer = transactionObj.getString("feePayer")
        val recentBlockhash = transactionObj.getString("recentBlockhash")
        val instructionsStr = transactionObj.getString("instructions")
        val instructions = GsonUtils.fromJson<List<Instructions>>(
            instructionsStr,
            GsonUtils.getListType(Instructions::class.java)
        )
        val transactionInstructions = instructions?.map { instruction ->
            val keys = instruction.keys.map { key ->
                AccountMeta(PublicKey(key.pubkey), key.isSigner, key.isWritable)
            }
            TransactionInstruction(PublicKey(instruction.programId), keys, instruction.data)
        } ?: arrayListOf()
        val transaction = Transaction()
        transaction.add(*transactionInstructions.toTypedArray())
        transaction.setRecentBlockHash(recentBlockhash)
        transaction.feePayer = PublicKey(feePayer)
        return transaction
    }

    private fun resolveV0Payload(transactionStr: String): SolanaTransactionPam {
        val transactionObj = JSONObject(transactionStr)
        val raw = transactionObj.getString("raw")
        val v0Transaction = VersionedTransaction.from(Base64.decode(raw))
        return SolanaTransactionPam(null, raw, v0Transaction.message.recentBlockhash, null)
    }

    private fun sendRawTransaction(id: Long, v0SolanaTransactionPam: SolanaTransactionPam?, transaction: Transaction?) {
        CoroutineScope(Dispatchers.IO).launch {
            val timestamp = TimeUtils.getNowMills().toString()
            val signature = SolanaWeb3.sendRawTransaction(
                timestamp,
                v0SolanaTransactionPam?.recentBlockhash,
                ArrayList(transaction?.instructions ?: arrayListOf()),
                v0SolanaTransactionPam?.transaction,
                dappName,
                "Sign and Send Transaction"
            )
            if (signature != null) {
                webview.sendSolanaSignature(id, signature)
            } else {
                webview.sendSolanaError(id, "Failed to sign transaction")
            }
        }
    }

    private fun handleSignMessage(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        MainScope().launch {
            val obj = jsonObj.getJSONObject("object")
            val raw = obj.getString("data")
            signMessageDialog = SignMessageDialog(dappLogo, webview.originalUrl ?: "", null, raw)
                .apply {
                    onResult = { confirm ->
                        signMessageDialog = null
                        if (confirm) {
                            signMessage(id, raw)
                        } else {
                            webview.sendSolanaCancel(id)
                        }
                    }
                }
            (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
                signMessageDialog?.show(
                    it.supportFragmentManager,
                    System.currentTimeMillis().toString()
                )
            }
        }
    }

    private fun signMessage(id: Long, message: String) {
        val errorHandler = CoroutineExceptionHandler { _, e ->
            webview.sendSolanaError(id, "Failed to sign transaction")
        }
        CoroutineScope(Dispatchers.IO + errorHandler).launch {
            val signedMessage = SolanaWeb3.signMessage(message.toHexByteArray())
            val address = WalletHelper.getCurWallet()?.solanaWallet?.publicKeyBase58
                ?: WalletHelper.getCurWallet()?.address ?: ""
            webview.sendSolanaSignMessageResult(id, address, signedMessage.toHexByteArray())
        }
    }

    private fun handleSignTransaction(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        val errorHandler = CoroutineExceptionHandler { _, e ->
            webview.sendSolanaError(id, "Failed to sign transaction")
        }
        MainScope().launch(errorHandler) {
            val obj = jsonObj.getJSONObject("object")
            val dataStr = obj.getString("data")
            val message = dataStr.replace("\\", "")
            signMessageDialog = SignMessageDialog(dappLogo, UrlUtils.getDomain(webview.originalUrl ?: ""), null, message)
                .apply {
                    onResult = { confirm ->
                        signMessageDialog = null
                        if (confirm) {
                            signTransaction(id, obj.toString())
                        } else {
                            webview.sendSolanaCancel(id)
                        }
                    }
                }
            (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
                signMessageDialog?.show(
                    it.supportFragmentManager,
                    System.currentTimeMillis().toString()
                )
            }
        }
    }

    private fun signTransaction(id: Long, dataStr: String) {
        val errorHandler = CoroutineExceptionHandler { _, e ->
            webview.sendSolanaError(id, "Failed to sign transaction")
        }
        CoroutineScope(Dispatchers.IO + errorHandler).launch {
            val wallet = WalletHelper.getCurWallet()
            val privateKey = WalletHelper.getWalletPrivateKey(wallet)
            if (!WalletHelper.isSolanaPrivatakey(privateKey)) return@launch
            val account = HotAccount(Base58.decode(privateKey))
            val dataObj = JSONObject(dataStr)
            if (dataObj.has("raw")) {
                val v0Transaction = VersionedTransaction.from(Base64.decode(dataObj.getString("raw")))
                v0Transaction.sign(account)
                webview.sendSolanaV0TransactionSignature(id, v0Transaction.signatures)
            } else {
                val transaction = resolveLegacyPayload(dataStr)
                transaction.sign(account)
                transaction.signature?.let { signature ->
                    webview.sendSolanaTransactionSignature(id, Base58.encodeToString(signature))
                } ?: webview.sendSolanaError(id, "Failed to sign transaction")
            }
        }
    }

    private fun handleSignAllTransactions(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        val errorHandler = CoroutineExceptionHandler { _, e ->
            webview.sendSolanaError(id, "Failed to sign transaction")
        }
        MainScope().launch(errorHandler) {
            val dataStr = jsonObj.toString().replace("\\", "")
            signMessageDialog = SignMessageDialog(dappLogo, webview.originalUrl ?: "", null, dataStr)
                .apply {
                    onResult = { confirm ->
                        signMessageDialog = null
                        if (confirm) {
                            signAllTransactions(id, jsonObj.toString())
                        } else {
                            webview.sendSolanaCancel(id)
                        }
                    }
                }
            (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
                signMessageDialog?.show(
                    it.supportFragmentManager,
                    System.currentTimeMillis().toString()
                )
            }
        }
    }

    private fun signAllTransactions(id: Long, json: String) {
        val errorHandler = CoroutineExceptionHandler { _, e ->
            webview.sendSolanaError(id, "Failed to sign transaction")
        }
        CoroutineScope(Dispatchers.IO + errorHandler).launch {
            val wallet = WalletHelper.getCurWallet()
            val privateKey = WalletHelper.getWalletPrivateKey(wallet)
            if (!WalletHelper.isSolanaPrivatakey(privateKey)) return@launch
            val account = HotAccount(Base58.decode(privateKey))
            val payload = JSONObject(json).getJSONObject("object")
            val txObjArr = JSONArray(payload.getString("transactions"))
            val txList = mutableListOf<Transaction>()
            (0 until txObjArr.length()).map { index ->
                val txStr = txObjArr.getString(index)
                txList.add(resolveLegacyPayload(txStr))
            }
            val v0TxList = mutableListOf<VersionedTransaction>()
            val v0TxObjArr = JSONArray(payload.getString("versionedTransactions"))
            (0 until v0TxObjArr.length()).map { index ->
                val v0TxStr = v0TxObjArr.getString(index)
                val transactionPam = resolveV0Payload(v0TxStr)
                transactionPam.transaction?.let { raw ->
                    val v0Transaction = VersionedTransaction.from(Base64.decode(raw))
                    v0TxList.add(v0Transaction)
                }
            }
            txList.forEach { transaction ->
                transaction.sign(account)
            }
            v0TxList.forEach { transaction ->
                transaction.sign(account)
            }
            val txSignatures = txList.map { transaction ->
                transaction.signature?.let { signature ->
                    Base58.encodeToString(signature)
                } ?: ""
            }
            val v0TxSignatures = v0TxList.map { transaction ->
                transaction.signatures
            }
            webview.sendSolanaSignAllTransaction(id, txSignatures, v0TxSignatures)
        }
    }
}
