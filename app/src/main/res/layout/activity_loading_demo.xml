<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_background"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/dp_24">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_32"
        android:text="IoTeX Logo Loading Animation Demo"
        android:textColor="@color/color_title"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 基础版本动画 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:background="@drawable/shape_card_back_r18"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/dp_16">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_16"
            android:text="Basic Version"
            android:textColor="@color/color_title"
            android:textSize="16sp"
            android:textStyle="bold" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieBasic"
            android:layout_width="@dimen/dp_100"
            android:layout_height="@dimen/dp_100"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/lottie_logo_loading" />

        <Button
            android:id="@+id/btnShowBasic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/btn_shape_gradient_common"
            android:paddingHorizontal="@dimen/dp_24"
            android:paddingVertical="@dimen/dp_8"
            android:text="Show Dialog"
            android:textColor="@color/white"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 增强版本动画 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_card_back_r18"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/dp_16">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_16"
            android:text="Enhanced Version"
            android:textColor="@color/color_title"
            android:textSize="16sp"
            android:textStyle="bold" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieEnhanced"
            android:layout_width="@dimen/dp_100"
            android:layout_height="@dimen/dp_100"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/lottie_logo_loading_enhanced" />

        <Button
            android:id="@+id/btnShowEnhanced"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/btn_shape_gradient_common"
            android:paddingHorizontal="@dimen/dp_24"
            android:paddingVertical="@dimen/dp_8"
            android:text="Show Dialog"
            android:textColor="@color/white"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_24"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnHideLoading"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_weight="1"
            android:background="@drawable/stroke_617aff_r4"
            android:paddingVertical="@dimen/dp_8"
            android:text="Hide Loading"
            android:textColor="@color/color_617AFF"
            android:textSize="14sp" />

        <Button
            android:id="@+id/btnTestUtils"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_weight="1"
            android:background="@drawable/stroke_617aff_r4"
            android:paddingVertical="@dimen/dp_8"
            android:text="Test Utils"
            android:textColor="@color/color_617AFF"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout>
