<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/llToolBarLand"
                android:gravity="bottom"
                android:background="@color/theme_window_back"
                android:layout_width="wrap_content"
                android:layout_height="50dp">
                <ImageView
                    android:layout_marginBottom="7dp"
                    android:id="@+id/btnCloseLand"
                    android:padding="7dp"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/icon_close_grey"
                    android:layout_width="36dp"
                    android:layout_height="36dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <ImageView
                    android:id="@+id/ivBackLand"
                    android:alpha="0.2"
                    android:layout_marginBottom="@dimen/dp_13"
                    android:src="@drawable/icon_left_web"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <ImageView
                    android:id="@+id/ivForeLand"
                    android:alpha="0.2"
                    android:layout_marginBottom="@dimen/dp_13"
                    android:src="@drawable/icon_right_web"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <ImageView
                    android:id="@+id/ivLoad"
                    android:layout_marginBottom="@dimen/dp_13"
                    android:src="@drawable/icon_refresh"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <LinearLayout
                    android:layout_weight="1"
                    android:gravity="center"
                    android:paddingStart="13dp"
                    android:paddingEnd="13dp"
                    android:layout_marginBottom="@dimen/dp_7"
                    android:background="@drawable/shape_card_back_r18"
                    android:layout_width="230dp"
                    android:layout_height="36dp">
                    <ImageView
                        android:id="@+id/ivWebSafeLand"
                        android:src="@drawable/web_safe_https"
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"/>
                    <ImageView
                        android:layout_marginStart="5dp"
                        android:src="@drawable/icon_search_web"
                        android:layout_width="16dp"
                        android:layout_height="16dp"/>
                    <EditText
                        android:id="@+id/etUrlLand"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/color_title"
                        android:textSize="14sp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:imeOptions="actionDone|flagNoExtractUi"
                        android:inputType="text|textUri|textAutoComplete"
                        android:background="@color/transparent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                </LinearLayout>

                <View
                    android:layout_weight="2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <ImageView
                    android:layout_marginBottom="@dimen/dp_13"
                    android:id="@+id/ivLand"
                    android:src="@drawable/icon_land"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tvMultiLand"
                    android:background="@drawable/icon_page_web"
                    android:text="1"
                    android:textSize="12sp"
                    android:layout_marginBottom="@dimen/dp_7"
                    android:textColor="@color/gray_898ea2"
                    android:gravity="center"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="12dp"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <ImageView
                    android:layout_marginBottom="@dimen/dp_13"
                    android:id="@+id/btnOptsLand"
                    android:src="@drawable/icon_more_grey"
                    android:layout_marginEnd="19dp"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />
            </LinearLayout>

            <LinearLayout
                android:background="@color/theme_window_back"
                android:id="@+id/llToolBar"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:gravity="bottom"
                    android:id="@+id/llToolBarContent"
                    android:layout_width="match_parent"
                    android:layout_height="50dp">
                    <ImageView
                        android:layout_marginTop="7dp"
                        android:layout_marginBottom="7dp"
                        android:id="@+id/btnClose"
                        android:padding="7dp"
                        android:layout_marginStart="10dp"
                        android:src="@drawable/icon_close_grey"
                        android:layout_width="36dp"
                        android:layout_height="36dp" />

                    <LinearLayout
                        android:id="@+id/llTitle"
                        android:layout_marginTop="7dp"
                        android:layout_marginBottom="7dp"
                        android:layout_marginEnd="@dimen/dp_15"
                        android:layout_weight="1"
                        android:background="@drawable/shape_card_back_r18"
                        android:layout_width="0dp"
                        android:gravity="center"
                        android:layout_height="@dimen/dp_36">
                        <ImageView
                            android:id="@+id/ivWebSafe"
                            android:src="@drawable/web_safe_https"
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_24"/>
                        <TextView
                            android:layout_marginStart="4dp"
                            android:id="@+id/tvTitle"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:maxEms="13"
                            android:gravity="center"
                            android:textSize="17sp"
                            android:textColor="@color/color_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <FrameLayout
                android:orientation="vertical"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="0dp">

                <FrameLayout
                    android:id="@+id/flWebView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <LinearLayout
                    android:id="@+id/llCode"
                    android:visibility="gone"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <FrameLayout
                        android:id="@+id/flCode"
                        android:layout_weight="1"
                        android:layout_width="match_parent"
                        android:layout_height="0dp" />

                    <include
                        layout="@layout/barcode_field"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom" />

                </LinearLayout>

            </FrameLayout>

            <LinearLayout
                android:id="@+id/llBottom"
                android:paddingStart="30dp"
                android:paddingEnd="30dp"
                android:layout_gravity="bottom"
                android:background="@color/color_card_back"
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <ImageView
                    android:id="@+id/ivBack"
                    android:alpha="0.2"
                    android:layout_marginTop="@dimen/dp_13"
                    android:src="@drawable/icon_left_web"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <ImageView
                    android:id="@+id/ivFore"
                    android:alpha="0.2"
                    android:layout_marginTop="@dimen/dp_13"
                    android:src="@drawable/icon_right_web"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tvMulti"
                    android:background="@drawable/icon_page_web"
                    android:text="1"
                    android:layout_marginTop="@dimen/dp_13"
                    android:textSize="12sp"
                    android:textColor="@color/gray_898ea2"
                    android:gravity="center"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />


                <ImageView
                    android:layout_marginTop="@dimen/dp_13"
                    android:id="@+id/ivVertical"
                    android:src="@drawable/icon_vertical"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <ImageView
                    android:layout_marginTop="@dimen/dp_13"
                    android:id="@+id/btnOpts"
                    android:src="@drawable/icon_more_grey"
                    android:layout_marginEnd="@dimen/common_padding_small"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />
            </LinearLayout>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/flMulti"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

</layout>