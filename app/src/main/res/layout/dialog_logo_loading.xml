<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/progressBarLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_card_back_r18"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/dp_32">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieLoading"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_120"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/lottie_logo_loading" />

        <TextView
            android:id="@+id/tvLoadingText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/loading"
            android:textColor="@color/color_title"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

</RelativeLayout>
