<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:layout_marginTop="@dimen/dp_12"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatImageView
                    android:tint="@color/color_title_thr"
                    android:src="@drawable/icon_swap_wallet"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <com.robinhood.ticker.TickerView
                    android:id="@+id/tvFromBalance"
                    android:layout_weight="1"
                    android:layout_marginStart="@dimen/dp_3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="--"
                    android:textColor="@color/color_title"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tvMax"
                    android:text="MAX"
                    android:textSize="14sp"
                    android:textColor="@color/color_855eff"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>

            <FrameLayout
                android:layout_marginTop="@dimen/dp_12"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:paddingTop="@dimen/dp_24"
                        android:paddingBottom="@dimen/dp_24"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:background="@drawable/shape_card_back"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <LinearLayout
                                android:id="@+id/llFrom"
                                android:gravity="center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="@dimen/dp_30">

                                <LinearLayout
                                    android:id="@+id/llFromToken"
                                    android:gravity="center"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">
                                    <FrameLayout
                                        android:layout_marginEnd="@dimen/dp_5"
                                        android:layout_width="@dimen/dp_29"
                                        android:layout_height="wrap_content">
                                        <com.makeramen.roundedimageview.RoundedImageView
                                            app:riv_corner_radius="@dimen/dp_14"
                                            android:id="@+id/ivFrom"
                                            android:src="@drawable/icon_iotex_network_logo"
                                            android:layout_width="@dimen/dp_28"
                                            android:layout_height="@dimen/dp_28"/>
                                        <FrameLayout
                                            android:layout_gravity="bottom|end"
                                            android:padding="@dimen/dp_1"
                                            android:background="@drawable/shape_circle_chain_bg"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content">
                                            <com.makeramen.roundedimageview.RoundedImageView
                                                android:id="@+id/ivFromChain"
                                                app:riv_corner_radius="@dimen/dp_5"
                                                android:src="@drawable/icon_iotex_network_logo"
                                                android:layout_width="@dimen/dp_10"
                                                android:layout_height="@dimen/dp_10"/>
                                        </FrameLayout>
                                    </FrameLayout>
                                    <TextView
                                        android:id="@+id/tvFrom"
                                        android:text="@string/iotx_string"
                                        android:textSize="16sp"
                                        android:textColor="@color/color_title"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"/>
                                </LinearLayout>
                                <TextView
                                    android:id="@+id/tvSelectFrom"
                                    android:text="@string/select_token"
                                    android:textSize="16sp"
                                    android:visibility="gone"
                                    android:textColor="@color/color_title"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                                <androidx.appcompat.widget.AppCompatImageView
                                    android:tint="@color/color_title"
                                    android:layout_marginStart="@dimen/dp_9"
                                    android:src="@drawable/icon_swap_token_arrow"
                                    android:layout_width="@dimen/dp_9"
                                    android:layout_height="@dimen/dp_7"/>
                            </LinearLayout>
                        </LinearLayout>
                        <LinearLayout
                            android:gravity="end"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <LinearLayout
                                android:id="@+id/llFromEt"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content">

                                <EditText
                                    android:id="@+id/etFrom"
                                    android:layout_height="wrap_content"
                                    android:layout_width="match_parent"
                                    android:minWidth="@dimen/dp_100"
                                    android:gravity="end"
                                    android:maxLines="1"
                                    android:inputType="numberDecimal"
                                    android:hint="@string/enter_a_amount"
                                    android:includeFontPadding="false"
                                    android:background="@color/transparent"
                                    android:textColorHint="@color/color_hint"
                                    android:textSize="20sp"
                                    android:textColor="@color/color_title" />

                                <io.iotex.iopay.widget.DINTextView
                                    android:id="@+id/tvFromValue"
                                    android:gravity="end"
                                    android:text="~$ --"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/color_title_thr"
                                    android:textSize="14sp" />
                            </LinearLayout>
                            <include android:id="@+id/shimmerFrom"
                                android:visibility="gone"
                                android:layout_gravity="center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                layout="@layout/shimmer_swap_load"/>
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginTop="@dimen/dp_10"
                        android:background="@drawable/shape_card_back"
                        android:paddingTop="@dimen/dp_24"
                        android:paddingBottom="@dimen/dp_24"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <LinearLayout
                            android:id="@+id/llTo"
                            android:gravity="end"
                            android:orientation="vertical"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <LinearLayout
                                android:gravity="center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="@dimen/dp_30">

                                <LinearLayout
                                    android:id="@+id/llToToken"
                                    android:gravity="center"
                                    android:visibility="gone"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">
                                    <FrameLayout
                                        android:layout_marginEnd="@dimen/dp_5"
                                        android:layout_width="@dimen/dp_29"
                                        android:layout_height="wrap_content">
                                        <com.makeramen.roundedimageview.RoundedImageView
                                            android:id="@+id/ivTo"
                                            app:riv_corner_radius="@dimen/dp_14"
                                            android:src="@drawable/icon_iotex_network_logo"
                                            android:layout_width="@dimen/dp_28"
                                            android:layout_height="@dimen/dp_28"/>
                                        <FrameLayout
                                            android:layout_gravity="bottom|end"
                                            android:padding="@dimen/dp_1"
                                            android:background="@drawable/shape_circle_chain_bg"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content">
                                            <com.makeramen.roundedimageview.RoundedImageView
                                                android:id="@+id/ivToChain"
                                                app:riv_corner_radius="@dimen/dp_5"
                                                android:src="@drawable/icon_iotex_network_logo"
                                                android:layout_width="@dimen/dp_10"
                                                android:layout_height="@dimen/dp_10"/>
                                        </FrameLayout>
                                    </FrameLayout>
                                    <TextView
                                        android:id="@+id/tvTo"
                                        tools:text="@string/iotx_string"
                                        android:textSize="16sp"
                                        android:textColor="@color/color_title"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"/>
                                </LinearLayout>
                                <TextView
                                    android:id="@+id/tvSelectTo"
                                    android:text="@string/select_token"
                                    android:textSize="16sp"
                                    android:textColor="@color/color_title"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                                <androidx.appcompat.widget.AppCompatImageView
                                    android:tint="@color/color_title"
                                    android:layout_marginStart="@dimen/dp_9"
                                    android:src="@drawable/icon_swap_token_arrow"
                                    android:layout_width="@dimen/dp_9"
                                    android:layout_height="@dimen/dp_7"/>
                            </LinearLayout>

                        </LinearLayout>
                        <LinearLayout
                            android:gravity="end"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <LinearLayout
                                android:id="@+id/llToEt"
                                android:orientation="vertical"
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content">

                                <EditText
                                    android:id="@+id/etTo"
                                    android:gravity="end"
                                    android:layout_height="wrap_content"
                                    android:layout_width="match_parent"
                                    android:minWidth="@dimen/dp_100"
                                    android:maxLines="1"
                                    android:inputType="numberDecimal"
                                    android:hint="@string/enter_a_amount"
                                    android:includeFontPadding="false"
                                    android:background="@color/transparent"
                                    android:textColorHint="@color/color_hint"
                                    android:textSize="20sp"
                                    android:textColor="@color/color_title" />

                                <io.iotex.iopay.widget.DINTextView
                                    android:id="@+id/tvToValue"
                                    android:text="~$ --"
                                    android:gravity="end"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/color_title_thr"
                                    android:textSize="14sp" />
                            </LinearLayout>
                            <include android:id="@+id/shimmerTo"
                                android:visibility="gone"
                                android:layout_gravity="center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                layout="@layout/shimmer_swap_load"/>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/dp_12"
                    android:layout_marginBottom="@dimen/dp_12"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivSwitch"
                        android:elevation="@dimen/dp_5"
                        android:src="@drawable/icon_swap_switch"
                        android:layout_width="@dimen/dp_34"
                        android:layout_height="@dimen/dp_34"/>
                </LinearLayout>
            </FrameLayout>

            <LinearLayout
                android:id="@+id/llPriceWarning"
                android:background="@drawable/shape_swap_impact"
                android:padding="@dimen/dp_12"
                android:gravity="center"
                android:visibility="gone"
                android:layout_marginTop="@dimen/dp_30"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatImageView
                    android:tint="@color/color_ec7f11"
                    android:src="@drawable/ic_warning"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"/>
                <TextView
                    android:text="@string/price_impact_is_too_high"
                    android:textSize="@dimen/dp_10"
                    android:includeFontPadding="false"
                    android:layout_marginStart="@dimen/dp_3"
                    android:textColor="@color/color_ec7f11"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <TextView
                android:id="@+id/tvConfirm"
                android:layout_marginTop="@dimen/dp_16"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:alpha="0.5"
                android:enabled="false"
                android:gravity="center"
                android:text="@string/confirm"
                android:background="@drawable/btn_shape_gradient_common"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_44"/>

            <LinearLayout
                android:id="@+id/llSlip"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dp_24"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:layout_gravity="center"
                    android:gravity="center_vertical"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textColor="@color/color_title_sub"
                        android:textSize="12sp"
                        android:text="@string/slippage_tolerance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <ImageView
                        android:id="@+id/ivSlip"
                        android:layout_marginStart="@dimen/dp_6"
                        android:src="@drawable/icon_swap_quest"
                        android:layout_width="@dimen/dp_11"
                        android:layout_height="@dimen/dp_11"/>
                </LinearLayout>
                <TextView
                    android:id="@+id/tvSlip"
                    android:textColor="@color/color_title"
                    android:textSize="13sp"
                    android:text="0.5%"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <androidx.appcompat.widget.AppCompatImageView
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_swap_arrow"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/llRouter"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dp_12"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:layout_gravity="center"
                    android:gravity="center_vertical"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textColor="@color/color_swap_key"
                        android:textSize="12sp"
                        android:text="@string/mimo_smart_router"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <ImageView
                        android:id="@+id/ivRouter"
                        android:layout_marginStart="@dimen/dp_6"
                        android:src="@drawable/icon_swap_quest"
                        android:layout_width="@dimen/dp_11"
                        android:layout_height="@dimen/dp_11"/>
                </LinearLayout>
                <TextView
                    android:id="@+id/tvRouter"
                    android:textColor="@color/color_title"
                    android:textSize="13sp"
                    android:text="@string/best_trade"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <androidx.appcompat.widget.AppCompatImageView
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_swap_arrow"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/llResp"
                android:visibility="gone"
                tools:visibility="visible"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:gravity="center_vertical"
                    android:layout_marginTop="@dimen/dp_12"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textColor="@color/color_swap_key"
                        android:textSize="12sp"
                        android:text="@string/price"
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/tvPrice"
                        android:textColor="@color/color_title"
                        android:textSize="13sp"
                        tools:text="1 ETH ≈ 75,666.2095 IOTX"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:layout_marginTop="@dimen/dp_12"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/tvMinMaxKey"
                        android:textColor="@color/color_swap_key"
                        android:textSize="12sp"
                        android:text="@string/minimum_received"
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/tvMinMaxValue"
                        android:textColor="@color/color_title"
                        android:textSize="13sp"
                        tools:text="23.23 BNB"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <LinearLayout
                            android:gravity="center_vertical"
                            android:layout_marginTop="@dimen/dp_12"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:textColor="@color/color_swap_key"
                                android:textSize="12sp"
                                android:text="@string/price_impact"
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvPriceImpact"
                                android:textColor="@color/color_title"
                                android:textSize="13sp"
                                android:text="&lt;1%"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <LinearLayout
                            android:gravity="center_vertical"
                            android:layout_marginTop="@dimen/dp_12"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:textColor="@color/color_swap_key"
                                android:textSize="12sp"
                                android:text="@string/fee"
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvFee"
                                android:textColor="@color/color_title"
                                android:textSize="13sp"
                                android:text="0.003 IOTX"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <LinearLayout
                            android:gravity="center_vertical"
                            android:layout_marginTop="@dimen/dp_12"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:textColor="@color/color_swap_key"
                                android:textSize="12sp"
                                android:text="@string/route"
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"/>
                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerViewRouter"
                                tools:itemCount="1"
                                tools:listitem="@layout/item_token_router_grid"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/llCover"
                        android:gravity="center_horizontal"
                        android:background="@color/theme_window_back"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">
                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/ivShow"
                            android:tint="@color/color_title_sub"
                            android:layout_marginTop="@dimen/dp_20"
                            android:src="@drawable/icon_swap_down_content"
                            android:layout_width="@dimen/dp_20"
                            android:layout_height="@dimen/dp_20"/>
                    </LinearLayout>
                </FrameLayout>
            </LinearLayout>

        </LinearLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</layout>