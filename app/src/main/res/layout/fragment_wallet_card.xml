<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:id="@+id/llWalletTips"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:layout_marginBottom="@dimen/dp_12"
                android:background="@color/color_card_back"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_20"
                    android:src="@drawable/ic_warning" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_8"
                    android:text="@string/wallet_error_tips"
                    android:textColor="@color/colorWarning"
                    android:textSize="@dimen/font_size_common"
                    app:autoSizeTextType="uniform" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_8"
                    android:text="@string/learn_more"
                    android:textColor="@color/colorWarning"
                    android:textSize="@dimen/font_size_common"
                    app:autoSizeTextType="uniform" />
            </LinearLayout>
            <LinearLayout
                android:gravity="bottom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <com.robinhood.ticker.TickerView
                    android:id="@+id/tvWalletValue"
                    tools:text="1000"
                    app:textType="BOLD"
                    android:textSize="40sp"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:id="@+id/tvWalletHide"
                    android:text="******"
                    android:visibility="gone"
                    app:textType="BOLD"
                    android:textSize="40sp"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivBalancesEye"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_margin="@dimen/dp_2"
                    android:src="@drawable/bg_eye"
                    android:backgroundTint="@color/color_title" />
            </LinearLayout>

            <LinearLayout
                android:gravity="center"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/llCopy"
                    android:paddingStart="@dimen/dp_4"
                    android:paddingEnd="@dimen/dp_4"
                    android:background="@drawable/shape_33617aff_r11"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_22"
                    android:gravity="center">

                    <io.iotex.iopay.widget.DINTextView
                        android:id="@+id/tvAddress"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_marginEnd="@dimen/dp_4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="middle"
                        android:gravity="end"
                        android:singleLine="true"
                        android:textColor="@color/color_617AFF"
                        android:textSize="14sp"
                        tools:text="io1c52d...t56r23" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"
                        android:layout_marginEnd="@dimen/dp_4"
                        android:tint="@color/color_617AFF"
                        android:src="@drawable/icon_home_copy" />
                </LinearLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivWalletAlert"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"
                    android:visibility="gone"
                    android:layout_marginStart="@dimen/dp_5"
                    android:src="@drawable/ic_warning"
                    android:tint="@color/error_red"
                    tools:visibility="visible"/>
                <ImageView
                    android:layout_marginStart="@dimen/dp_5"
                    android:id="@+id/ivBitcoinAddressChange"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"
                    android:padding="@dimen/dp_5"
                    android:layout_marginEnd="@dimen/dp_6"
                    android:visibility="gone"
                    android:src="@drawable/icon_bitcoin_change_address"
                    tools:visibility="visible"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginBottom="@dimen/dp_10"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/llSend"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp_3"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp_11"
                    android:paddingBottom="@dimen/dp_11">

                    <FrameLayout
                        android:background="@drawable/shape_circle_home_card_back"
                        android:layout_width="@dimen/dp_42"
                        android:layout_height="@dimen/dp_42">
                        <ImageView
                            android:layout_width="@dimen/dp_18"
                            android:layout_height="@dimen/dp_18"
                            android:layout_gravity="center"
                            android:src="@drawable/icon_card_send" />
                    </FrameLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_4"
                        android:text="@string/transfer_send"
                        android:textColor="@color/color_title"
                        android:textSize="@dimen/font_size_small" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/llReceive"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp_11"
                    android:paddingBottom="@dimen/dp_11">

                    <FrameLayout
                        android:background="@drawable/shape_circle_home_card_back"
                        android:layout_width="@dimen/dp_42"
                        android:layout_height="@dimen/dp_42">
                        <ImageView
                            android:layout_width="@dimen/dp_18"
                            android:layout_height="@dimen/dp_18"
                            android:layout_gravity="center"
                            android:src="@drawable/icon_card_receive" />
                    </FrameLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_4"
                        android:text="@string/receive"
                        android:textColor="@color/color_title"
                        android:textSize="@dimen/font_size_small" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llBuy"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp_11"
                    android:paddingBottom="@dimen/dp_11">

                    <FrameLayout
                        android:background="@drawable/shape_circle_home_card_back"
                        android:layout_width="@dimen/dp_42"
                        android:layout_height="@dimen/dp_42">
                        <ImageView
                            android:layout_width="@dimen/dp_18"
                            android:layout_height="@dimen/dp_17"
                            android:layout_gravity="center"
                            android:src="@drawable/icon_card_buy" />
                    </FrameLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_4"
                        android:text="@string/buy"
                        android:textColor="@color/color_title"
                        android:textSize="@dimen/font_size_small" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llEarn"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp_11"
                    android:paddingBottom="@dimen/dp_11">

                    <FrameLayout
                        android:background="@drawable/shape_circle_home_card_back"
                        android:layout_width="@dimen/dp_42"
                        android:layout_height="@dimen/dp_42">

                        <ImageView
                            android:layout_width="@dimen/dp_17"
                            android:layout_height="@dimen/dp_15"
                            android:layout_gravity="center"
                            android:src="@drawable/icon_card_earn" />

                    </FrameLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_4"
                        android:text="@string/earn"
                        android:textColor="@color/color_title"
                        android:textSize="@dimen/font_size_small" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llActivity"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp_3"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp_11"
                    android:paddingBottom="@dimen/dp_11">

                    <FrameLayout
                        android:background="@drawable/shape_circle_home_card_back"
                        android:layout_width="@dimen/dp_42"
                        android:layout_height="@dimen/dp_42">
                        <ImageView
                            android:layout_width="@dimen/dp_22"
                            android:layout_height="@dimen/dp_22"
                            android:layout_gravity="center"
                            android:src="@drawable/icon_card_activity" />
                    </FrameLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_4"
                        android:text="@string/activities"
                        android:textColor="@color/color_title"
                        android:textSize="@dimen/font_size_small" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</layout>