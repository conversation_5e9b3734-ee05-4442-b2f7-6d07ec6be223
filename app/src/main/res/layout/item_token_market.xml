<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_marginTop="@dimen/dp_12"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/mTvLabel"
            android:layout_centerVertical="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_title_sub"
            android:textSize="12sp"
            tools:text="@string/amount" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/mTvValue"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/color_title"
            android:textSize="12sp"
            tools:text="1.0" />
    </RelativeLayout>
</layout>