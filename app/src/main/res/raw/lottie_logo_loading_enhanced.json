{"v": "5.7.0", "fr": 30, "ip": 0, "op": 120, "w": 200, "h": 200, "nm": "IoTeX Enhanced Logo Loading", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Outer Ring", "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 120, "s": [-360]}]}, "p": {"a": 0, "k": [100, 100, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [160, 160]}, "p": {"a": 0, "k": [0, 0]}, "nm": "Ellipse Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0.381, 0.478, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "lc": 1, "lj": 1, "ml": 4, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 20}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 10}}], "bm": 0, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}], "nm": "Ellipse 1", "bm": 0}], "ip": 0, "op": 120, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Main Circle", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [100, 100, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.42], "y": [1]}, "o": {"x": [0.58], "y": [0]}, "t": 0, "s": [85]}, {"i": {"x": [0.42], "y": [1]}, "o": {"x": [0.58], "y": [0]}, "t": 30, "s": [100]}, {"i": {"x": [0.42], "y": [1]}, "o": {"x": [0.58], "y": [0]}, "t": 60, "s": [85]}, {"i": {"x": [0.42], "y": [1]}, "o": {"x": [0.58], "y": [0]}, "t": 90, "s": [100]}, {"t": 120, "s": [85]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [120, 120]}, "p": {"a": 0, "k": [0, 0]}, "nm": "Ellipse Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0.341, 0.341, 0.341, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1"}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [0, 0.267, 1, 0.698, 0.5, 0.381, 0.478, 1, 1, 0.522, 0.369, 1]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, 0.522, 0.369, 1, 0.5, 0.267, 1, 0.698, 1, 0.381, 0.478, 1]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [0, 0.267, 1, 0.698, 0.5, 0.381, 0.478, 1, 1, 0.522, 0.369, 1]}, {"t": 120, "s": [0, 0.522, 0.369, 1, 0.5, 0.267, 1, 0.698, 1, 0.381, 0.478, 1]}]}}, "s": {"a": 0, "k": [-60, -60]}, "e": {"a": 0, "k": [60, 60]}, "t": 1, "nm": "Gradient Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 120, "s": [180]}]}, "o": {"a": 0, "k": 100}}], "nm": "Ellipse 1", "bm": 0}], "ip": 0, "op": 120, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Center Dot", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [50]}, {"t": 120, "s": [100]}]}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [100, 100, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [120]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [120]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [120]}, {"t": 120, "s": [100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [24, 24]}, "p": {"a": 0, "k": [0, 0]}, "nm": "Ellipse Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.204, 0.204, 0.224, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}], "nm": "Ellipse 1", "bm": 0}], "ip": 0, "op": 120, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Orbiting Dot 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 120, "s": [360]}]}, "p": {"a": 0, "k": [100, 100, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [14, 14]}, "p": {"a": 0, "k": [35, -25]}, "nm": "Ellipse Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1"}, {"ty": "st", "c": {"a": 0, "k": [0.341, 0.341, 0.341, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}], "nm": "Ellipse 1", "bm": 0}], "ip": 0, "op": 120, "st": 0, "bm": 0}], "markers": []}