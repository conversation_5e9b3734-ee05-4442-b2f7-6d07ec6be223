<?xml version="1.0" encoding="UTF-8"?>
<resources>
  <string name="app_name">ioPay</string>
  <string name="continue_text">Continue</string>
  <string name="yes">Yes</string>
  <string name="no">No</string>
  <string name="cancel">Cancel</string>
  <string name="close">Close</string>
  <string name="no_cancel">No,cancel</string>
  <string name="yes_transfer">Yes,make transaction</string>
  <string name="ok">OK</string>
  <string name="export">Export</string>
  <string name="delete">Delete</string>
  <string name="private_key">Private Key</string>
  <string name="address">Address</string>
  <string name="key_store">Keystore</string>
  <string name="setting">Settings</string>
  <string name="help">Help</string>
  <string name="menu_switch">Switch</string>
  <string name="copy_success">Copied!</string>
  <string name="current_balance">Current balance： </string>
  <string name="balance">balance:</string>
  <string name="iotx">%1$s IOTX</string>
  <string name="xrc20_value">≈ $ %1$s </string>
  <string name="iotx_string">IOTX</string>
  <string name="iotex_string">IoTeX</string>
  <string name="native_string">Native</string>
  <string name="web3_string">Web3</string>
  <string name="eth_string">ETH</string>
  <string name="amount">Amount</string>
  <string name="choose_coin">Choose coin</string>
  <string name="choose_token">Choose Token</string>
  <string name="offer_amount">Amount:</string>
  <string name="time">Time</string>
  <string name="account">Account</string>
  <string name="from">From</string>
  <string name="to">To</string>
  <string name="gas_fee">Gas Fee (IOTX)</string>
  <string name="transfer">Transfer</string>
  <string name="transaction">Transaction</string>
  <string name="hash">Hash</string>
  <string name="nonce">Nonce</string>
  <string name="gas_fee_only">Gas Fee</string>
  <string name="max_fee_only">Max Fee</string>
  <string name="gas_fee_value">%1$s IOTX</string>
  <string name="network_switch">Network Switch - %1$s</string>
  <string name="network">Network</string>
  <string name="network_desc">Add and edit networks</string>
  <string name="community">Community</string>
  <string name="change_wallet">Manage Wallet</string>
  <string name="change_wallet_desc">Manage wallet easily</string>
  <string name="clear_auth_cache">Manage DApp Auth</string>
  <string name="clear_all">Clear All</string>
  <string name="all">All</string>
  <string name="dapps">DApps</string>
  <string name="cleared_successfully">Cleared Successfully</string>
  <string name="change_pin">Change PIN</string>
  <string name="address_book">Address Book</string>
  <string name="address_book_desc">Convenient to record address</string>
  <string name="web3Address">Web3Address</string>
  <string name="receive">Receive</string>
  <string name="send_to">Send To</string>
  <string name="receipt">Receipt</string>
  <string name="status">Status</string>
  <string name="action_fee">Action Fee</string>
  <string name="success">Success</string>
  <string name="failed">Failed</string>
  <string name="waiting">Waiting</string>
  <string name="menu_wallet">Wallet</string>
  <string name="menu_actions">Actions</string>
  <string name="menu_news">News</string>
  <string name="menu_setting">Settings</string>
  <string name="menu_discover">Discover</string>
  <string name="quick_wallet_switch">Quick Wallet Switch</string>
  <string name="to_contract">To Contract</string>
  <string name="bucketid">BucketId</string>
  <string name="receipt_address">Receipt Address</string>
  <string name="contract_is">Contract: %1$s</string>
  <string name="version">Version</string>
  <string name="scan_qr_code">Scan QR Code</string>
  <string name="edit_gas">Edit Max Fee</string>
  <string name="total">Total</string>
  <string name="switch_network">Switch Network</string>
  <string name="explorer">Explorer</string>
  <string name="loading">Loading...</string>
  <string name="token_lower_case">Token</string>
  <string name="tokens">Tokens</string>
  <string name="qev">Qev</string>
  <string name="gwei">GWEI</string>
  <string name="activities">Activities</string>
  <string name="create_wallet_button">Create Wallet</string>
  <string name="edit_wallet_button">Edit Wallet</string>
  <string name="import_wallet_button">Import Wallet</string>
  <string name="wallet_address">Wallet Address:</string>
  <string name="wallet_detail_title">Wallet</string>
  <string name="wallet_list">Wallets</string>
  <string name="xrc20_tokens">XRC20 Tokens</string>
  <string name="xrc20_tokens_desc">View your %s tokens</string>
  <string name="nft_tokens">Non-Fungible Tokens (NFTs)</string>
  <string name="history">History</string>
  <string name="transfer_iotx">Transfer IOTX</string>
  <string name="transfer_send_iotx">Send IOTX</string>
  <string name="transfer_receive_iotx">Receive IOTX</string>
  <string name="claim_vita">Claim VITA</string>
  <string name="claim_vita_content">You are going to claim VITA via smart contract. You may only claim once per cycle - any other requests will fail.</string>
  <string name="claim_vita_success">Your claim action has been sent successfully.</string>
  <string name="claim_vita_error">Your claim action was not sent successfully. Please try again later.</string>
  <string name="refresh_action">Please wait.</string>
  <string name="activity_wallet_name">Wallets</string>
  <string name="activity_create_wallet_name">Create Wallet</string>
  <string name="activity_create_wallet_title">Create a Wallet</string>
  <string name="wallet_create_toolbar_title">Create Wallet</string>
  <string name="wallet_create_form_thumbnail">Pick a thumbnail for your Wallet</string>
  <string name="wallet_create_form_name">Pick a name for your new Wallet</string>
  <string name="wallet_import_toolbar_title">Import Wallet</string>
  <string name="import_scan_qr">Scan QR code</string>
  <string name="keystore_recognized">Keystore Recognized</string>
  <string name="input_private_key">Please input your private key here.</string>
  <string name="input_address">Please input your wallet address here.</string>
  <string name="input_wallet_name">Wallet Name :</string>
  <string name="activity_user_agreement_toolbar">Welcome to ioPay</string>
  <string name="user_agreement_title">Terms of Use</string>
  <string name="user_agreement_content"><![CDATA[
		IoTeX Foundation Ltd. (together “IoTeX,” “we,” “our,” “us”) provides services through our website (iotex.io) as well as related applications and products (together our “Services,” “Sites,” “Products,” and “Wallets”). By accessing and using our Services, you agree to the following Terms of Use (collectively, these “Terms”). If you do not agree to all of these Terms, please do not use our Services. Your access and use of IoTeX Services constitutes your acceptance of and agreement to abide by each of the terms set forth in our Terms of Use, including our Privacy Policy, which is hereby incorporated in our Terms by reference. If you are using our Services on behalf of your organization, that organization accepts these terms. By creating a Wallet, downloading or running our Web, Mobile, and Desktop Applications, or visiting our website, you are agreeing to our Terms. You can only use our Services if permitted under the laws of your jurisdiction. Please make sure that these Terms are in compliance with all laws, rules, and regulations that apply to you.

		By accessing IoTeX Services, you agree to use them for their stated purpose. Any unlawful, discriminatory, harassing, defamatory, and harmful use of our Services or violation of applicable laws and legal rights of IoTeX or other users through our Services is not permitted. IoTeX will not be liable for any loss or damage arising from your failure to comply with our full Terms of Use. When you create a Wallet, you are strongly advised to take precautions in order to avoid loss of access to and/or control over your Wallet. Suggested measures include, but are not limited to creating a strong password that you do not use for any other website or online service; (b) using the backup functionality provided by the Wallet or safeguard your private key and recovery(backup) phrase on an external hard drive; © maintaining the security of your Wallet by protecting the private key and recovery(backup) phrase associated with your Wallet; and (d) promptly notifying us if you suspect any security breaches related to your Wallet.

		These Terms may be modified, changed, or supplemented from time to time. We suggest you visit this page regularly to keep up to date with any changes. Your continued use of IoTeX Services will confirm your acceptance of these Terms as modified, changed, or supplemented by us.

		Privacy Policy
		Your privacy and data security are extremely important to us. IoTeX and its affiliates are committed to the responsible management, protection, and use of your personal data. When you use our Products, we may collect personal data or other information from or about you. In general, we use this information to provide our Services and to enhance the quality of our Products.

		IoTeX does not automatically collect any personally identifiable information. Your contact details, such as email address and social media handle, may be requested when you communicate with us, report a bug, or troubleshoot any related issues to IoTeX Wallets. We will not use your Personal Information except to deliver you our Services, comply with the law, make IoTeX Services better, and protect our rights.

		Please review our full Terms of Use & Privacy Policy (https://iotex.io/policy). If you have any questions or concerns, please reach out to <NAME_EMAIL>.

	]]></string>
  <string name="create_password_notice_title">NOTICE</string>
  <string name="setting_passwords_note">Enter a unique password for %1$s.</string>
  <string name="wallet_password_title">Password</string>
  <string name="wallet_password_skip">Skip</string>
  <string name="back_to_simple">Switch to Basic PIN</string>
  <string name="success_save_setting">We have saved your wallet settings.</string>
  <string name="wallet_new_button">New Wallet</string>
  <string name="create_password_notice_content">The password you enter on the next page will only be used to unlock the HD Wallet on this device. You will not be able to restore it if lost. Be sure to record it in a safe place!
	</string>
  <string name="create_password_notice_confirm">You have successfully created a Wallet. Please record your private key and keep it in a safe place. Do not screenshot or share with others!
	</string>
  <string name="activity_create_password_name">Create Password</string>
  <string name="setup_password_title">ENTER YOUR PASSWORD</string>
  <string name="confirm_password_title">CONFIRM YOUR PASSWORD</string>
  <string name="password_touch_ID_title">Touch ID</string>
  <string name="password_touch_ID_description">Use Touch ID to unlock?</string>
  <string name="key_store_name">ioPay Keystore</string>
  <string name="login_failed">Login failed !</string>
  <string name="fingerprint_login_failed">Fingerprint login failed !</string>
  <string name="error_get_pass_info">Can not get password info</string>
  <string name="fingerprint_or_pass_unlock">Unlock with your password or fingerprint</string>
  <string name="password_create">Create Your Passcode</string>
  <string name="password_create_reset">Enter Your New PIN</string>
  <string name="advance_password">Or Try Advanced PIN?</string>
  <string name="input_password_private_key">Please confirm your password:</string>
  <string name="view_private_key">View Private Key</string>
  <string name="prompt_point_at_a_barcode">Point your camera at a QR code</string>
  <string name="cd_close_button">Back</string>
  <string name="cd_flash_button">Toggle camera flash</string>
  <string name="select_address">Select wallet address</string>
  <string name="key_invalid">Invalid Key !</string>
  <string name="wallet_invalid">Wallet Id invalid</string>
  <string name="sending">Sending</string>
  <string name="wallet_list_thumbnail">Wallet Thumbnails</string>
  <string name="wallet_header_img">Wallet Header Image</string>
  <string name="icon_key_store">Keystore</string>
  <string name="edit_wallet">Wallet Settings</string>
  <string name="edit_wallet_name">Rename your wallet</string>
  <string name="switch_net_title">Switch Network</string>
  <string name="new_net_title">New Network</string>
  <string name="main">Main</string>
  <string name="test">Test</string>
  <string name="main_network">Main Network</string>
  <string name="test_network">Test Network</string>
  <string name="delete_wallet_confirm">You are going to delete %s. Are you sure you want to do this?</string>
  <string name="clear_trust_confirm">You are going to clear the authentication cache in DApps. Are you sure you want to do this?</string>
  <string name="open_notification_confirm">App does not open notifications, go to open notifications</string>
  <string name="validate_not_null">Field can\'t be empty</string>
  <string name="validate_wallet_name_length">Wallet name too long</string>
  <string name="validate_private_key">Please input your private key!</string>
  <string name="please_input_address">Please input your address!</string>
  <string name="validate_alias_exist">This alias is occupied!</string>
  <string name="validate_address_exist">This wallet address already exists. Please try another one!</string>
  <string name="erc20_balance">%1$s %2$s</string>
  <string name="gas_fee_symbol">Gas Fee (%1$s)</string>
  <string name="amount_not_empty">Non-zero amount is required</string>
  <string name="stake_amount_must">Stake amount must greater than 100</string>
  <string name="gas_limit">Gas Limit</string>
  <string name="must_not_empty">This field is required.</string>
  <string name="token_transferred">Tokens Transferred</string>
  <string name="update_now">Update now</string>
  <string name="update_available">Update available</string>
  <string name="view_wallet">View %1$s</string>
  <string name="auto_wallet_name">Wallet %1$s</string>
  <string name="could_not_load_certificate">Could not load certificate!</string>
  <string name="no_network">No network available.</string>
  <string name="network_error">network error.</string>
  <string name="pref_previously_started">Previously Launched</string>
  <string name="detail">Detail</string>
  <string name="url_invalid">Url invalid!</string>
  <string name="claim">Claim</string>
  <string name="claim_vita_item">Claim VITA</string>
  <string name="claim_as">Claim As</string>
  <string name="fetching">Fetching</string>
  <string name="wallet_send">SEND</string>
  <string name="wallet_receive">RECEIVE</string>
  <string name="wallet_deposit">DEPOSIT</string>
  <string name="wallet_stake">STAKE</string>
  <string name="no_history">No Data.</string>
  <string name="transfer_xrc20">Transfer XRC20</string>
  <string name="transfer_send_xrc20">Send XRC20</string>
  <string name="transfer_receive_xrc20">Receive XRC20</string>
  <string name="transfer_send">Send</string>
  <string name="transfer_receive">Receive</string>
  <string name="transfer_amount_invalid">Balance is insufficient for the action!</string>
  <string name="amount_must_greater_zero">Amount must be greater than 0</string>
  <string name="no_tokens">No tokens added yet</string>
  <string name="no_transactions">You have no activities</string>
  <string name="bid">Bid</string>
  <string name="bid_vita">Bid VITA</string>
  <string name="discord">Discord</string>
  <string name="to_VITA_offer">Place bid for VITA</string>
  <string name="offer">Bid</string>
  <string name="you_send">You are about to send…</string>
  <string name="source_address">From Address</string>
  <string name="to_address">To Address</string>
  <string name="method">Method</string>
  <string name="message">Message</string>
  <string name="gas_price">Gas Price</string>
  <string name="high_gas_price_warning">*Gas fee is larger than usual, suggested gas price is 1, now the max gasfee maybe %s.</string>
  <string name="confirm_action">Are you sure you want to do this?</string>
  <string name="successful_broadcasting">Broadcast Successfully</string>
  <string name="successful_broadcasting_tip1">Your TX has been broadcast to the network. This does not mean it has been mined on the blockchain. It usually take a few seconds to confirm.</string>
  <string name="successful_broadcasting_tip2">You can check the status right now by clicking the following TX hash or save your TX hash and check the status later.</string>
  <string name="your_hash_value">Your TX hash:</string>
  <string name="smart_contract">Smart Contract</string>
  <string name="contract_address">Contract</string>
  <string name="please_enter">Please enter</string>
  <string name="please_select">Please select</string>
  <string name="enter_correct_number">Please enter the correct number</string>
  <string name="can_not_exceed_your_balance">Can\'t exceed your balance</string>
  <string name="owner_address">Owner: %s</string>
  <string name="owner">Owner</string>
  <string name="owner_eth">Owner (Eth)</string>
  <string name="switched_net">Switched to %s</string>
  <string name="twitter">Twitter</string>
  <string name="telegram_group">Telegram Group</string>
  <string name="facebook">FaceBook</string>
  <string name="reddit">Reddit</string>
  <string name="youtube">Youtube</string>
  <string name="forum">Forum</string>
  <string name="medium">Medium</string>
  <string name="date">Date</string>
  <string name="switch_language_title">Switch Language</string>
  <string name="general">General</string>
  <string name="language">Language</string>
  <string name="language_desc">Translate the application to a different supported language.</string>
  <string name="general_desc_gp">Language, Notification, Web3 address</string>
  <string name="general_desc">Language, Web3 address</string>
  <string name="address_invalid">Address is invalid!</string>
  <string name="need_permission">You need to allow access permissions</string>
  <string name="found_address">Address</string>
  <string name="xapps_title">Apps</string>
  <string name="news">News</string>
  <string name="action_error">Oops!%s</string>
  <string name="import_by">By</string>
  <string name="password">Password</string>
  <string name="input_keystore_json">Paste your private key Keystore</string>
  <string name="validate_keystore_json">Please input your keystore!</string>
  <string name="keystore_json_invalid">Keystore is invalid!</string>
  <string name="validate_password">Password is required</string>
  <string name="or">Or</string>
  <string name="type_password">Type your password</string>
  <string name="exit_app_tip">Press once more to exit the application</string>
  <string name="webview_title_connect">Connect with ioPay?</string>
  <string name="timeout">Timeout</string>
  <string name="keystore_not_init">The application keystore has not been initialized</string>
  <string name="Search">Enter</string>
  <string name="Search_dapp_hint">Use the link to go directly to DApp</string>
  <string name="plus_vita"> %s VITA</string>
  <string name="notification_icon" translatable="false">notification icon</string>
  <string name="evm_transactions">Contract</string>
  <string name="xrc20_transactions">XRC20</string>
  <string name="stake_transactions">Stake</string>
  <string name="wallet_register_switch">Receive Notification</string>
  <string name="wallet_register_switch_desc">Keep track of your transactions by enabling push notifications.</string>
  <string name="eth_address_switch">Show Web3 address</string>
  <string name="web3_address_switch">Show Web3 address</string>
  <string name="eth_address_switch_desc">Use an Ethereum wallet address in the application.</string>
  <string name="notification">Notification</string>
  <string name="iopay_channel_name" translatable="false">iopay-push-channel</string>
  <string name="channel_description" translatable="false">channel for push service from web ioPay home</string>
  <string name="receive_token">Your balance received %1$s %2$s</string>
  <string name="claim_receive_token">Your balance received %1$s %2$s</string>
  <string name="got_message">new message from ioPay</string>
  <string name="unreach_host">Could not reach to Push Service host</string>
  <string name="invalid_action">Invalid Action</string>
  <string name="invalid_receiver">Invalid Receiver</string>
  <string name="invalid_amount_number">Amount must be numeric</string>
  <string name="unsupport_type">Unsupported Type! Uppercase may effect</string>
  <string name="payment">Payment</string>
  <string name="wallet_connect">Wallet Connect</string>
  <string name="connect_wallet">Connect Wallet</string>
  <string name="content">content</string>
  <string name="developer_mode_tips">Show Developer Mode</string>
  <string name="developer_mode">Developer Mode</string>
  <string name="login_with_current_wallet">Login with current wallet?</string>
  <string name="app_login_title"> %s App would like to access your ioPay wallet information</string>
  <string name="ucam">Ucam</string>
  <string name="app_login_content">Please select an ioPay wallet address to log in:</string>
  <string name="switch_wallet">Switch Wallet</string>
  <string name="login_expire_time">Please try again for auth timeout</string>
  <string name="invalid_erc20_type">Invalid type</string>
  <string name="receipt_address_required">Receipt Address is required!</string>
  <string name="add_xrc20">Add</string>
  <string name="update_iopay">Update ioPay?</string>
  <string name="no_thank">No Thanks</string>
  <string name="update">Update</string>
  <string name="update_des">ioPay recommends that you update to the latest version.you should backup private key, You can keep using this app while downloading the update.</string>
  <string name="g_play">Google Play</string>
  <string name="device_must_api_level">Sorry! ioPay is required to device Android 6.0 (API level 23) or higher</string>
  <string name="error_get_account">Could not load the wallet account</string>
  <string name="you_doing">You are about to do...</string>
  <string name="stake_staking_buckets"> Staking Bucket Details</string>
  <string name="stake_vote_for">Vote For</string>
  <string name="stake_stake_duration">Stake Duration</string>
  <string name="auto_stake">Stake-Lock</string>
  <string name="stake_bucket_status">Bucket Status</string>
  <string name="bucket">Bucket</string>
  <string name="unstake">UnStake</string>
  <string name="stake_details">Stake Details</string>
  <string name="nft">x %1$s</string>
  <string name="nft_string">NFT</string>
  <string name="ucam_pioneer">Ucam Pioneer</string>
  <string name="unknown_action">unknown action. Please update the latest ioPay version.</string>
  <string name="NFT_title">NFT</string>
  <string name="nft_no">No.%1$s</string>
  <string name="apr_no">APR:%1$s</string>
  <string name="create_stake">Create Stake</string>
  <string name="btn_create_stake">New Stake</string>
  <string name="staked_amount">Staked Amount</string>
  <string name="total_staked_amount">Total Staked</string>
  <string name="pending_unstake_amount">Unstaking</string>
  <string name="ready_to_withdraw_amount">Ready to Withdraw</string>
  <string name="total_stakes_number">Total Votes</string>
  <string name="title_my_stakes">My Staking</string>
  <string name="my_stakes_list">My Staking Buckets</string>
  <string name="stake_actions">Edit</string>
  <string name="votes">%1$s Votes</string>
  <string name="buckets">%1$s Buckets</string>
  <string name="stake_unstake">Unstake</string>
  <string name="stake_withdraw">Withdraw Stake</string>
  <string name="stake_restake">Restake</string>
  <string name="stake_add_deposit">Add Stake</string>
  <string name="stake_change_candidate">Change Delegate</string>
  <string name="stake_register_candidate">Delegate Register</string>
  <string name="stake_transfer">Reassign Bucket</string>
  <string name="stake_candidate_update">Delegate Update</string>
  <string name="to_string">To: %1$s</string>
  <string name="stake_delegate_name">Delegate Name</string>
  <string name="stake_amount_sub">You can only increase this once processed.</string>
  <string name="stake_current_balance">Your wallet balance: %1$s IOTX.</string>
  <string name="stake_duration">Stake Duration</string>
  <string name="stake_duration_days">days</string>
  <string name="stake_duration_sub">You can extend it at any time.</string>
  <string name="stake_auto_stake">Stake-Lock</string>
  <string name="stake_auto_stake_sub">Turn on Stake-Lock to lock your Stake Duration and retain your bonus votes indefinitely. </string>
  <string name="stake_power">Staking Power</string>
  <string name="stake_power_votes">%1$s Votes</string>
  <string name="stake_power_estimation">Staking Power Estimation</string>
  <string name="stake_power_estimation_sub">Total votes throughout your Stake Duration.</string>
  <string name="stake_votes">VOTES</string>
  <string name="stake_delegates_list">Delegates List</string>
  <string name="stake_duration_not_none">please input stake duration</string>
  <string name="stake_delegate_valiate">please input stake duration</string>
  <string name="stake_delegate">Delegate</string>
  <string name="stake_my_reward">My Reward</string>
  <string name="stake_my_stake">My Stake</string>
  <string name="stake_title">Stake</string>
  <string name="from_string">From %1$s</string>
  <string name="stake_status_ongoing">Ongoing</string>
  <string name="stake_status_unstaking">Unstaking</string>
  <string name="stake_status_withdrawble">Ready to Withdraw</string>
  <string name="stake_status_unstaking_prefix">Until %1$s</string>
  <string name="stake_status_withdrawable_prefix">At %1$s</string>
  <string name="stake_status_no_stake_starttime">No stakeStartTime</string>
  <string name="stake_status_no_stake_starttime_prefix">---</string>
  <string name="stake_duration_days_string">%1$s Days</string>
  <string name="not_applicable">Not applicable</string>
  <string name="anytime">Anytime</string>
  <string name="transfer_address">Transfer Bucket to Address: </string>
  <string name="transfer_stake_warning">Reassign Bucket transfers ownership of your bucket to another address. Make sure you have the private key to the selected address -- do NOT use an exchange address.</string>
  <string name="transfer_stake">Reassign Bucket (No. %s)</string>
  <string name="add_stake_title">Deposit to Stake (No. %d)</string>
  <string name="delegate_rank_string">Rank:# %s</string>
  <string name="withdraw_action_title">Withdraw Bucket (No. %s)</string>
  <string name="withdraw_action_confirm">Are you sure to withdraw your token?</string>
  <string name="button_select">Select</string>
  <string name="after_string">After %s</string>
  <string name="add_stake_desc">The newly added tokens will align with the current bucket settings including Delegate Name, the remaining stake/vote duration, Stake-Lock selection, etc.</string>
  <string name="total_amount">Total Amount</string>
  <string name="stake_auto_stake_only">AutoStake Only</string>
  <string name="stake_bucket_index">Bucket Index</string>
  <string name="unstake_action_title">Unstake Bucket (No. %s)</string>
  <string name="unstake_action_confirm">Are you sure you want to unstake? It will take three days before you can withdraw your tokens.</string>
  <string name="amount_must_greater_100">Stake amount must grater than 100</string>
  <string name="off">OFF</string>
  <string name="stake_duration_max">Duration should be less than 1051 days</string>
  <string name="stake_duration_restake_min">Duration should be at least %s days</string>
  <string name="no_bucket">No bucket!</string>
  <string name="bucket_index">Bucket Index</string>
  <string name="burndrop_eligible">Burn-Drop Eligible</string>
  <string name="burndrop_eligible_tips">Stake-Lock and duration ≥ 91 days to be eligible for Burn-Drop.</string>
  <string name="format_number">#,###</string>
  <string name="complete">complete</string>
  <string name="xrc20_token_address">XRC20 Token address</string>
  <string name="token_address">Token address</string>
  <string name="custom_tokens">Custom Tokens</string>
  <string name="not_exist">Not Exist</string>
  <string name="address_already_exists">Address already exists</string>
  <string name="spender">Spender</string>
  <string name="owner_address_string">Owner Address</string>
  <string name="xrc_send">Send</string>
  <string name="xrc_receive">Receive</string>
  <string name="xrc_more">More</string>
  <string name="xrc_swap">Swap</string>
  <string name="max">max</string>
  <string name="take_maximum">Take Maximum</string>
  <string name="saved_successfully">Saved successfully</string>
  <string name="saved_fail">Saved failed</string>
  <string name="action">Action</string>
  <string name="compound">Compound</string>
  <string name="compound_title">Compound Interest Setting</string>
  <string name="compound_tip">Please select a bucket to receive your compound interest:</string>
  <string name="compound_tip_content1">You may choose to automatically re-stake your staking rewards by defining a “compound interest bucket”. This bucket will receive all staking rewards （from Hermes Delegates only） to maximize compound interest for users.</string>
  <string name="compound_tip_content2">Note: your compound interest bucket must have Stake-Lock ON.</string>
  <string name="remove">Remove %s</string>
  <string name="index_no">No.%1$s</string>
  <string name="buckets_actions">Buckets</string>
  <string name="other_method">Execution</string>
  <string name="menu_transactions">Transactions</string>
  <string name="update_json_url">https://raw.githubusercontent.com/hunglmtb/AppUpdater/master/app/update-changelog.json</string>
  <string name="switched_main_net">Switched to mainnet</string>
  <string name="switched_test_net">Switched to testnet</string>
  <string name="new_rpc_network">New RPC Network</string>
  <string name="use_custom_rpc_network">Use custom RPC network instead</string>
  <string name="other">Other</string>
  <string name="network_name">Network Name</string>
  <string name="rpc_address">RPC Address</string>
  <string name="symbol_optional">Symbol(Optional)</string>
  <string name="name_optional">Name(Optional)</string>
  <string name="decimals_optional">Decimals(Optional)</string>
  <string name="add">Add</string>
  <string name="add_network_incomplete_error">Please input all necessary fields</string>
  <string name="add_network_success">Add Network Success</string>
  <string name="method_contract_creation">Contract Creation</string>
  <string name="invalid_network_address_tip">Invalid RPC Address!</string>
  <string name="deposit">Deposit</string>
  <string name="deposit_add_iotx">ADD IOTX</string>
  <string name="deposit_add_other">ADD XRC20 tokens</string>
  <string name="deposit_hint0_1">Transfer iotx to this address</string>
  <string name="deposit_hint1_1">Withdraw from exchanges</string>
  <string name="deposit_hint1_2">Obtain IOTX from a list of exchanges with popular trading pairs.</string>
  <string name="deposit_hint2_1">Get from swap tools</string>
  <string name="deposit_hint2_2">Swap the assets of other chains into IOTX through DEX.</string>
  <string name="deposit_hint3_1">Send tokens from Ethereum</string>
  <string name="deposit_hint3_2">Deposit your ERC20 tokens using ioTube and mimo </string>
  <string name="deposit_iotube_hint_1">Please visit </string>
  <string name="deposit_iotube_hint_2"> ioTube to add XRC20 assets to your IOTX account by other ETH wallets (</string>
  <string name="deposit_iotube_hint_3">detail here</string>
  <string name="deposit_iotube_hint_4">) and exchange XRC20 assets to IOTX with mimo.</string>
  <string name="deposit_iotube_hint_long">From other assets in ETH wallet. You can use ioTube to transfer your assets from ETH wallet to IOTeX and then swap them to IOTX via mimo (DEX on IoTeX). Here is the list of supported assets by ioTube: WBTC, ETH, BUSD, UNISWAP, Paxos Gold.</string>
  <string name="exchange_binance">Binance</string>
  <string name="exchange_gate">Gate</string>
  <string name="exchange_upbit">Upbit</string>
  <string name="exchange_kucoin">KuCoin</string>
  <string name="exchange_bittrex">Bittrex</string>
  <string name="exchange_mxc">MXC</string>
  <string name="exchange_coindcx">CoinDCX</string>
  <string name="exchange_mimo">mimo</string>
  <string name="im_token">imToken</string>
  <string name="meta_mask">Metamask</string>
  <string name="trust_wallet">Trust Wallet</string>
  <string name="deposit_other_wallet_tip">To convert from ERC20 to XRC20, Please click the wallet to continue. </string>
  <string name="deposit_other_tip">Please COPY this link to visit ioTube in your other wallet app as a DApp.</string>
  <string name="app_not_found">Target app not found</string>
  <string name="backup_title">Please backup your Private Key</string>
  <string name="back_up_your_privatekey">Keep the private key ONLY to yourself. Write it down on a note and keep it safe. Please make sure you have saved it.</string>
  <string name="err_get_private_key">Could not load the wallet account, please try a new one.</string>
  <string name="empty_stake_tip_title">Stake IOTX to Help Secure the IoTeX Network</string>
  <string name="empty_stake_tip_content">The IoTeX Network is maintained by 63 Decentralized Delegates. By voting for a Delegate, you can participate in network governance and earn IOTX rewards!</string>
  <string name="how_to_buy_iotx">How to buy IOTX &gt;&gt;</string>
  <string name="btn_copy_wallet_address">Copy Wallet Address</string>
  <string name="dapp_changelly">Changelly</string>
  <string name="changelly_url">https://changelly.com/exchange/usdt/iotx</string>
  <string name="save">Save</string>
  <string name="describe">Describe</string>
  <string name="name">Name</string>
  <string name="give_it_a_name">Give it a name</string>
  <string name="new_address">New Address</string>
  <string name="address_list">Address List</string>
  <string name="send_choose_coin">Send-choose coin</string>
  <string name="type_your_new_address">Type your new address</string>
  <string name="format_balance_small">0.00000001</string>
  <string name="format_balance_3_decimal_places">##0.000</string>
  <string name="format_balance_2_decimal_places">##0.00</string>
  <string name="format_balance_1_decimal_places">##0.0</string>
  <string name="format_balance_decimal_places">.</string>
  <string name="format_balance_qian_decimal">,</string>
  <string name="input_verify_private_key">Please verify your private key here.</string>
  <string name="choose_deposit_iotx">Choose your favorite method to deposit token to your ioPay wallet!</string>
  <string name="wallet_error_tips">Your private key data is missing.</string>
  <string name="learn_more"><u>Learn more.</u></string>
  <string name="wallet_learn_more_content">ioPay is currently unable to read your private key, due to one of the following reasons:</string>
  <string name="wallet_learn_more_content1">1. You restored your ioPay wallet from an old phone backup, but the backup cannot access the private key stored in phone’s keystore. </string>
  <string name="wallet_learn_more_content2">2. Your phone has experience an issue where the private key data was wiped from your keystore.</string>
  <string name="wallet_learn_more_content3">Your private key was NOT leaked. If you backed up your private key, you can uninstall/reinstall ioPay and enter your private key to access your wallet. If you did not backup your private key, you will not be able to use your wallet to send transactions or sign smart contracts — please contact us on Telegram with any questions.</string>
  <string name="what_does_it_mean">What does it mean?</string>
  <string name="missing_private_key">Missing Private Key</string>
  <string name="what_sign_message">Sign Message</string>
  <string name="edit">Edit</string>
  <string name="token">TOKEN</string>
  <string name="add_token">Add Token</string>
  <string name="stake">Staked</string>
  <string name="error">Error</string>
  <string name="watch_address_string">This is a \"Watch address\": you cannot sign actions for this wallet because you didnt provide the private key when you imported it.</string>
  <string name="add_watch_address_tips">Please notice that you are importing an Address, not a wallet account: this does not give you access to the funds in this address, instead it will only show you the wallet balance and info. If this is your wallet, please make sure you have a backup of the private key.</string>
  <string name="ioAddress">ioAddress</string>
  <string name="Web3Address">Web3Address</string>
  <string name="native_address">Native Address</string>
  <string name="web3_address">Web3 Address</string>
  <string name="native_receive_desc">This address only can accepts IOTX from the IOTX main network.</string>
  <string name="web3_receive_desc">This address only can accepts Web3 from the Web3 main network.</string>
  <string name="web3_native_receive_desc">This address only accepts native IOTX and XRC20 tokens on IoTeX Network.</string>
  <string name="save_QR_code">Save QR code</string>
  <string name="staking_reward">Staking Reward</string>
  <string name="burndrop_reward">Burndrop Reward</string>
  <string name="total_reward">Total Reward</string>
  <string name="compound_apy">Compound APY</string>
  <string name="last_day">Last Day</string>
  <string name="last_7_day">Last 7 Days</string>
  <string name="last_14_day">Last 14 Days</string>
  <string name="last_30_day">Last 30 Days</string>
  <string name="security_privacy">Security &amp; Privacy</string>
  <string name="security_privacy_desc">Change PIN,  Manage DApp auth</string>
  <string name="about_ioPay">About ioPay</string>
  <string name="ioPay">ioPay</string>
  <string name="about_ioPay_desc">Community, About</string>
  <string name="wallet_switch_desc">Quickly switch wallets on the homepage</string>
  <string name="privacy_policy"><u>Privacy policy </u></string>
  <string name="and"> and </string>
  <string name="terms_of_service"><u>Terms of service</u></string>
  <string name="open_source_license"><u>Open source license</u></string>
  <string name="add_custom_token_tips">Please enter the complete information</string>
  <string name="please_input_name">Please input your name!</string>
  <string name="please_input_decimals">Please input your decimals!</string>
  <string name="please_input_symbol">Please input your symbol!</string>
  <string name="lower_limit">%1$s must be at least %2$s</string>
  <string name="switch_rpc_networks">Switch RPC networks.</string>
  <string name="antenna_networks">Antenna Networks</string>
  <string name="antenna_networks_desc">Switch antenna networks on IoTeX main network.</string>
  <string name="switch_title">This site would like to switch the network</string>
  <string name="switch_tips">This will switch the selected network within ioPay to a previously added network:</string>
  <string name="see_more">See More</string>
  <string name="add_network_title">Allow this site to add a network?</string>
  <string name="add_network_tips1">This will allow this network to be used within ioPay.</string>
  <string name="add_network_tips2">ioPay does not verify custom networks.Learn about scams and network security risks.</string>
  <string name="add_network_tips3">Learn about</string>
  <string name="add_network_tips4">scams and network security risks.</string>
  <string name="add_network_tips5">The network details for this chain ID do not match our records.We recommend that you verify the network details before proceeding</string>
  <string name="network_url">Network URL</string>
  <string name="chain_id">Chain ID</string>
  <string name="view_all_detail">View all details</string>
  <string name="approve">Approve</string>
  <string name="input_name">Please input name.</string>
  <string name="input_symbol">Please input symbol.</string>
  <string name="input_decimals">Please input decimals.</string>
  <string name="decimals_error">The decimals is error.</string>
  <string name="more">More</string>
  <string name="my_dApps">My DApps</string>
  <string name="hot_dApps">Hot DApps</string>
  <string name="transfer_tips_content1">Sending on IoTeX Network</string>
  <string name="transfer_tips_content2">Please make sure the recipient is on IoTeX network. If you are sending to an exchange account, please verify they accepting assets on IoTeX network. Click here to verify.</string>
  <string name="transfer_tips_title">IOTX Transfer Instructions</string>
  <string name="transaction_submitted">Transaction Submitted</string>
  <string name="transaction_submitted_tips">Waiting for confirmation</string>
  <string name="transaction_confirmed">Transaction Confirmed!</string>
  <string name="transaction_confirmed_tips">Tap to view this transaction</string>
  <string name="input_address_hint">Input the receive address</string>
  <string name="input_amount_hint">Input the amount</string>
  <string name="refresh">Refresh</string>
  <string name="copy_url">Copy Url</string>
  <string name="open_in_browsers">Open in browsers</string>
  <string name="confirm">Confirm</string>
  <string name="confirmation">Confirmation</string>
  <string name="wallets">Wallets</string>
  <string name="wallet">Wallet</string>
  <string name="staked">STAKED</string>
  <string name="allow">Allow</string>
  <string name="oops">Oops</string>
  <string name="failure_alert">Something went wrong. Please try again later.</string>
  <string name="reject">Reject</string>
  <string name="transaction_error_alert">Transaction error, exception thrown in contract.</string>
  <string name="wallet_connect_account_desc_disconnected">WalletConnect request to connect your wallet https://walletconnect.com </string>
  <string name="wallet_connect_account_desc_connected">WalletConnect request to connect your wallet https://walletconnect.com </string>
  <string name="wallet_connect_account_desc1">Allow getting current wallet address </string>
  <string name="wallet_connect_account_desc2">Allow signature request from the current wallet </string>
  <string name="disconnect">Disconnect</string>
  <string name="days_left_string">%s days left</string>
  <string name="stake_status_locked_duration">%s days locked</string>
  <string name="ready_to_unstake">Ready to Unstake</string>
  <string name="developer_mode_test"><u>Enable developer mode to use testnet</u></string>
  <string name="chain_id_for">ChainID:%s</string>
  <string name="rpc_url_for">RPC URL:%s</string>
  <string name="open_notification_confirm_title">Please Turn On Notifications</string>
  <string name="add_network_tips5_highlight">verify the network details</string>
  <string name="hour_left_string">%s hour left</string>
  <string name="ready_to_stake">Ready to Stake</string>
  <string name="feedback">Feedback</string>
  <string name="amount_must_greater_10">Stake amount must grater than 10</string>
  <string name="token_id">Token id</string>
  <string name="buy">Buy</string>
  <string name="earn">Earn</string>
  <string name="wallet_create_form_name_bottom">Robots lovingly delivered by Robohash.org</string>
  <string name="about_desc">Community, Feedback, About</string>
  <string name="transaction_failed">Transaction failed!</string>
  <string name="back_to_source_app">Back to source app</string>
  <string name="app_initial_guide_tip_1">Your All-in-one Crypto Wallet</string>
  <string name="app_initial_guide_sub_tip_1">It is more than a wallet! Advanced functionality meets novice simplicity.</string>
  <string name="app_initial_guide_tip_2">Manage Assets Across Multiple Chains</string>
  <string name="app_initial_guide_sub_tip_2">With ioPay, you can seamlessly switch between IoTeX, Ethereum, BSC, Polygon and more networks.</string>
  <string name="app_initial_guide_tip_3">Buy, Swap, and Stake Assets</string>
  <string name="app_initial_guide_sub_tip_3">Open the universe of DeFi and move assets across chains. It is never been easier to start learning and earning in the Web3 world.</string>
  <string name="app_initial_guide_tip_4">Explore Hundreds of DApps.</string>
  <string name="app_initial_guide_sub_tip_4">Choose from DeFi, NFTs, GameFi, DePIN, Marketplaces, and more, all without ever leaving ioPay.</string>
  <string name="robots">Robots</string>
  <string name="blockies">Blockies</string>
  <string name="Jazzicons">Jazzicons</string>
  <string name="choose_avatar">Choose avatar</string>
  <string name="switch_account">Switch account</string>
  <string name="avatar">Avatar</string>
  <string name="remove_token">Remove</string>
  <string name="remove_token_confirm_message">Do you want to remove this token?</string>
  <string name="remove_token_confirm_yes_title">Token removed!</string>
  <string name="remove_token_confirm_yes_message">If you change your mind, you can add it back by tapping on add token.</string>
  <string name="notice">Notice</string>
  <string name="got_it">GOT IT</string>
  <string name="change_avatar_tips">Robots, Blockies and Jazzicons are different styles for your accounts </string>
  <string name="skip">Skip</string>
  <string name="guide_title_1">Balance</string>
  <string name="guide_content_1">The native token balance of your wallet under different networks. </string>
  <string name="guide_title_2">Send</string>
  <string name="guide_content_2">Send and receive tokens to anyone in the Web3 world (QR code scan is supported), and check transaction history in the Activities tab. </string>
  <string name="guide_title_3">Deposit</string>
  <string name="guide_content_3">You can deposit assets to wallets using address and QR Code, note that there are native address and Web3 ones. The native address only applies to native IOTX and XRC20 tokens on IoTeX Network. </string>
  <string name="guide_title_4">Earn</string>
  <string name="guide_content_4">An easy way to check and play with the  profitable platform for your assets with secure.</string>
  <string name="guide_title_5">Token List</string>
  <string name="guide_content_5">All the added token balance will show in token list, and you can add tokens by tapping + icon at the right corner.</string>
  <string name="guide_title_6">Private Key</string>
  <string name="guide_content_6">A private key is a secret number that is used in cryptography, similar to a password.  It is vitally important to prevent your private keys from being lost or compromised.</string>
  <string name="guide_title_7">Network</string>
  <string name="guide_content_7">ioPay supports mutlichain now,  you can choose different network in the list,  have a good time in Web3 world. </string>
  <string name="get_started">Get Started</string>
  <string name="welcome_to_ioPay">Welcome to ioPay!</string>
  <string name="no_thanks">No, thanks</string>
  <string name="take_the_tour">Take the tour</string>
  <string name="welcome_to_ioPay_tips">It is never been easier to start learning and earning in the Web3 world! Here is a quick start guide showing how to use ioPay in simple steps.</string>
  <string name="def">Default</string>
  <string name="custom">Custom</string>
  <string name="add_a_node">Add a custom node</string>
  <string name="add_node">Add custom node</string>
  <string name="nodes_setting">Nodes Setting</string>
  <string name="delete_node">Delete Node</string>
  <string name="delete_network_tips">Are you sure you want to delete this network? This network will be deleted immediately. </string>
  <string name="switch_node">Switch node</string>
  <string name="edit_network">Edit network</string>
  <string name="rpc_url">RPC URL</string>
  <string name="network_node_error">The RPC URL you have entered returned a different chain ID. Please update the Chain ID to match the RPC URL of the network you are trying to add.</string>
  <string name="new_rpc_url">New RPC URL</string>
  <string name="currency_symbol">Currency Symbol</string>
  <string name="explorer_url_optional">Block Explorer URL(Optional)</string>
  <string name="add_network_tips">Please add a trusted network, ioPay cannot verify the security of custom network. Malicious network providers may record your network activity and lie about the state of the blockchain.</string>
  <string name="add_custom_network">Add Custom Network</string>
  <string name="edit_custom_network">Edit Custom Network</string>
  <string name="network_name_error">Network Name is invalid</string>
  <string name="currency_symbol_error">Currency Symbol is invalid</string>
  <string name="rpc_url_error">Rpc Url is invalid</string>
  <string name="chain_id_error">Chain Id is invalid</string>
  <string name="network_exists_error">The Network already exists</string>
  <string name="rpc_url_exists_error">The Rpc Url already exists</string>
  <string name="delete_node_error">The last Rpc Url cannot be deleted</string>
  <string name="general_desc_avatar">Language, Web3 address, Avatar</string>
  <string name="general_desc_gp_avatar">Language, Notification, Web3 address, Avatar</string>
  <string name="wallet_connect_request_tips">request to connect your wallet</string>
  <string name="google_review_title">Do you like ioPay?</string>
  <string name="google_review_love_it">I love it!</string>
  <string name="google_review_improved">It needs to be improved</string>
  <string name="authentication">Authentication</string>
  <string name="auth_before_login">Auth before login</string>
  <string name="auth_before_transaction">Auth before transaction</string>
  <string name="enter_wallet">Enter Wallet</string>
  <string name="news_date_format_year">dd MMM, yyyy</string>
  <string name="news_date_format_day">dd MMM</string>
  <string name="watch_wallet_warning">The current wallet is a watch address, you are unable to execute this action on it.</string>
  <string name="chart_no_data_desc">No chart data available.</string>
  <string name="reward_distributed">Reward Distributed</string>
  <string name="failed_qr_code">Failed to identify the QR code</string>
  <string name="fio_name">FIO Name</string>
  <string name="fio_name_error">Invalid fio name, please check the linked network and token.</string>
  <string name="fio_name_tips">FIO protocol names are supported. </string>
  <string name="create_new_wallet">Create new wallet</string>
  <string name="create_new_wallet_caption">Create a new wallet and backup recovery phrase or private key.</string>
  <string name="import_exist_wallet">Import an existing wallet</string>
  <string name="import_exist_wallet_caption">Recovery phrase or private key import supported.</string>
  <string name="watch_mode">Watch Mode</string>
  <string name="watch_mode_caption">Enter the wallet address to watch it.</string>
  <string name="your_mnemonic_phrase">Your recovery phrase</string>
  <string name="create_mnemonic_tips">Please write down your 12-word recovery phrase and store safely.</string>
  <string name="create_mnemonic_warning"> - You can write the phrases on paper or save them in a secure password manager.\n- Do not send them via email or take screenshots.\n- The order of the words in the phrases is important.</string>
  <string name="regenerate_underline"><u>Regenerate</u></string>
  <string name="mnemonic_verify_tips">Please fill in order according to the recovery phrase you backup.</string>
  <string name="mnemonic_verify_error">The order of recovery phrase is wrong, please check if your back up is correct.</string>
  <string name="mnemonic_verify">Verify Recovery Phrase</string>
  <string name="mnemonic">Recovery Phrase</string>
  <string name="mnemonic_phrase">Recovery Phrase</string>
  <string name="add_wallet">Add Wallet</string>
  <string name="import_via_private_key_caption">Import the account with the private key</string>
  <string name="import_via_mnemonic_caption">Import the account with the recovery phrase.</string>
  <string name="import_via_keystore_caption">Add a wallet by importing a keystore</string>
  <string name="import_iotex_wallet">Import IoTeX wallet</string>
  <string name="import_iotex_wallet_caption">Only supports the recovery phrase or private key from ioPay.</string>
  <string name="import_evm_wallet">Import EVM wallet</string>
  <string name="import_evm_wallet_caption">Supports all the recovery phrase or private key from EVM wallet start with 0x.</string>
  <string name="give_a_name">Give it a name</string>
  <string name="manage_wallet">Manage Wallet</string>
  <string name="no_private_key_wallet">No private key wallet</string>
  <string name="no_mnemonic_phrase">No recovery phrase</string>
  <string name="mnemonic_invalid">The recovery phrase is invalid</string>
  <string name="mnemonic_exist">The recovery phrase already exists</string>
  <string name="input_mnemonic">Please use space to separate the recovery phrase.</string>
  <string name="select_wallet">Select Address to Import</string>
  <string name="select_wallet_caption">This recovery phrase corresponds to the following wallet address, please select the address you want to import</string>
  <string name="back_up">Backup</string>
  <string name="manage_mnemonic">Manage Recovery Phrase</string>
  <string name="export_mnemonic">Export recovery phrase</string>
  <string name="choose_network">Choose network</string>
  <string name="load_more_underline"><u>Load More</u></string>
  <string name="edit_mnemonic_name">Edit recovery phrase name</string>
  <string name="add_mnemonic_wallet">Add recovery phrase</string>
  <string name="delete_mnemonic_warning">This will permanently delete all your existing wallets! Make sure you have backup recovery phrase!</string>
  <string name="warning">Warning!</string>
  <string name="reveal_mnemonic_phrase">Reveal Recovery phrase</string>
  <string name="set_the_mnemonic_name">Set the recovery phrase name</string>
  <string name="delete_u"><u>Delete</u></string>
  <string name="translation_contributors">Translation contributors</string>
  <string name="fio_name_tips1">FIO protocol names and ENS are supported. </string>
  <string name="fio_name_tips2">FIO protocol names and Space ID are supported. </string>
  <string name="network_anomaly">Network Anomaly!</string>
  <string name="network_anomaly_desc">Abnormal Network appears to your ioPay, which may prevent your actions. Please check the settings and switch the Node.</string>
  <string name="go_to_setting">Go to settings</string>
  <string name="multi_close_all">Close All</string>
  <string name="multi_done">Done</string>
  <string name="multi_browser">Browser</string>
  <string name="my_wallet">My wallet</string>
  <string name="search_address">Search address</string>
  <string name="add_network">Add Network</string>
  <string name="search_in_chain_list_org">Search in chainlist.org</string>
  <string name="function_request">Feature request</string>
  <string name="manage_token">Manage Token</string>
  <string name="current_network">Current Network:</string>
  <string name="default_network">Default Network:</string>
  <string name="customized_network">Customized Network:</string>
  <string name="current_node_not_reliable">The current node is not authenticated and may not be reliable.</string>
  <string name="adding_non_default_node">You are adding a non-default node which is not verified and may not be reliable.</string>
  <string name="i_got_it">I Got It</string>
  <string name="message_notice">Notice</string>
  <string name="setting_w3bstream">W3bstream</string>
  <string name="setting_w3bstream_info">Decentralized Infrastructure for Real-World Devices and Data</string>
  <string name="geo_location">Geo Location</string>
  <string name="meta_des">Enable certain rewards and functionality in Web3 DApps by providing a proof of your trusted GPS location.</string>
  <string name="connect_mobile_device">Connect W3bstream to this mobile device</string>
  <string name="connect">Connect</string>
  <string name="activate_geo_location">Activate Geo Location</string>
  <string name="connect_w3bstream">Connecting W3bstream...</string>
  <string name="connected_w3bstream">Connected W3bstream</string>
  <string name="you_have_successfully_connected_w3bstream">You have successfully connected W3bstream to your mobile device!</string>
  <string name="meta_info">Turning on Geo Location will tie your mobile device to your wallet address via a decentralized identity including your mobile device\'s IMEI number and Serial Number. By turning on Geo Location you can enable certain rewards and functionality in Web3 DApps by providing a proof of your trusted GPS location.</string>
  <string name="pebble_upload_failed">Upload Data Failed</string>
  <string name="pebble_supported_net">Supported Network: IoTeX Testnet</string>
  <string name="pebble_uploading_data">Uploading data</string>
  <string name="meta_setting">Setting</string>
  <string name="meta_location">Location</string>
  <string name="meta_gps_interval">GPS Collection Interval</string>
  <string name="meta_gps_precision">GPS Precision</string>
  <string name="supported_network">Supported Network: IoTeX Testnet</string>
  <string name="location_fail">Failed to get location information</string>
  <string name="open_location_permission">Please turn on location permission in settings</string>
  <string name="try_later">I will do it later</string>
  <string name="wallet_error">Wallet Error</string>
  <string name="your_mobile_device_is">Your mobile device is bound to the following wallet address:</string>
  <string name="the_wallet_you_are_currently_using">The wallet you are currently using to access this page does not match the above address. Please change your wallet to the one that has been previously bound to this device.</string>
  <string name="failure">Failure</string>
  <string name="select_specific_wallet">Please select the wallet whose wallet address is %s</string>
  <string name="security_privacy_desc2">Change PIN,  Manage DApp auth, Manage recovery phrase</string>
  <string name="delete_wallet_warning">Are you sure you would like to delete this wallet? Make sure you have backup of your wallet.</string>
  <string name="select_hd_path">Select HD Path</string>
  <string name="select_hd_path_caption">If you don’t see the accounts you expect, try switching the HD Path.</string>
  <string name="select_network">Select network</string>
  <string name="select_wallet_warning">Maximum 10 wallet addresses can be selected at one time.</string>
  <string name="supported_main_network">Supported Network: IoTeX</string>
  <string name="amount_remain">Current Amount</string>
  <string name="risk">Risk</string>
  <string name="high_risk">High Risk</string>
  <string name="You_have_left_iopay">ioPay is running in the background.</string>
  <string name="backup_tips">Backup Tips</string>
  <string name="obtaining_private_key">Obtaining Private Key equals owning all assets</string>
  <string name="copy_it_on_the_paper_and_keep">Copy it on the paper and keep in a safe place once the private key gets lost,it cannot be retrieved.Please be sure to back up the private key</string>
  <string name="for_the_safety">For the safety,</string>
  <string name="please_copy_in_sections">please copy in sections</string>
  <string name="copy_all">Copy All</string>
  <string name="search_nft">Name or Contract</string>
  <string name="geo_location_tips">Geo Location Tips</string>
  <string name="to_upload_accurate_gps_data">To upload accurate GPS data, please set the location access to Always allow or Allow While in Using</string>
  <string name="details">Details</string>
  <string name="contract_addr">Contract Address</string>
  <string name="token_standard">Token Standard</string>
  <string name="blockchain">Blockchain</string>
  <string name="share">Share</string>
  <string name="share_nft_caption">MULTI-CHAIN SUPPORT CRYPTO WALLET</string>
  <string name="once_the_private_key_gets_lost">· Once the private key gets lost, it cannot be retrieved.</string>
  <string name="Please_be_sure_to_back_up_the_private_key">· Please be sure to back up the private key.</string>
  <string name="theme">Theme</string>
  <string name="iopay_default">ioPay</string>
  <string name="dapp_theme_des">Various themes will help you customize your ioPay and get into your favorite DApp with a glance</string>
  <string name="iopay_unsafe_state">ioPay unsafe state</string>
  <string name="default_theme">Default Theme:</string>
  <string name="ecosystem_theme">Ecosystem Theme:</string>
  <string name="receive_transaction_notification">Receive Transaction Notification</string>
  <string name="receive_system_notification">Receive System Notification</string>
  <string name="Keep_track_of_important_updates">Keep track of important updates, newsletters, or other relevant information by enabling push notification.</string>
  <string name="to_change_your_iopay_password">To change your ioPay password, please first verify your current one.</string>
  <string name="after_clearing_the_allowance_record">After clearing the allowance record, you will have to allow each DApp again in the future.</string>
  <string name="configure_your_authentication">Configure your authentication methods and procedures.</string>
  <string name="contract_approval_checker">Contract Approval Checker</string>
  <string name="please_make_sure_to_back_up_the_wallet">Please make sure to back up the wallet used to register geolocation. Once the registration is successful, it cannot be changed.</string>
  <string name="did_not_find_the_expected_network">Didn\'t find the expected network? Set the network or enable developer mode in settings.</string>
  <string name="txid">Txid</string>
  <string name="tips_w3bstream">W3bstream</string>
  <string name="register_geo_location">Register Geo Location</string>
  <string name="register_success">Register success</string>
  <string name="did_not_find_the_expected_network_tips">settings</string>
  <string name="all_channels">All Channels</string>
  <string name="sent_nft_success">Sent NFT Success</string>
  <string name="sent_nft_failed">Sent NFT Failed</string>
  <string name="the_current_gps_signal_is_weak">The current GPS signal is weak, and the uploaded location is inaccurate.</string>
  <string name="upload_data_failed">Upload Data Failed</string>
  <string name="weak_signal">Weak signal</string>
  <string name="back_to_the_source_app">Back to the source app</string>
  <string name="low">Low</string>
  <string name="market">Market</string>
  <string name="high">High</string>
  <string name="max_fee">Max fee: </string>
  <string name="weak_gps">Weak GPS</string>
  <string name="you_are_about_to_enter">You are about to enter a third party DApp</string>
  <string name="please_note_that_the_dapp_page">Please note that the DApp page is fully managed by the project, and ioPay app functions solely as a browser. When using DApps, exercise caution and verify the destination address or contract address before making any transfers.</string>
  <string name="i_understood_do_not_show_this_again">I understood, do not show this again.</string>
  <string name="please_note_that_filda">Please note that FilDA is fully managed by the project, and ioPay app functions solely as a browser. When using DApps, exercise caution and verify the destination address or contract address before making any transfers.</string>
  <string name="please_note_buy_title">Disclaimer</string>
  <string name="no_dapp">No DApp</string>
  <string name="sbts_are_issued_by">SBTs (Soulbound Tokens) are credentials or affiliations issued to Blockchain accounts or wallets and are not transferable.</string>
  <string name="learn_more_short">Learn more</string>
  <string name="i_understood_do_not_show_this_again_today">I understood, do not show this again today.</string>
  <string name="create_new_wallet_caption_02">Create a new wallet and backup recovery phrase</string>
  <string name="import_exist_wallet_caption_02">Recovery phrase or private key import supported</string>
  <string name="import_exist_by_mnemonic">Supports recovery phrase</string>
  <string name="import_via_private_key_caption_02">Import the wallet with plaintext private key</string>
  <string name="import_via_keystore_caption_02">Add a wallet by importing  encrypted keystore</string>
  <string name="manage_private_key">Manage Private Key</string>
  <string name="reveal_phrase">Reveal Recovery Phrase</string>
  <string name="website">Website</string>
  <string name="approve_token">Approve Token</string>
  <string name="approve_amount">Approve Amount</string>
  <string name="unlimited">Unlimited</string>
  <string name="edit_approve_amount">Edit Approve Amount</string>
  <string name="edit_contract_name">Edit Contract Name</string>
  <string name="current_amount">Current Amount</string>
  <string name="set_approve_amount">Set Approve Amount</string>
  <string name="max_big">MAX</string>
  <string name="enter_a_number">Enter a number</string>
  <string name="only_enter_a_number_that_you">Only enter a number that you\'re comfortable with the contract spending now or in the future. You can always increase the approve amount later.</string>
  <string name="delete_network"><u>Delete network</u></string>
  <string name="symbol">Symbol</string>
  <string name="menu_explore">Explore</string>
  <string name="edit_address">Edit Address</string>
  <string name="place_bid_for_vita">Place bid for VITA</string>
  <string name="view">View</string>
  <string name="you_will_be_redirected_to_a_third_party_dapp">You will be redirected to a third party DApp</string>
  <string name="please_note_that_is_fully_managed_by_the_project">Please note that %s is fully managed by the project, and ioPay app functions solely as a browser.When using DApps, exercise caution and verify the destination address or contract address before making any transfers.</string>
  <string name="choose_from_defi_nfts_gamefi_depin">Choose from DeFi, NFTs, GameFi, DePIN, Marketplaces, and more, all without ever leaving ioPay.</string>
  <string name="contract_name">Contract Name</string>
  <string name="use_default">Use Default</string>
  <string name="view_on_iotexscan"><u>View on IoTeXScan</u></string>
  <string name="hd_path_caption">ioPay supports the default BIP44 Standard (IOTEX HD PATH: 304 )/(EVM HD PATH:60), learn more.</string>
  <string name="learn_more_lowercase">learn more</string>
  <string name="verified_contract">Verified Contract</string>
  <string name="view_contract">View Contract</string>
  <string name="obtaining_private_key_equals_owning_all_assets">· Obtaining Private Key equals owning all assets</string>
  <string name="copy_it_on_the_paper_and_keep_in_a_safe_place">· Copy it on the paper and keep in a safe place.</string>
  <string name="obtaining_recovery_phrase_equals_owning_all_assets">· Obtaining recovery phrase equals owning all assets. </string>
  <string name="once_the_recovery_phrase_gets_lost">· Once the recovery phrase gets lost, it cannot be retrieved.</string>
  <string name="Please_be_sure_to_back_up_the_recovery_phrase">· Please be sure to back up the recovery phrase.</string>
  <string name="view_on_explorer"><u>View on explorer</u></string>
  <string name="type_name_or_contract_to_search_token">Type name or contract to search token</string>
  <string name="set_approve_allowance">Set Approve Allowance</string>
  <string name="please_scroll_to_read_all_sections">Please scroll to read all sections</string>
  <string name="terms_of_use">Terms of Use</string>
  <string name="i_agree_to_the_terms_of_use">I agree to the Terms of Use, which apply to my use of ioPay and all of its features</string>
  <string name="please_turn_on_app_notifications_to_stay">Please turn on app notifications to stay updated with the latest news and important information related to ioPay. </string>
  <string name="you_can_write_the_phrases_down_in_a_piece_of_paper">You can write the phrases down in a piece of paper or save ina secure password manager. Don’t email them or screenshot them. The order of words is important.</string>
  <string name="iopay_is_locked">ioPay is locked</string>
  <string name="try_again_in">try again in %s</string>
  <string name="next_step">NEXT</string>
  <string name="next_pf">Next</string>
  <string name="receipt_contract">The address you have entered is a contract address. Please check and enter a valid wallet address.</string>
  <string name="section_1">Section 1</string>
  <string name="section_2">Section 2</string>
  <string name="copy_mnemonic">For the assets safety, kindly copy the recovery phrases in sections.</string>
  <string name="join_the_beta">Join the beta</string>
  <string name="nft_marketplace">NFT Marketplace</string>
  <string name="by_approving_this_you_are_authorizing_full_access">By approving this, you are authorizing full access to all your NFTs from the chosen collection, including any you acquire in the future. Proceed with caution.</string>
  <string name="sign_request">Sign Request</string>
  <string name="token_id_big">Token ID</string>
  <string name="more_details_u"><u>More Details</u></string>
  <string name="more_details">More Details</string>
  <string name="choose_from_defi_nfts_gamefi_machinefi_marketplaces">Choose from DeFi, NFTs, GameFi, MachineFi, marketplaces, and more, all without ever leaving ioPay.</string>
  <string name="the_wallet_connect_protocol_be_shut_down">The WalletConnect v1.0 protocol be shut down on June 28, 2023 at 2pm (UTC)</string>
  <string name="view_data">View Data</string>
  <string name="big_mimo">Mimo</string>
  <string name="phrase_hd_path_bip44_standard">Phrase HD Path: BIP44 Standard (EVM HD PATH:%s)</string>
  <string name="stake_amount_duration">Stake Amount &amp; Duration</string>
  <string name="candidate_name">Candidate Name</string>
  <string name="stake_lock">Lock Bucket</string>
  <string name="stake_unlock">Unlock Bucket</string>
  <string name="stake_withdraw_receiption">Receiver</string>
  <string name="withdraw">Withdraw</string>
  <string name="voter_address">Voter Address</string>
  <string name="sign">Sign</string>
  <string name="confirm_password">Confirm Your Passcode</string>
  <string name="lock_screen_title_pf">Input pin code or use fingerprint</string>
  <string name="no_fingerprints_title_pf">No fingerprints found</string>
  <string name="no_fingerprints_message_pf">No fingerprints found. Please add fingerprints in the settings if you want to use this authorization method.\"</string>
  <string name="settings_pf">Settings</string>
  <string name="cancel_pf">PIN</string>
  <string name="fingerprint_description_pf">Confirm fingerprint to continue</string>
  <string name="fingerprint_hint_pf">Touch sensor</string>
  <string name="fingerprint_not_recognized_pf">Fingerprint not recognized. Try again</string>
  <string name="fingerprint_success_pf">Fingerprint recognized</string>
  <string name="sign_in_pf">Authentication required</string>
  <string name="second">second</string>
  <string name="minute">minute</string>
  <string name="recovery_phrase">Recovery phrase</string>
  <string name="add_recovery_phrase">Add Recovery Phrase</string>
  <string name="delete_mnemonic_warning_word">This will permanently erase all wallets associated with this recovery phrase! Ensure you have a backup of the Recovery phrase!</string>
  <string name="fio_name_tips3">FIO protocol names and INS are supported. </string>
  <string name="no_fingerprints_message">No fingerprints found. Please add fingerprints in the settings if you want to use this authorization method.</string>
  <string name="estimate_gas_error">Transaction cannot proceed: gas fee estimate error. Please adjust gas fee manually.</string>
  <string name="you_can_create_or_import_recovery_phrase_wallet">You can create or import recovery phrase wallet through Add Wallet.</string>
  <string name="this_wallet_list_includes_the_wallet_addresses">This wallet list includes the wallet addresses imported by private key, keystore &amp; watcher address.</string>
  <string name="this_wallet_list_is_for_recovery_phrase_wallets">This wallet list is for recovery phrase wallets, a single recovery phrase can be used to access multiple wallets.</string>
  <string name="you_have_the_option_to_generate_a_new_wallet_using_this_recovery_phrase">You have the option to generate a new wallet using this recovery phrase, but please be aware that the private key will be entirely distinct.</string>
  <string name="you_have_the_option_to_either_generate_a_single_recovery_phrase">You have the option to either generate a single recovery phrase or import multiple recovery phrase wallets using the Manage wallets feature.</string>
  <string name="reveal_and_backup_your_recovery_phrase">Reveal and backup your recovery phrase, these words are the key to all your accounts. DO NOT share this phrase with anyone!</string>
  <string name="by_managing_the_private_key">The private key provides access to mnemonic and private key wallet private keys. Keep secure. One mnemonic phrase can generate multiple wallets with unique private keys.</string>
  <string name="by_managing_your_recovery_phrase">By managing your recovery phrase, you have the ability to create, reveal, or delete your wallets associated with it.</string>
  <string name="please_note_buy_content_1">The purchase of cryptocurrency is provided by a third-party service provider. The guantity and the time you receive the digital assets depends on the service provider and the blockchain network condition.</string>
  <string name="insufficient_gas_fee">Insufficient Gas fee</string>
  <string name="activities_big">ACTIVITIES</string>
  <string name="tools_big">TOOLS</string>
  <string name="compound_method_register">Register</string>
  <string name="compound_method_unregister">Unregister</string>
  <string name="staking_details">Staking Details</string>
  <string name="vote_for">Vote for</string>
  <string name="lock_duration">Lock Duration</string>
  <string name="stake_lock_detail">Stake Lock</string>
  <string name="bucket_status">Bucket Status</string>
  <plurals name="day_num">
    <item quantity="one">%d day</item>
    <item quantity="other">%d days</item>
  </plurals>
  <string name="aa_wallet">AA wallet</string>
  <string name="aa_wallet_caption">ioPay facilitates the creation of AA Smart Accounts on the IoTeX mainnet.</string>
  <string name="create_new_aa_wallet">Create new AA wallet</string>
  <string name="create_new_aa_wallet_caption">Assets are held by smart contracts exclusively, not by externally-owned accounts (EOAs).</string>
  <string name="recover_aa_wallet">Recover AA wallet</string>
  <string name="recover_aa_wallet_caption">Recover your AA wallet through E-mail</string>
  <string name="recover_aa_wallet_caption_2">To ensure the security of your assets,  a 24-hour waiting period is required to recover the AA wallet on a new device.</string>
  <string name="support_network">Supported network:</string>
  <string name="potential_risks">Be aware of potential risks:</string>
  <string name="potential_risks_caption">Since the AA wallet uses your email as a recovery method, it\'s crucial to safeguard your private key and email.</string>
  <string name="recover_aa_wallet_tips">Recover the AA wallet using the email associated with your bound wallet.</string>
  <string name="input_your_email">Input your e-mail</string>
  <string name="aa_wallet_list_tips">We found the following wallets associated with your email address. Please select one to recover.</string>
  <string name="send_recover_email_tips">Please be aware that once you send this email to recover your AA wallet, the wallet will be disabled on all other devices where it was previously active.</string>
  <string name="sned_recover_email_arlet">Kindly send us the following subject using the recovery email, please.</string>
  <string name="to_colon">To:</string>
  <string name="subject_colon">Subject:</string>
  <string name="recover_email_caption">Email is only recorded in the local database. Please note that this information is only stored locally and is not recorded anywhere else.</string>
  <string name="email">E-mail</string>
  <string name="send_code">Send Code</string>
  <string name="enter_email">Enter E-mail</string>
  <string name="code">Code</string>
  <string name="enter_code">Enter code</string>
  <string name="learn_more_about">Learn more about</string>
  <string name="aa_wallet_underline"><u>AA wallet</u></string>
  <string name="apply_free_gas_paragraph_1">During the smart account launch period, there will be free gas activities with the following details:</string>
  <string name="apply_free_gas_paragraph_2">When you initiate a transfer, NFT transfer, or contract interaction through the AA wallet in the ioPay App, as long as the current Gas Fee is &lt; 10 IOTX the Gas can be fully subsidized.</string>
  <string name="apply_free_gas_paragraph_3">Please note that the free gas fee is only used to deduct when paying gas fees, it will not be transferred to the wallet balance. You can apply 10 lOTX per day as free gas fee.</string>
  <string name="apply_free_gas_paragraph_4">Moving forward, ioPay will continue contributing toAccount Abstraction technology based on EIP-4337, and will successively roll out social recovery and other unique features.</string>
  <string name="have_applied_free_gas">You have applied free gas fee today.</string>
  <string name="apply_exact_gas">Apply %1$s Gas Fee</string>
  <string name="apply_free_gas_fee">Apply Free Gas Fee</string>
  <string name="apply_free_gas_fee_now_underline"><u>Apply Free Gas Fee Now</u></string>
  <string name="remain_free_gas_fee">Remaining free Gas Fee:</string>
  <string name="setup_email">Set up E-mail</string>
  <string name="setup_email_desc">To ensure the safety of your assets, we recommend setting up a recovery email. If you fail to establish a recovery email or lose access to the linked email account, and cannot restore on your device, we will be unable to assist you in recovering your wallet.</string>
  <string name="setup_email_tips">Please use Gmail as the recovery email.</string>
  <string name="invalid_email">Invalid E-mail</string>
  <string name="invalid_code">Error code</string>
  <string name="have_sent_email">I have sent e-mail</string>
  <string name="wallet_exists">The wallet already exists.</string>
  <string name="has_not_bound_wallet">The email hasn\'t been bound to a wallet yet.</string>
  <string name="the_aa_wallet_has_not_been_recovered">The AA wallet hasn\'t been recovered successfully yet, transactions and other features are currently unavailable. Please use the bound email to recover the AA wallet and gain access to all features.</string>
  <string name="value_money">≈$ %s</string>
  <string name="card_single_value">$ %s</string>
  <string name="private_key_wallet">Private Key Wallet</string>
  <string name="depin_scan">DePINscan</string>
  <string name="the_depin_wallet_of_choice">THE DePIN WALLET OF CHOICE</string>
  <string name="check_your_depin_assets_with_iopay">Check your DePIN assets with ioPay!</string>
  <string name="create_new_wallet_import_wallet">Create new wallet, Import wallet</string>
  <string name="since_you_are_currently_in_visitor_mode">Since you\'re currently in visitor mode, you may not be able to fully experience DApp functions. Please create a wallet or import a wallet.</string>
  <string name="aa_wallet_is_recovering">AA wallet is Recovering</string>
  <string name="recovering_alert_1">Your AA wallet is currently being recovering on a new device.</string>
  <string name="recovering_alert_2">If it\'s not your recovery application you can stop now.</string>
  <string name="stop_recovering">Stop Recovering</string>
  <string name="continue_recovering_underline"><u>It\'s me, continue recovering</u></string>
  <string name="please_be_patient">Please be patient.</string>
  <string name="congratulation">Congratulations!</string>
  <string name="keep_email_alert">You\'ve successfully created your wallet. Please keep your email safe!</string>
  <string name="iopay_gas_subsidy">ioPay Gas Subsidy</string>
  <string name="free_gas_alert_1">Applied free gas fee automatically and activated AA wallet successfully.</string>
  <string name="free_gas_alert_2">Your account free trials for today: %s</string>
  <string name="free_gas_alert_3"><u>Learn more about ioPay gas subsidy</u></string>
  <string name="enter_aa_wallet">Enter AA wallet</string>
  <string name="aa_wallet_expired">AA wallet Expired</string>
  <string name="wallet_expired_alert_1">The current wallet has expired! Transaction capabilities and Dapp operations are currently disabled.</string>
  <string name="wallet_expired_alert_2">Please switch to another wallet.</string>
  <string name="send_recovery_email_undeline"><u>Send Recovery Email</u></string>
  <string name="send_recovery_email_alert">If you have already sent the recovery email, please wait patiently for the wallet to be recovered.</string>
  <string name="query_failed_and_retry">Data query failed, please try again.</string>
  <string name="try_again">Try Again</string>
  <string name="free_gas_fee">Free Gas Fee: </string>
  <string name="not_supporting_action">This AA wallet does not support the current operation, please switch your wallet.</string>
  <string name="since_the_aa_wallet_uses_your_email">Since the AA wallet uses your email as a recovery method, it\'s crucial to safeguard your private key and email.</string>
  <string name="manage_your_private_key_wallet_and_aa_wallet">Manage your private key wallet and AA wallet, please ensure to keep your wallets secure. Please note that one recovery phrase can generate multiple wallets with unique private keys.</string>
  <string name="biometrics_authentication">Biometrics authentication</string>
  <string name="active_wallet_failed">Active AA Wallet Failed</string>
  <string name="active_wallet_failed_reason">Due to an unstable network connection, the processing of the AA wallet activation failed. Please try again with your email.</string>
  <string name="send_recovery_email_alert_have_received">If you have already sent the recovery email, please wait patiently for the wallet to be recovered.<u>I have received the recovery email.</u></string>
  <string name="have_received_recovery_email"><u>I have received the recovery email.</u></string>
  <string name="contract_us_underline"><u>Need help? Contact us!</u></string>
  <string name="search">Search</string>
  <string name="address_is_empty">Address is empty</string>
  <string name="add_address">Add Address</string>
  <string name="manage_address_book">Manage Address Book</string>
  <string name="receiver">Receiver</string>
  <string name="apply_today_free_gas_fee_now">Apply Today‘s Free Gas Fee Now&gt;&gt;</string>
  <string name="around_value_symbol" formatted="false">≈ %s %s</string>
  <string name="not_support_aa_wallet_caption">The AA wallet is a type of contract wallet that does not support the current operation (personal sign related signature operations). Please try using other DApps or switch wallets.</string>
  <string name="content_cap">Content</string>
  <string name="the_process_of_activate_aa">The process of activating an AA wallet takes time, please be patient.</string>
  <string name="network_status">Network status:</string>
  <string name="not_available">Not Available</string>
  <string name="available">Available</string>
  <string name="iopay_aa_wallet">ioPay AA wallet</string>
  <string name="verify_recovery_email">Verify your recovery email</string>
  <string name="apply_gas_automatically">Apply Gas subsidy (%sIOTX) automatically</string>
  <string name="verified_failed">Verified failed.</string>
  <string name="applied_failed">Applied failed.</string>
  <string name="try_again_underline"><u>Try Again</u></string>
  <string name="create_your_aa_wallet">Create your AA wallet</string>
  <string name="create_failed">Create failed</string>
  <string name="bind_email_to_aa_wallet">Bind your email to your AA wallet</string>
  <string name="bind_failed">Bind failed</string>
  <string name="network_error_title">Network Error</string>
  <string name="network_error_tips">Due to an unstable network connection, the processing of the AA wallet activation may fail.</string>
  <string name="get_account">Get Account</string>
  <string name="wallet_address_only">Wallet Address</string>
  <string name="network_is_busy">Network is busy. Gas prices are high and estimates are less accurate.</string>
  <string name="balance_cap">Balance</string>
  <string name="amount_gas_fee">Amount + Gas Fee</string>
  <string name="insufficient_balance_for_this_action">Insufficient balance for this action.</string>
  <string name="two_string_add" formatted="false">%s %s</string>
  <string name="no_dot">No.</string>
  <string name="gas_subsidy">Gas Subsidy</string>
  <string name="advanced_mode">Advanced mode</string>
  <string name="data_to_chain">Data to chain</string>
  <string name="text_cap">Text</string>
  <string name="hex_big">HEX</string>
  <string name="the_data_will_be_written">The data will be written on blockchain and consume a certain fee</string>
  <string name="advanced">Advanced</string>
  <string name="edit_gas_fee">Edit Gas Fee</string>
  <string name="gas_option">Gas Option</string>
  <string name="sec">sec</string>
  <string name="base_fee">Base Fee</string>
  <string name="priority_fee">Priority Fee</string>
  <string name="advanced_gas_fee">Advanced Gas Fee</string>
  <string name="max_base_fee_with_unit">Max base fee (GWEI)</string>
  <string name="current">Current:</string>
  <string name="priority_fee_with_unit">Priority fee (GWEI)</string>
  <string name="save_selection">Save these values as my default for the Polygon network</string>
  <string name="aggressive">Aggressive</string>
  <string name="apply_gas_automatically_1">Apply Gas subsidy automatically</string>
  <string name="remaining_gas_subsidy">Remaining Gas Subsidy</string>
  <string name="bind_email_failed">Bind Recovery Email Failed</string>
  <string name="bind_email_failed_description">The gas fee may be too high and the binding cannot be successful at the moment, please try again later.</string>
  <string name="add_wallet_by_importing_recovery_phrase">Add wallet by importing recovery phrase</string>
  <string name="when_your_transaction_is_included_in_the_block">When your transaction is included in the block, any difference between your maximum base fee and the actual base fee will be refunded. The total amount is calculated in terms of the maximum base fee (in GWEI) * fuel limit.</string>
  <string name="priority_fee_goes_directly">Priority fee (aka “miner tip”) goes directly to miners and incentivizes them to prioritize your transaction.</string>
  <string name="transaction_sending">Transaction Sending</string>
  <string name="transaction_succeed">Transaction Succeed</string>
  <string name="transaction_fail">Transaction Failed</string>
  <string name="send_symbol">Send %s</string>
  <string name="value_symbol" formatted="false">-%s %s</string>
  <string name="to_address_format">To: %s</string>
  <string name="speed_up">Speed Up</string>
  <string name="this_gas_fee_will_replace_the_original">This gas fee will replace the original.</string>
  <string name="before">Before</string>
  <string name="after">After</string>
  <string name="gas_price_and_limit" formatted="false">= Gas price (%s Gwei) * Gas limit (%s)</string>
  <string name="cancel_transaction">Cancel Transaction</string>
  <string name="canceling_the_transaction">Canceling the transaction needs to spend additional gas fees.</string>
  <string name="due_to_block_reasons">Due to block reasons, the cancellation of the transaction does not guarantee success.</string>
  <string name="canceled">Canceled</string>
  <string name="speed_up_cancellation">Speed Up Cancellation</string>
  <string name="transaction_canceled">Transaction Canceled</string>
  <string name="please_note_buy_content_2">ioPay is not responsible for any problems that happen to your order during the purchase process. Please contact the third-party provider directly.</string>
  <string name="gas_price_and_limit_qev" formatted="false">= Gas price (%s Qev) * Gas limit (%s)</string>
  <string name="last_7_days">Last 7 days</string>
  <string name="save_these_values_as_my_default">Save these values as my default for the %s network</string>
  <string name="guide_content_6_v2">A private key is a secret number that is used in cryptography, similar to a password.  It is vitally important to prevent your private keys from being lost or compromised. Please note that  the AA wallet does not support exporting the private key.</string>
  <string name="lower_limit_2">%1$s must be greater than %2$s</string>
  <string name="transfer_bucket">Transfer Bucket</string>
  <string name="important_tips">Important Tips</string>
  <string name="this_address_only_accepts_native">· This address only accepts native IOTX and XRC20 tokens on IoTeX Network.</string>
  <string name="aa_wallet_operates_on_smart_contracts">· AA wallet operates on smart contracts;  kindly refrain from using this address to receive assets from Centralized Exchange.</string>
  <string name="tools">Tools</string>
  <string name="launch_app">Launch App</string>
  <string name="slow">Slow</string>
  <string name="slow_sec_or_longer">60 sec or longer</string>
  <string name="average">Average</string>
  <string name="average_sec_or_longer">30 sec or longer</string>
  <string name="fast">Fast</string>
  <string name="fast_sec_or_longer">10 sec or longer</string>
  <string name="customize">Customize</string>
  <string name="click_to_see_mnemonics">Click to see mnemonics</string>
  <string name="you_can_write_the_phrases_down">· You can write the phrases down in a piece of paper or save in a secure password manager.</string>
  <string name="don_not_email_them_or_screenshot_them">· Don’t email them or screenshot them.</string>
  <string name="the_order_of_words_is_important">· The order of words is important.</string>
  <string name="already_exists">Already Exists</string>
  <string name="switch_address">Switch Address</string>
  <string name="brc_20">BRC-20</string>
  <string name="stay_tuned_for_brc20_tokens">Stay tuned for BRC20 tokens.</string>
  <string name="transaction_time">%s sec or longer</string>
  <string name="start_exploring">Start exploring</string>
  <string name="created_wallet_success">Created wallet success!</string>
  <string name="import_wallet_success">Import wallet success!</string>
  <string name="import_watch_address_success">Import Watch Address Success！</string>
  <string name="choosing_a_language">Choosing a language:</string>
  <string name="basic_functions">Basic Functions</string>
  <string name="iopay_settings">ioPay Settings</string>
  <string name="address_books">Address Books</string>
  <string name="networks">Networks</string>
  <string name="go_now">Go Now</string>
  <string name="iopay_now_supports_evm_and_btc_private_key">ioPay now supports EVM and BTC Private Key</string>
  <string name="iopay_now_supports_evm_and_btc_recovery_phrase">ioPay now supports EVM and BTC Recovery Phrase</string>
  <string name="the_watch_mode_not_support_switch_between_evm_and_btc_networks">The Watch mode does not support switch between EVM and BTC networks.</string>
  <string name="enter_dapp_name_or_url">Enter DApp name or URL</string>
  <string name="backup_your_wallet_set_up_your_wallet_network">Backup your wallet, set up your wallet network, explore your ioPay basic settings, including wallet management, address management, security and privacy, etc.</string>
  <string name="input_the_path">Input the Path</string>
  <string name="path_invalid">The path is invalid</string>
  <string name="customize_path_desc">Did not find the path? Try customize path</string>
  <string name="btc_wallet_tips_1">• This address only accepts BTC on Bitcoin Network.</string>
  <string name="btc_wallet_tips_2">• Please check the BTC address type before transaction.</string>
  <string name="dapp_not_support_bitcoin_network">This project not support the Bitcoin network, please switch your network.</string>
  <string name="note">Note</string>
  <string name="the_current_network_does_not_support_swap">The current network does not support swap, please switch the network and then use it.</string>
  <string name="the_current_network_is_not_bitcoin">The current network isn\'t Bitcoin Network, please switch the network and then use it.</string>
  <string name="wif_private_key">WIF Private Key</string>
  <string name="hex_private_key">HEX Private Key</string>
  <string name="note_unsuported_feature">This feature isn\'t supported yet, please stay tuned.</string>
  <string name="supports_bitcoin_beta">ioPay Now Supports Bitcoin!</string>
  <string name="supports_bitcoin_beta_desc">The current Bitcoin functionality is still in beta, and BRC-20 related features are under development.</string>
  <string name="explore">Explore</string>
  <string name="authorized_network">Authorized network</string>
  <string name="this_site_is_unsafe">This site is unsafe, if you continue, you could lose all your assets.</string>
  <string name="after_authorization">After authorization, the app will get the permissions to</string>
  <string name="access_your_information">• Access your information on the network</string>
  <string name="ask_for_your_permission">• Ask for your permission to make a transaction</string>
  <string name="note_dot">Note:</string>
  <string name="your_private_key_will_not_be_shared">• Your private key will not be shared as a result of this authorization.</string>
  <string name="checking_for_updates">Checking for updates</string>
  <string name="iopay_is_already_the_latest_version">ioPay is already the latest version</string>
  <string name="update_now_cap">Update Now</string>
  <string name="update_your_iopay">Update Your ioPay</string>
  <string name="wallet_is_connecting">Wallet is Connecting</string>
  <string name="network_failed">Network Failed</string>
  <string name="there_appears_to_be_an_issue_with_the_network">There appears to be an issue with the network you are connected to. Please check your network.</string>
  <string name="list_is_empty">List is empty</string>
  <string name="you_are_currently_offline">You\'re currently offline.</string>
  <string name="connection_to_the_blockchain_host_is_unavailable">Connection to the blockchain host is unavailable.</string>
  <string name="retry">Retry</string>
  <string name="gas_fee_too_low">The gas price is too low for transactions, please edit gas price and try again.</string>
  <string name="all_networks">All networks</string>
  <string name="switch_network_to_see_network_asset_details"><u>Switch network to see network asset details.</u></string>
  <string name="ethereum">Ethereum</string>
  <string name="btc">BTC</string>
  <string name="add_a_wallet">Add a wallet</string>
  <string name="create_wallet">Create wallet</string>
  <string name="import_wallet">Import wallet</string>
  <string name="create_wallet_with_private_key">Create wallet with private key</string>
  <string name="create_new_wallet_with_mnemonic_phrase">Create new wallet with mnemonic phrase</string>
  <string name="create_a_new_wallet_and_backup_private_key">Create a new wallet and backup private key.</string>
  <string name="add_wallet_cap">Add wallet</string>
  <string name="your_private_key">Your private key</string>
  <string name="verify_private_key">Verify private key</string>
  <string name="keep_the_private_key_only_to_yourself">Keep the private key only to yourself. Write it down on a note and keep it safe. Please make sure your have saved it.</string>
  <string name="view_wallet_cap">View Wallet</string>
  <string name="click_to_see_private_key">Click to see private key</string>
  <string name="swap_from">Swap From</string>
  <string name="minimum_receive">Minimum Receive</string>
  <string name="you_send_only">You Send</string>
  <string name="nodes_settings">Nodes Settings</string>
  <string name="chain_id_dot">Chain ID:</string>
  <string name="default_node">Default node</string>
  <string name="custom_node">Custom node</string>
  <string name="polygon">Polygon</string>
  <string name="validate_wallet_exist">This wallet already exists. Please try a different wallet!</string>
  <string name="iopay_now_supports_evm_solana_and_btc_private_key">ioPay now supports EVM, Solana and BTC Private Key(WIF/Hex)</string>
  <string name="iopay_now_supports_evm_solana_and_btc_recovery_phrase">ioPay now supports EVM, Solana and BTC Recovery Phrase</string>
  <string name="the_watch_mode_not_support_switch_between_evm_solana_and_btc_networks">The Watch mode does not support switch between EVM, Solana and BTC networks.</string>
  <string name="stake_migrate">Migrate</string>
  <string name="Okay">Okay</string>
  <string name="appearance">Appearance</string>
  <string name="adjust_your_iopay_appearance">Adjust your ioPay appearance</string>
  <string name="system">System</string>
  <string name="we_adjust_your_appearance">We\'ll adjust your appearance based on your device\'s system settings.</string>
  <string name="light">Light</string>
  <string name="dark">Dark</string>
  <string name="iopay_now_supports_solana">ioPay now supports Solana</string>
  <string name="import_your_mnemonic_phrase_or_solana_private_key">Import your mnemonic phrase or Solana private key, and seamlessly switch to the Solana network to fully access and utilize its features.</string>
  <string name="stake_method_edit">Edit</string>
  <string name="dapp_not_support_current_network">This project not support the current network, please switch your network.</string>
  <string name="if_the_wallets_you_are_expecting_are_not_displayed">If the wallets you\'re expecting aren\'t displayed, try switching the HD Path.</string>
  <string name="switch_now">Switch Now</string>
  <string name="wallet_not_support_current_network">This wallet doesn\'t support the current network. Please switch your wallet and network. When switching wallets, it will automatically connect to the corresponding network.</string>
  <string name="ucam_login_currently_only_supports_iotex">Ucam login currently only supports IoTeX wallets (Solana and AA wallets are not supported).</string>
  <string name="tips">Tips</string>
  <string name="no_nfts_yet">No NFTs yet</string>
  <string name="login_with_iopay">Login with ioPay</string>
  <string name="go_to_nft_marketplace">Go to NFT Marketplace</string>
  <string name="create_or_import_your_solana_wallet">Create or import your Solana Wallet (mnemonic phrase/private key), and seamlessly switch to the Solana network to fully access and utilize its features.</string>
  <string name="add_solana_wallet">Add Solana Wallet</string>
  <string name="you_are_invited_to_iopay">You Are Invited to ioPay</string>
  <string name="referral_code">Referral Code</string>
  <string name="input_your_referral_code">Input Your Referral Code</string>
  <string name="explore_iopay">Explore ioPay</string>
  <string name="your_referral_code">Your Referral Code</string>
  <string name="gift_center">Gift Center</string>
  <string name="invite_your_friends_to_iopay">Invite Your Friends to ioPay</string>
  <string name="referral_link">Referral Link</string>
  <string name="invite_friends_to_earn_points">Invite Friends to Earn Points</string>
  <string name="invited_users_must_enter_the_referral_code">• Invited users must enter the referral code after their first download for it to be valid.</string>
  <string name="invitations_are_limited_to_a_single_device">• Invitations are limited to a single device; multiple downloads or wallet creations on the same device will be considered invalid.</string>
  <string name="iopay_reserves_the_right_to_interpret_the_rules">• ioPay reserves the right to interpret the rules.</string>
  <string name="join_iopay_and_earn_rewards">Join ioPay and earn rewards!</string>
  <string name="you_have_completed_task_and_earn">Congratulations! You have completed task and earn %s points!</string>
  <string name="the_current_wallet_is_not_support">The current wallet is not support to execute this action.</string>
  <string name="enable_certain_rewards_and_functionality">Enable certain rewards and functionality in Web3 DApps by providing a proof of your trusted GPS location.</string>
  <string name="please_switch_our_network_to_iotex">Please switch your network to IoTeX</string>
  <string name="dex_screener">DEX SCREENER</string>
  <string name="please_add_the_network_with_chain">Please add the network with Chain ID %s in your wallet before connecting to this DApp.</string>
  <string name="how_to_receive_from_other_wallets">How to receive from other wallets?</string>
  <string name="how_to_receive_from_exchanges">How to receive from exchanges?</string>
  <string name="steps_in_another_wallet_app">Steps (in another wallet\'s app)</string>
  <string name="select_send_or_transfer_in_some_wallets">Select Send (or Transfer in some wallets).</string>
  <string name="select_iotx_on_iotex_mainnet_to_send">Select IOTX(on IoTeX Mainnet) to send.</string>
  <string name="enter_your_iopay_wallet_address_as_the_recipient">Enter your ioPay wallet address as the recipient or scan the wallet QR code that appears in the ioPay app.</string>
  <string name="select_the_network_that_matches_your_iopay_wallet_address">Select the network that matches your ioPay wallet address.</string>
  <string name="iotex_network_mainnet">IoTeX Network Mainnet</string>
  <string name="enter_amount_confirm_send_and_find_the_token">Enter amount, confirm send and find the token in your ioPay wallet.</string>
  <string name="steps_in_the_exchange_app">Steps (in the exchange app)</string>
  <string name="select_iotx_to_withdraw">Select IOTX to withdraw.</string>
  <string name="enter_amount_confirm_withdrawal">Enter amount, confirm withdrawal and find the token in your ioPay wallet.</string>
  <string name="receive_iotx_from_exchanges">Receive IOTX from exchanges:</string>
  <string name="are_you_sure_you_want_to_delete">Are you sure you want to delete?</string>
  <string name="go_back_to_the_dapp_and_proceed">Go back to the DApp and proceed.</string>
  <string name="for_your_assets_security">For your assets security, please backup your private key and Mnemonic phrases.</string>
  <string name="edit_stake">Edit Stake</string>
  <string name="receive_sol_on_solana">• This address only accepts SOL on Solana Network.</string>
  <string name="background_location">ioPay needs to apply for a background location.</string>
  <string name="geo_will_upload_location_data_to_the_blockchain">Geo will upload location data to the blockchain to enable DePIN-related functionalities. Even when the app is running in the background, users can continue uploading their location data. Of course, users also have the option to disable background location tracking and uploads via the Geo settings page.</string>
  <string name="disconnect_all">Disconnect all</string>
  <string name="authorized_network_dot">Authorized network:</string>
  <string name="connected_dapp">Connected DApp</string>
  <string name="due_to_the_volatility">Due to the volatility of the market, please DYOR.</string>
  <string name="add_liquidity">Add Liquidity</string>
  <string name="iopay_supports_the_default_bip44_standard">ioPay supports the default BIP44 Standard (IOTEX HD PATH: 304 )/(EVM HD PATH:60)/(BTC PATH)/(SOLANA PATH) and custom path, learn more.</string>
  <string name="sec_or_longer">sec or longer</string>
  <string name="hd_path">HD Path</string>
  <string name="bino_ai">Bino AI</string>
  <string name="stake_iotx_and_earn_rewards">Stake IOTX and Earn Rewards</string>
  <string name="the_staking_interest_rate">With IoTeX, the staking interest rate is up to %s APR.</string>
  <string name="market_details">Market details</string>
  <string name="market_value">Market value</string>
  <string name="billion">Billion</string>
  <string name="million">Million</string>
  <string name="total_transaction_volume">Total Transaction volume (24 hours)</string>
  <string name="turnover_market_value">Turnover/market value</string>
  <string name="circular_supply">Circular supply</string>
  <string name="all_time_high">All-time high</string>
  <string name="record_low">Record low</string>
  <string name="fully_diluted">Fully diluted</string>
  <string name="hide_assets_1_usd">Hide assets &lt;1 USD</string>
  <string name="would_you_like_to_import_these_tokens">Would you like to import these tokens to token list on the homepage?</string>
  <string name="please_update_the_iopay">Please update the ioPay to the latest version to continue.</string>
  <string name="search_for_wallet_name_or_address">Search for wallet name or address.</string>
  <string name="aa_wallet_operates_on_smart_contracts_only">AA Wallet operates on smart contracts, kindly refrain from using this address to receive assets from Centralized Exchange.</string>
  <string name="the_address_is_copied_to_the_clipboard">The address is copied to the clipboard</string>
  <string name="this_is_not_an_address_you_interacted">This is not an address you\'ve interacted with recently. Please proceed with caution.</string>
  <string name="price_impact_is_too_high">Price impact is too high. You will lose a big portion of your funds in this trade.</string>
  <string name="enter_a_amount">Enter an amount</string>
  <string name="select_token">Select Token</string>
  <string name="slippage_tolerance">Slippage tolerance</string>
  <string name="price">Price</string>
  <string name="minimum_received">Minimum received</string>
  <string name="maximum_pay">Maximum Pay</string>
  <string name="price_impact">Price impact</string>
  <string name="fee">Fee</string>
  <string name="route">Route</string>
  <string name="mimo_smart_router">Mimo smart router</string>
  <string name="mimo_smart_router_cap">Mimo Smart Router</string>
  <string name="best_trade">Best Trade</string>
  <string name="point_of_slip">Point of slip</string>
  <string name="slippage_is_when_the_transaction_price">Slippage is when the transaction price is different from the expected price at the time of placing the order, usually due to the price change from the time of placing the order to the time of closing. If the price changes beyond the slip point setting, the transaction will be canceled, but the fuel fee will still be charged on the chain.</string>
  <string name="smart_router">Smart Router</string>
  <string name="when_available_aggregate_v2_v3">When available, aggregate v2,v3 and query for better prices, and when unavailable, only query prices from mimo v2.</string>
  <string name="set_slip_point">Set slip point</string>
  <string name="optimal_slip_point">Optimal slip point</string>
  <string name="customization">Customization</string>
  <string name="minimum_acquired_quantity">Minimum acquired quantity</string>
  <string name="optimal_slip_point_present">Optimal slip point (%s) </string>
  <string name="according_to_the_current_trading">According to the current trading currency recommended suitable slip point value, to help you trade successfully.</string>
  <string name="search_name_or_paste_address">Search Name Or Paste Address</string>
  <string name="selection_network_dot">Selection network:</string>
  <string name="insufficient_balance">Insufficient balance</string>
  <string name="please_switch_your_vpn">Please switch your VPN node or Network and try again</string>
  <string name="iopay_binoai">ioPay BinoAI</string>
  <string name="may_be_due_to_price_fluctuation">May be due to price fluctuation, please try to increase slippage percentage</string>
  <string name="network_error_please_try_again">Network error please try again</string>
  <string name="one_day">1day</string>
  <string name="one_week">1week</string>
  <string name="one_month">1month</string>
  <string name="one_year">1year</string>
  <string name="swap_to">Swap To</string>
  <string name="ignore_this_version">Ignore this version</string>
  <string name="crate_new_wallet_for_the_network">Crate new wallet for the network</string>
  <string name="wallet_not_support_current_network_2">This wallet doesn\'t support the current network. Please switch your wallet and network. When switching wallet or network, it will automatically connect to the corresponding network or wallet.</string>
  <string name="to_trade_stocks_please_switch_to_the_solana_network">To trade stocks, please switch to the Solana network.</string>
  <string name="please_switch_wallets_or_create_a_new_wallet" formatted="false">Your current wallet doesn\'t support %s network. Please switch wallets or create a new %s wallet.</string>
  <string name="user_feedback">User Feedback</string>
  <string name="your_feedback">Your Feedback</string>
  <string name="contact_email">Contact Email</string>
  <string name="enter_email_address">Enter email address</string>
  <string name="find_us">Find us:</string>
  <string name="problem_picture_caption">Problem Picture Caption</string>
  <string name="thanks_for_your_feedback">Thanks for your feedback!</string>
  <string name="exact_out_feature_currently_not_support_solana">Exact Out feature currently not support Solana</string>
  <string name="your_stake">Your Stake</string>
  <string name="stake_with_iotex">Stake with iotex</string>
  <string name="per_year">per year</string>
  <string name="learn_more_cap">Learn More</string>
  <string name="start_earning">Start Earning</string>
  <string name="iotex_staked_iotx">iotex Staked IOTX</string>
  <string name="secure_chain">Secure Chain</string>
  <string name="by_staking_token_holders_enhance">By staking, token holders enhance the security and efficiency of the loTeX Network.</string>
  <string name="earn_rewards">Earn Rewards</string>
  <string name="with_lotex_the_staking">With loTeX, the staking interest rate is up to 6–11% APR.</string>
  <string name="vote">Vote</string>
  <string name="any_token_holder_that_stakes">Any token-holder that stakes IOTX can vote for one or more delegates they trust to mine blocks.</string>
  <string name="past_performance_does_not_guarantee">· Past performance does not guarantee future results.</string>
  <string name="the_estimated_apy_is_partially_derived">· The estimated APY is partially derived from network inflation rates, which are subject to change and beyond ioPay\\'s control. Actual returns may fluctuate over time.</string>
</resources>
