package com.solana.api

import com.solana.core.PublicKey
import com.solana.networking.RpcRequest
import com.solana.networking.makeRequestResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.add
import kotlinx.serialization.json.addJsonObject
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.put

class GetTokenAccountsByOwnerRequest(account: PublicKey) : RpcRequest() {
    override val method: String = "getTokenAccountsByOwner"
    override val params = buildJsonArray {
        add(account.toString())
        addJsonObject {
            put("programId", "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA")
        }
        addJsonObject {
            put("encoding", "jsonParsed")
        }
    }
}

@Serializable
data class TokenAccountsByOwnerResponse(
    val value: ArrayList<TokenAccountsByOwnerValue>?,
)

@Serializable
data class TokenAccountsByOwnerValue(
    val account: TokenAccountsByOwnerAccount?,
    val pubkey:String
)

@Serializable
data class TokenAccountsByOwnerAccount(
    val data: TokenAccountsByOwnerData?,
)

@Serializable
data class TokenAccountsByOwnerData(
    val parsed: TokenAccountsByOwnerParsed?,
    val program: String?,
)

@Serializable
data class TokenAccountsByOwnerParsed(
    val info: TokenAccountsByOwnerInfo?,
)

@Serializable
data class TokenAccountsByOwnerInfo(
    val mint: String?,
    val tokenAmount: TokenAccountsByOwnerTokenAmount?,
)

@Serializable
data class TokenAccountsByOwnerTokenAmount(
    val amount: String?,
    val decimals: Long?,
    val uiAmount: Float?,
)

internal fun GetTokenAccountsByOwnerSerializer() = TokenAccountsByOwnerResponse.serializer()

suspend fun Api.getTokenAccountsByOwner(
    tokenAccount: PublicKey
): Result<TokenAccountsByOwnerResponse> =
    router.makeRequestResult(
        GetTokenAccountsByOwnerRequest(tokenAccount),
        GetTokenAccountsByOwnerSerializer()
    )
        .let { result ->
            @Suppress("UNCHECKED_CAST")
            if (result.isSuccess && result.getOrNull() == null)
                Result.failure(Error("Can not be null"))
            else result as Result<TokenAccountsByOwnerResponse> // safe cast, null case handled above
        }

fun Api.getTokenAccountsByOwner(
    tokenAccount: PublicKey,
    onComplete: (Result<TokenAccountsByOwnerResponse>) -> Unit
) {

    CoroutineScope(dispatcher).launch {
        onComplete(getTokenAccountsByOwner(tokenAccount))
    }
}